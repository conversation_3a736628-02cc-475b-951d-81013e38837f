import React from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";

type typePropPaper = {
  title?: string;
  text1?: string;
  text2?: string;
  text3?: string;
  text4?: string;
  level?: number;
  score?: number;
  onPress?: any;
  date?: any;
  badgeShow?: boolean;
};

const PaperCompany = ({
  title,
  text1,
  text2,
  text3,
  text4,
  level,
  onPress,
  date,
  score,
  badgeShow = true,
}: typePropPaper) => {
  function formatDate(dateString: string) {
    const date = new Date(dateString);
    return date.toLocaleDateString("fr-FR");
  }

  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title}>
            {title && (title.length > 80 ? title.slice(0, 80) + "..." : title)}
          </Text>
          {date && (
            <Text style={styles.date}>{formatDate(date)}</Text>
          )}
        </View>

        <View style={styles.textContainer}>
          {text1 && (
            <Text style={styles.text}>
              {text1.length > 100 ? text1.slice(0, 100) + "..." : text1}
            </Text>
          )}
          {text2 && (
            <Text style={styles.text}>
              {text2.length > 100 ? text2.slice(0, 100) + "..." : text2}
            </Text>
          )}
          {text3 && (
            <Text style={styles.text}>
              {text3.length > 100 ? text3.slice(0, 100) + "..." : text3}
            </Text>
          )}
          {text4 && (
            <Text style={styles.text}>
              {text4.length > 100 ? text4.slice(0, 100) + "..." : text4}
            </Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: "100%",
    backgroundColor: "white",
    borderRadius: 8,
    marginBottom: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1C3144",
    flex: 1,
  },
  date: {
    fontSize: 14,
    color: "#666",
    marginLeft: 8,
  },
  textContainer: {
    gap: 8,
  },
  text: {
    fontSize: 14,
    color: "#525252",
    lineHeight: 20,
  },
});

export default PaperCompany;
