/*
app/screens/observations/DetailObservation.tsx

Page de détails d'un constat.

Informations pertinentes :

- Les informations du constat sont récupérées depuis les paramètres de navigation
- On affiche les détails du constat (nom, type, exigence, risque, processus)
- Un bouton permet d'accéder à la page de modification du constat si l'audit n'est pas conclu
*/

import React, { useContext, useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
} from "react-native";
import {
  ParamListBase,
  RouteProp,
  useNavigation,
  useRoute,
  useFocusEffect,
} from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AppContext } from "@/state/AppContext";
import Header from "@/components/common/Header";
import moment from "moment";
import { supabase } from "@/lib/supabase";

// Données de navigation
type DetailObservationRouteParams = {
  audit: {
    id: number;
    name: string;
    type: string;
    field: string;
    date: string;
    conclusion: string;
  };
  observation: {
    id: number;
    name: string;
    type: string;
    requirements: string;
    risk: string;
    process_id: number;
    created_at: string;
  };
};

// Gestion des types
const getObservationTypeStyle = (type: string) => {
  switch (type) {
    case "sensitive_point":
      return { text: "Point sensible", backgroundColor: "#2FC12B" };
    case "non_compliant":
      return { text: "Non conforme", backgroundColor: "#F93C27" };
    case "note":
      return { text: "Note", backgroundColor: "#CACACA" };
    default:
      return { text: "Piste de progrès", backgroundColor: "#277FF9" }; // Valeur par défaut
  }
};

export default function DetailObservation() {
  const route =
    useRoute<RouteProp<{ params: DetailObservationRouteParams }, "params">>();
  const { audit, observation: initialObservation } = route.params;
  const [observation, setObservation] = useState(initialObservation);
  const [processName, setProcessName] = useState<string>("");
  const { user } = useContext(AppContext);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  // Rafraîchir les données du constat lorsqu'on revient sur la page
  useFocusEffect(
    React.useCallback(() => {
      const fetchObservationDetails = async () => {
        try {
          // Récupérer les détails du constat
          const { data, error } = await supabase
            .from("observations")
            .select("*")
            .eq("id", initialObservation.id)
            .single();

          if (error) {
            console.error("Erreur lors de la récupération du constat :", error);
            return;
          }

          if (data) {
            setObservation(data);
            
            // Récupérer le nom du processus
            if (data.process_id) {
              const { data: processData, error: processError } = await supabase
                .from("process")
                .select("process_name")
                .eq("id", data.process_id)
                .single();
                
              if (!processError && processData) {
                setProcessName(processData.process_name);
              }
            }
          }
        } catch (err) {
          console.error("Erreur inattendue :", err);
        }
      };

      fetchObservationDetails();
    }, [initialObservation.id])
  );

  // Fonction pour naviguer vers la page de modification
  const handleEditObservation = () => {
    // Vérifier si l'audit est conclu
    if (audit?.conclusion && audit.conclusion !== "Non renseignée") {
      Alert.alert(
        "Audit clôturé",
        "Cet audit est clôturé, vous ne pouvez pas modifier ses informations."
      );
      return;
    }

    // Si l'audit n'est pas conclu, naviguer vers la page de modification
    navigation.navigate("screens/observations/UpdateObservation", {
      audit,
      observation,
    });
  };

  const { text, backgroundColor } = getObservationTypeStyle(observation.type);

  return (
    <>
      <Header
        onPressIcon={() => handleEditObservation()}
      />
      <ScrollView style={styles.container}>
        {/* Informations de l'audit */}
        <View style={styles.auditCard}>
          <Text style={styles.cardName}>{audit?.name}</Text>
          <Text style={styles.date}>{moment(audit?.date).format("DD/MM/YYYY")}</Text>
        </View>

        {/* Informations du constat */}
        <View style={styles.observationCard}>
          <View style={styles.observationHeader}>
            <Text style={styles.observationTitle}>{observation.name}</Text>
            <Text style={styles.date}>{moment(observation.created_at).format("DD/MM/YYYY")}</Text>
          </View>

          <View style={styles.typeContainer}>
            <Text style={[styles.typeText, { backgroundColor }]}>{text}</Text>
          </View>

          <View style={styles.detailSection}>
            <Text style={styles.detailLabel}>Exigence concernée :</Text>
            <Text style={styles.detailText}>{observation.requirements}</Text>
          </View>

          <View style={styles.detailSection}>
            <Text style={styles.detailLabel}>Risque :</Text>
            <Text style={styles.detailText}>{observation.risk}</Text>
          </View>

          <View style={styles.detailSection}>
            <Text style={styles.detailLabel}>Processus :</Text>
            <Text style={styles.detailText}>{processName || "Non spécifié"}</Text>
          </View>
        </View>

        {/* Bouton de modification */}
        <TouchableOpacity
          style={[
            styles.editButton,
            audit?.conclusion && audit.conclusion !== "Non renseignée"
              ? styles.disabledButton
              : {},
          ]}
          onPress={handleEditObservation}
          disabled={!!audit?.conclusion && audit.conclusion !== "Non renseignée"}
        >
          <Text style={styles.editButtonText}>Modifier le constat</Text>
        </TouchableOpacity>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: "#F5F5F5",
  },
  auditCard: {
    backgroundColor: "#CCC",
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  cardName: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#1C3144",
  },
  date: {
    fontSize: 14,
    color: "#525252",
  },
  observationCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  observationHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 15,
  },
  observationTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1C3144",
    flex: 1,
  },
  typeContainer: {
    marginBottom: 15,
    alignItems: "flex-start",
  },
  typeText: {
    fontSize: 12,
    color: "#FFFFFF",
    padding: 8,
    borderRadius: 50,
    textAlign: "center",
    minWidth: 100,
  },
  detailSection: {
    marginBottom: 15,
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#1C3144",
    marginBottom: 5,
  },
  detailText: {
    fontSize: 14,
    color: "#525252",
  },
  editButton: {
    backgroundColor: "#F99527",
    borderRadius: 8,
    padding: 15,
    alignItems: "center",
    marginTop: 10,
  },
  editButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "bold",
  },
  disabledButton: {
    backgroundColor: "#CCCCCC",
  },
}); 