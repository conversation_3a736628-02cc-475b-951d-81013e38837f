/*
app/screens/documents/DocumentDetails.tsx

Page de détails d'un document.

Informations pertinentes :

- Les informations du document sont récupérés depuis la navigation du composant `DocumentList.tsx`
*/

import {
  Image,
  Keyboard,
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
  Linking,
  Alert,
  ScrollView,
  Platform
} from "react-native";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { useRoute, RouteProp } from "@react-navigation/native";
import React, { useEffect, useState } from "react";
import Header from "@/components/common/Header";
import { useRouter } from "expo-router";
import { supabase } from "@/lib/supabase";

type DocumentDetailsRouteParams = {
  document: {
    id: number;
    name: string;
    description: string;
    file: string;
    activity: string;
    companyId: number;
  };
};

export default function DocumentDetails() {
  const route =
    useRoute<RouteProp<{ params: DocumentDetailsRouteParams }, "params">>();
  const { document: initialDocument } = route.params;
  const [document, setDocument] = useState(initialDocument);
  const [activityName, setActivityName] = useState<string>("");
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const router = useRouter();

  // Rafraîchir les données du document et récupérer le nom de l'activité
  useEffect(() => {
    const fetchUpdatedDocument = async () => {
      try {
        // Récupérer le document mis à jour
        const { data: documentData, error: documentError } = await supabase
          .from("documents")
          .select("*")
          .eq("id", document.id)
          .single();

        if (documentError) throw documentError;
        if (documentData) {
          setDocument(documentData);
          
          // Récupérer le nom de l'activité
          const { data: activityData, error: activityError } = await supabase
            .from("activities")
            .select("activity_name")
            .eq("id", documentData.activity)
            .single();

          if (activityError) throw activityError;
          if (activityData) {
            setActivityName(activityData.activity_name);
          }
        }
      } catch (error) {
        console.error("Erreur lors de la récupération des données:", error);
      }
    };

    // S'abonner aux changements de focus pour rafraîchir les données
    const unsubscribe = navigation.addListener('focus', () => {
      fetchUpdatedDocument();
    });

    return unsubscribe;
  }, [document.id, navigation]);

  return (
    <>
      <Header
        onPressIcon={() =>
          Platform.OS === 'web'
            ? router.push({
                pathname: "/screens/documents/UpdateDocument",
                params: { document: JSON.stringify(document) }
              })
            : navigation.navigate("screens/documents/UpdateDocument", { document })
        }
      />
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <ScrollView
          style={{
            flex: 1,
            paddingHorizontal: Platform.OS === "web" ? 0 : 30,
            marginBottom: Platform.OS === "web" ? 0 : 200,
          }}
        >
          <View style={Platform.OS === "web" ? { flex: 1, alignItems: "center" } : { flex: 1 }}>
            <View style={Platform.OS === "web" ? { width: "100%", maxWidth: 800 } : { width: "100%" }}>
              {/* Informations du document */}
              <View style={styles.card}>
                {document ? (
                  <View style={styles.insideCard}>
                    {/* <Text style={styles.detailTitle}>Document</Text> */}
                    <Text style={styles.detailName}>{document.name}</Text>
                    {Platform.OS === "web" ? (
                      document.file && document.file.endsWith(".pdf") ? (
                        // ✅ Afficher PDF uniquement sur Web
                        <iframe
                          src={document.file}
                          style={{
                            width: "100%",
                            height: "500px",
                            border: "none",
                          }}
                        />
                      ) : document.file ? (
                        // ✅ Afficher l'image sur Web
                        <Image
                          source={{ uri: document.file }}
                          style={styles.detailImage}
                        />
                      ) : (
                        <Text style={styles.noFileText}>Aucun fichier disponible</Text>
                      )
                    ) : (
                      // ✅ Afficher l'image sur Mobile (qui peut aussi être un aperçu de PDF)
                      <TouchableOpacity
                        style={styles.imageTouchable}
                        onPress={() => {
                          if (document.file) {
                            Linking.openURL(document.file);
                          } else {
                            Alert.alert("Erreur", "Aucun fichier disponible pour ce document.");
                          }
                        }}
                      >
                        {document.file ? (
                          <Image
                            source={{ uri: document.file }}
                            style={styles.detailImage}
                          />
                        ) : (
                          <Text style={styles.noFileText}>Aucun fichier disponible</Text>
                        )}
                      </TouchableOpacity>
                    )}
                    <Text style={styles.detailTitle}>Description</Text>
                    <Text style={styles.listDescription2}>
                      {document.description}
                    </Text>
                    <Text style={styles.detailTitle}>Activité</Text>
                    <Text style={styles.listDescription2}>{activityName}</Text>
                  </View>
                ) : (
                  <Text>Les détails du document ne sont pas disponibles.</Text>
                )}
              </View>

              {/* Ouvrir le document */}
              {document.file && (
                <View>
                  <TouchableOpacity
                    style={styles.opendocumentButton}
                    onPress={() => {
                      if (document.file) {
                        Linking.openURL(document.file);
                      }
                    }}>
                    <Text style={styles.opendocumentButtonText}>Ouvrir le document</Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </View>
        </ScrollView>
      </TouchableWithoutFeedback>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 20,
    marginBottom: 500
  },
  headSection: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  backButton: {
    width: 24,
    textAlign: "left",
  },
  titleWrapper: {
    display: "flex",
    flexDirection: "row",
    marginVertical: 15,
  },
  title: {
    color: "#262627",
    fontSize: 17,
    textAlign: "left",
  },
  card: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    alignContent: "center",
    padding: 5,
    borderRadius: 8,
    marginVertical: 20,
    shadowColor: "#CCC",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.9,
    shadowRadius: 2,
    elevation: 1,
    rowGap: 5,
  },
  insideCard: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "flex-start",
    rowGap: 5,
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  detailImage: {
    width: "100%",
    height: 230,
    borderRadius: 10,
    marginBottom: 20,
  },
  imageTouchable: {
    width: "100%",
  },
  detailTitle: {
    fontSize: 17,
    fontWeight: "bold",
    color: "#1C3144",
    marginBottom: 10,
  },
  detailName: {
    fontSize: 17,
    fontWeight: "bold",
    color: "#1C3144",
    marginBottom: 10,
  },
  detailDescription: {
    fontSize: 16,
    color: "#525252",
    marginBottom: 5,
  },
  listDescription2: {
    // width: "70%",
    fontSize: 14,
    color: "#1c3144",
    fontStyle: "normal",
    flexWrap: "wrap",
  },
  opendocumentButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    marginBottom: 200,
    backgroundColor: "#1C3144",
    marginTop: 10,
  },
  opendocumentButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  noFileText: {
    fontSize: 14,
    color: "#666",
    fontStyle: "italic",
    textAlign: "center",
    padding: 20,
  },
});
