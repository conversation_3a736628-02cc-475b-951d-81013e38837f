import ContainedButton from "@/components/ui/buttons/ContainedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useRouter, useLocalSearchParams } from "expo-router";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  Alert,
} from "react-native";

import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import typeUserByAdmin from "@/types/typeUserByAdmin";
import CustomizedSelect from "@/components/ui/customizedSelect/customizedSelect";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";

export default function UpdateUser() {
  const [loading, setLoading] = useState(false);
  const [userDetails, setUserDetails] = useState<typeUserByAdmin | null>(null);
  const { user } = useContext(AppContext);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const router = useRouter();
  const { id } = useLocalSearchParams();

  const inputs = [
    {
      label: "Prénom",
      name: "first_name",
      placeholder: "Ecrire le prénom",
    },
    {
      label: "Nom",
      name: "last_name",
      placeholder: "Ecrire le nom",
    },
    {
      label: "Rôle",
      name: "profil",
      placeholder: "Sélectionner le rôle",
      type: "Select",
      data: [
        { label: "Auditeur", value: "Auditeur" },
        { label: "Fournisseur", value: "Fournisseur" },
        { label: "Client", value: "Client" },
        { label: "Collaborateur", value: "Collaborateur" },
        { label: "Responsable QSE", value: "Responsable QSE" },
        { label: "Pilote de processus", value: "Pilote de Processus" },
        { label: "Responsable QSE Adjoint", value: "Responsable QSE Adjoint" },
        { label: "Responsable Achat", value: "Responsable achat" },
        { label: "Responsable Maintenance", value: "Responsable maintenance" },
      ],
    },
  ];

  useEffect(() => {
    const fetchUser = async () => {
      if (!id) return;
      
      const { data, error } = await supabase
        .from("users")
        .select("*")
        .eq("id", id)
        .single();

      if (error) {
        console.error("Erreur lors de la récupération de l'utilisateur:", error);
        return;
      }

      setUserDetails(data);
      reset({
        first_name: data.first_name,
        last_name: data.last_name,
        profil: data.profil || "User",
      });
    };

    fetchUser();
  }, [id]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
    control,
    reset,
  } = useForm<typeUserByAdmin>({
    defaultValues: {
      first_name: "",
      last_name: "",
      profil: "",
    },
  });

  const updateUser = async (data: typeUserByAdmin) => {
    if (!user || !id) {
      console.log("Utilisateur non connecté ou ID manquant.");
      return;
    }

    if (!data.first_name || !data.last_name || !data.profil) {
      Toast.show({
        type: "error",
        text1: "Tous les champs sont obligatoires",
        text1Style: { color: "#1C3144" },
      });
      return;
    }

    setLoading(true);

    try {
      const { error } = await supabase
        .from("users")
        .update({
          first_name: data.first_name,
          last_name: data.last_name,
          profil: data.profil,
        })
        .eq("id", id);

      if (error) {
        console.error("Erreur lors de la mise à jour:", error);
        Toast.show({
          type: "error",
          text1: "Une erreur est survenue lors de la mise à jour",
          text1Style: { color: "#1C3144" },
        });
        setLoading(false);
        return;
      }

      Toast.show({
        type: "success",
        text1: "Utilisateur mis à jour avec succès",
        text1Style: { color: "#1C3144" },
      });

      router.back();
    } catch (err) {
      console.error("Erreur inattendue:", err);
      Toast.show({
        type: "error",
        text1: "Une erreur est survenue",
        text1Style: { color: "#1C3144" },
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView>
      <View style={styles.containerContent}>
        <View style={styles.registerInfo}>
          <View style={styles.inputs}>
            {inputs.map((input, index) => {
              if (input?.type === "Select") {
                return (
                  <View key={index}>
                    <CustomizedSelect
                      name={input.name}
                      label={input.label}
                      register={register}
                      control={control}
                      errors={errors}
                      setError={setError}
                      data={input.data as any}
                    />
                  </View>
                );
              } else {
                return (
                  <View key={index}>
                    <InputCustomized
                      label={input.label}
                      placeholder={input.placeholder}
                      register={register}
                      name={input.name}
                      control={control}
                      errors={errors}
                      setError={setError}
                    />
                  </View>
                );
              }
            })}
          </View>
        </View>

        <View style={styles.buttons}>
          <ContainedButton
            label="Mettre à jour"
            backgroundColor="#F99527"
            onPress={handleSubmit(updateUser)}
            disabled={loading}
          />
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  containerContent: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    padding: 20,
    gap: 20,
    marginVertical: 20,
    width: "80%",
    alignSelf: "center",
    marginBottom: 70,
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    width: "100%",
    gap: 20,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
}); 