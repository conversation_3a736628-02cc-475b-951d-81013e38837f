import { View, Text, StyleSheet, Platform, TouchableOpacity, TextInput, Alert } from "react-native";
import { useForm } from "react-hook-form";
import { useLocalSearchParams, useRouter } from "expo-router";
import { useEffect, useState } from "react";
import { supabase } from "@/lib/supabase";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import CustomizedSelect from "@/components/ui/customizedSelect/customizedSelect";
import ContainedButton from "@/components/ui/buttons/ContainedButton";
import { useContext } from "react";
import { AppContext } from "@/state/AppContext";
import Toast from "react-native-toast-message";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import InputFileCustomized from "@/components/ui/inputs/InputFileCustomized";
import * as DocumentPicker from "expo-document-picker";
import uploadPDF from "@/lib/uploadDocumentCoverStorage";
import Header from "@/components/common/Header";

type TypeDoc = {
  name: string;
  image: string;
};

export default function UpdateDoc() {
  const [loading, setLoading] = useState(false);
  const [fileUri, setFileUri] = useState<string | null>(null);
  const [fileName, setFileName] = useState<string | null>(null);
  const { user, companySelected } = useContext(AppContext);

  const router = useRouter();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  const { doc_id, name, image, product_id } = useLocalSearchParams();
  const imageUrl = Array.isArray(image) ? image[0] : image;
  const docName = Array.isArray(name) ? name[0] : name;

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors, touchedFields },
    control,
    reset,
  } = useForm<TypeDoc>({
    defaultValues: {
      name: docName as string,
      image: imageUrl as string,
    },
  });

  const inputs = [
    {
      label: "Nom du document",
      name: "name",
      placeholder: "Nom du document",
    },
    {
      label: "Fichier PDF",
      name: "image",
      placeholder: "Choisir le fichier PDF",
      type: "file",
    },
  ];

  const pickFile = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: "application/pdf",
      });

      if (result.canceled) return;

      const selectedFile = result.assets?.[0];
      if (selectedFile && selectedFile.mimeType === "application/pdf") {
        setFileUri(selectedFile.uri);
        setFileName(selectedFile.name);
      } else {
        Toast.show({
          type: "error",
          text1: "Format non supporté",
          text1Style: { color: "#1C3144" },
          text2: "Veuillez sélectionner un fichier PDF",
        });
      }
    } catch (error) {
      console.error("Erreur lors de la sélection du fichier :", error);
      Toast.show({
        type: "error",
        text1: "Erreur",
        text1Style: { color: "#1C3144" },
        text2: "Impossible de sélectionner le fichier",
      });
    }
  };

  const updateDoc = async (data: TypeDoc) => {
    if (!data.name) {
      Toast.show({
        type: "error",
        text1: "Veuillez remplir tous les champs requis",
        text1Style: { color: "#1C3144" },
      });
      return;
    }

    setLoading(true);

    let publicUrl = imageUrl as string;
    if (fileUri) {
      const uploadResult = await uploadPDF(fileUri, user?.uid || "");

      if (!uploadResult || !uploadResult.url) {
        Alert.alert("Erreur", "Impossible de télécharger le fichier.");
        setLoading(false);
        return;
      }

      publicUrl = uploadResult.url;
    }

    try {
      const { error } = await supabase
        .from("docsProduct")
        .update({
          name: data.name,
          image: publicUrl,
        })
        .eq("id", doc_id);

      if (error) {
        console.log("error__", error);
        Toast.show({
          type: "error",
          text1: "Erreur lors de la modification du document",
          text1Style: { color: "#1C3144" },
        });
        setLoading(false);
        return;
      }

      Toast.show({
        type: "success",
        text1: "Le document a été modifié avec succès",
        text1Style: { color: "#1C3144" },
      });

      router.back();

      reset();
      setLoading(false);
    } catch (err) {
      setFileUri(null);
      setFileName(null);
      setLoading(false);

      Toast.show({
        type: "error",
        text1: "Une erreur est survenue",
        text1Style: { color: "#1C3144" },
      });
    }
  };

  const handleDelete = async () => {
    try {
      const { error } = await supabase
        .from("docsProduct")
        .delete()
        .eq("id", doc_id);

      if (error) {
        Toast.show({
          type: "error",
          text1: "Erreur lors de la suppression",
          text1Style: { color: "#1C3144" },
        });
        return;
      }

      Toast.show({
        type: "success",
        text1: "Document supprimé avec succès",
        text1Style: { color: "#1C3144" },
      });

      router.back();
    } catch (err) {
      Toast.show({
        type: "error",
        text1: "Une erreur est survenue",
        text1Style: { color: "#1C3144" },
      });
    }
  };

  return (
    <View style={styles.mainContainer}>
      <View style={styles.container}>
        <View style={styles.content}>
          <View style={styles.formContainer}>
            <View style={styles.inputs}>
              <InputCustomized
                label="Nom du document"
                placeholder="Nom du document"
                register={register}
                name="name"
                control={control}
                errors={errors}
                setError={setError}
              />

              <View style={styles.fileContainer}>
                <Text style={styles.label}>Fichier actuel</Text>
                <View style={styles.currentFile}>
                  <Text style={styles.fileName}>{docName}</Text>
                  <Text style={styles.fileInfo}>
                    {imageUrl?.endsWith(".pdf") ? "PDF" : "Image"}
                  </Text>
                </View>
              </View>

              <View style={styles.fileContainer}>
                <Text style={styles.label}>Nouveau fichier (optionnel)</Text>
                <View style={styles.fileInput}>
                  <TextInput
                    style={styles.fileInputText}
                    value={fileName || "Aucun fichier sélectionné"}
                    editable={false}
                  />
                  <TouchableOpacity
                    style={styles.fileButton}
                    onPress={pickFile}
                  >
                    <Text style={styles.fileButtonText}>Sélectionner</Text>
                  </TouchableOpacity>
                </View>
              </View>

              <View style={styles.fileContainer}>
                <Text style={styles.label}>Type de document</Text>
                <CustomizedSelect
                  name="type"
                  label="Type de document"
                  register={register}
                  control={control}
                  errors={errors}
                  setError={setError}
                  data={[
                    { label: "Document", value: "document" },
                    { label: "Image", value: "image" },
                    { label: "PDF", value: "pdf" },
                  ]}
                />
              </View>

              <View style={styles.fileContainer}>
                <Text style={styles.label}>Catégorie</Text>
                <CustomizedSelect
                  name="category"
                  label="Catégorie"
                  register={register}
                  control={control}
                  errors={errors}
                  setError={setError}
                  data={[
                    { label: "Manuel", value: "manuel" },
                    { label: "Procédure", value: "procedure" },
                    { label: "Instruction", value: "instruction" },
                    { label: "Autre", value: "autre" },
                  ]}
                />
              </View>
            </View>

            <View style={styles.buttons}>
              <ContainedButton
                label="Modifier"
                backgroundColor="#F99527"
                onPress={handleSubmit(updateDoc)}
                disabled={loading}
              />
              <TouchableOpacity 
                style={styles.deleteButton}
                onPress={() => {
                  if (Platform.OS === 'web') {
                    if (window.confirm("Êtes-vous sûr de vouloir supprimer ce document ?")) {
                      handleDelete();
                    }
                  } else {
                    Alert.alert(
                      "Supprimer le document",
                      "Êtes-vous sûr de vouloir supprimer ce document ?",
                      [
                        {
                          text: "Annuler",
                          style: "cancel"
                        },
                        {
                          text: "Supprimer",
                          onPress: handleDelete,
                          style: "destructive"
                        }
                      ]
                    );
                  }
                }}
              >
                <Text style={styles.deleteButtonText}>Supprimer le document</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  content: {
    gap: 20,
    padding: 20,
  },
  formContainer: {
    width: "100%",
    gap: 20,
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
  fileContainer: {
    width: "100%",
    gap: 10,
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
  },
  currentFile: {
    padding: 15,
    backgroundColor: "#f5f5f5",
    borderRadius: 4,
    borderWidth: 1,
    borderColor: "#d3d3d3",
  },
  fileName: {
    fontSize: 16,
    color: "#262627",
    marginBottom: 5,
  },
  fileInfo: {
    fontSize: 14,
    color: "#666",
  },
  fileInput: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  fileInputText: {
    flex: 1,
    padding: 10,
    borderWidth: 1,
    borderColor: "#d3d3d3",
    borderRadius: 4,
    backgroundColor: "#f5f5f5",
  },
  fileButton: {
    backgroundColor: "#F99527",
    padding: 10,
    borderRadius: 4,
  },
  fileButtonText: {
    color: "#fff",
    fontWeight: "bold",
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  deleteButton: {
    backgroundColor: '#FF3B30',
    padding: 12,
    borderRadius: 7,
    alignItems: 'center',
    marginTop: 10,
  },
  deleteButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
});
