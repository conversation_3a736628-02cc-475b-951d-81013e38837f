import { useEffect } from "react";
import { useRouter } from "expo-router";
import { supabase } from "@/lib/supabase";
import { useNavigationContainerRef } from "@react-navigation/native";

export default function AuthRedirect() {
  const router = useRouter();

  useEffect(() => {
    if (!router) return;

    const hash = window.location.hash;
    const urlParams = new URLSearchParams(hash.slice(1));
    const access_token = urlParams.get("access_token");
    const refresh_token = urlParams.get("refresh_token");

    const connect = async () => {
      if (access_token && refresh_token) {
        const { error } = await supabase.auth.setSession({ access_token, refresh_token });

        if (error) {
          console.error("Erreur lors du setSession :", error);
          router.replace("/login"); // OK après que le router est prêt
        } else {
          router.replace("/Accueil");
        }
      } else {
        router.replace("/login");
      }
    };

    // Attendre que la navigation soit montée
    setTimeout(() => {
      connect();
    }, 50); // une petite latence permet au layout de monter

  }, [router]);

  return <p>Connexion en cours...</p>;
}
