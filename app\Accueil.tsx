import Home from './menu/home';
import Carto from './menu/itemsMenuCockpit/carto';
import { useContext, useEffect } from 'react';
import { AppContext } from '@/state/AppContext';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import Header from '@/components/common/Header';
import { useRouter } from 'expo-router';

export default function AccueilWrapper() {
  const { companySelected, user, inCockpit, setInCockpit } = useContext(AppContext);
  const router = useRouter();

  useEffect(() => {
    // Vérifier si l'utilisateur est authentifié
    if (!user) {
      router.replace('/pre-login');
    } else {
      // Définir l'état inCockpit en fonction du statut de l'utilisateur
      const isInCockpit = user?.status === "Admin" || user?.status === "Manager";
      setInCockpit(isInCockpit);
    }
  }, [user?.status, user]);

  if (!user) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#F99527" />
      </View>
    );
  }

  if (!companySelected) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#F99527" />
      </View>
    );
  }

  // Déterminer si l'utilisateur est dans le cockpit
  const isInCockpit = inCockpit || (user?.status === "Admin" || user?.status === "Manager");

  return (
    <View style={{ flex: 1 }}>
      <Header onPressFlesh={() => router.navigate("/")} />
      {isInCockpit ? <Carto /> : <Home />}
    </View>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
}); 