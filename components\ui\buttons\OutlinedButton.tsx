import { StyleSheet, TouchableOpacity, Text } from "react-native";

type TypesProps = {
  label: string;
  labelColor?: string;
  backgroundColor?: string;
  onPress?: any;
  disabled?: boolean;
  borderColor?: string;
};

const OutlinedButton = ({
  label,
  labelColor = "white",
  onPress,
  disabled,
  borderColor,
}: TypesProps) => {
  return (
    <TouchableOpacity
      style={[styles.button, { borderColor: borderColor }]}
      onPress={onPress}
      disabled={disabled}
    >
      <Text style={[styles.label, { color: labelColor }]}>{label}</Text>
    </TouchableOpacity>
  );
};

export default OutlinedButton;

const styles = StyleSheet.create({
  button: {
    width: "100%",
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  label: {
    textAlign: "center",
    fontSize: 16,
  },
});
