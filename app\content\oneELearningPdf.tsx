import React, { useEffect, useState, useCallback } from 'react';
import { View, StyleSheet, Platform, Dimensions } from 'react-native';
import { useLocalSearchParams, useRouter, useFocusEffect } from 'expo-router';
import { supabase } from '@/lib/supabase';
import Toast from 'react-native-toast-message';
import Header from '@/components/common/Header';
import { WebView } from 'react-native-webview';

export default function OneELearningPdf() {
  const { id, name, url_media, url_mignature } = useLocalSearchParams();
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const router = useRouter();

  const extractFilePath = (url: string) => {
    try {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/');
      // Le chemin commence après 'object/public/'
      const publicIndex = pathParts.indexOf('public');
      if (publicIndex === -1) return null;
      
      const bucket = pathParts[publicIndex + 1];
      const filePath = pathParts.slice(publicIndex + 2).join('/');
      console.log('Chemin extrait:', { bucket, filePath });
      return { bucket, filePath };
    } catch (error) {
      console.error('Erreur lors de l\'extraction du chemin du fichier:', error);
      return null;
    }
  };

  const fetchPdfUrl = async () => {
    console.log('=== DÉBUT RECHERCHE PDF ===');
    console.log('ID:', id);
    console.log('URL Media:', url_media);
    console.log('URL Mignature:', url_mignature);

    try {
      // Essayer d'abord avec url_media
      if (url_media && url_media !== "") {
        const pathInfo = extractFilePath(url_media as string);
        if (pathInfo) {
          console.log('Tentative avec url_media:', pathInfo);
          const { data: mediaData, error: mediaError } = await supabase
            .storage
            .from(pathInfo.bucket)
            .createSignedUrl(pathInfo.filePath, 3600);

          if (mediaError) {
            console.log('Erreur url_media:', mediaError.message);
          } else if (mediaData?.signedUrl) {
            console.log('PDF trouvé dans url_media');
            console.log('URL signée générée:', mediaData.signedUrl);
            setPdfUrl(mediaData.signedUrl);
            return;
          }
        }
      }

      // Si url_media n'a pas fonctionné, essayer avec url_mignature
      if (url_mignature && url_mignature !== "") {
        const pathInfo = extractFilePath(url_mignature as string);
        if (pathInfo) {
          console.log('Tentative avec url_mignature:', pathInfo);
          const { data: mignatureData, error: mignatureError } = await supabase
            .storage
            .from(pathInfo.bucket)
            .createSignedUrl(pathInfo.filePath, 3600);

          if (mignatureError) {
            console.log('Erreur url_mignature:', mignatureError.message);
          } else if (mignatureData?.signedUrl) {
            console.log('PDF trouvé dans url_mignature');
            console.log('URL signée générée:', mignatureData.signedUrl);
            setPdfUrl(mignatureData.signedUrl);
            return;
          }
        }
      }

      // Si aucun des deux n'a fonctionné, essayer de récupérer les données de la base
      console.log('Tentative de récupération depuis la base de données');
      const { data: eLearningData, error: eLearningError } = await supabase
        .from("elearnings")
        .select("url_media")
        .eq("id", id)
        .single();

      if (eLearningError) {
        console.error('Erreur lors de la récupération des données:', eLearningError);
        throw eLearningError;
      }

      if (eLearningData?.url_media && eLearningData.url_media !== "") {
        const pathInfo = extractFilePath(eLearningData.url_media);
        if (pathInfo) {
          const { data: signedUrlData, error: signedUrlError } = await supabase
            .storage
            .from(pathInfo.bucket)
            .createSignedUrl(pathInfo.filePath, 3600);

          if (signedUrlError) {
            throw signedUrlError;
          }

          if (signedUrlData?.signedUrl) {
            setPdfUrl(signedUrlData.signedUrl);
            return;
          }
        }
      }

      // Si aucun PDF n'est trouvé, afficher un message approprié
      Toast.show({
        type: 'info',
        text1: 'Aucun PDF disponible',
        text2: 'Ce document ne contient pas de PDF',
      });
    } catch (error) {
      console.error('Erreur lors de la récupération du PDF:', error);
      Toast.show({
        type: 'error',
        text1: 'Erreur lors du chargement du PDF',
        text2: error instanceof Error ? error.message : 'Une erreur est survenue',
      });
    }
  };

  // Ajouter useFocusEffect pour recharger les données à chaque focus
  useFocusEffect(
    useCallback(() => {
      if (id) {
        fetchPdfUrl();
      }
    }, [id, url_media, url_mignature])
  );

  // Garder l'useEffect existant pour le chargement initial
  useEffect(() => {
    if (id) {
      fetchPdfUrl();
    }
  }, [id, url_media, url_mignature]);

  const getPdfViewerUrl = (pdfUrl: string) => {
    return `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodeURIComponent(pdfUrl)}`;
  };

  const handleEdit = () => {
    router.push({
      pathname: '/content/updateELearningPdf',
      params: {
        idELearning: id,
        title: name,
        description: '',
        url_media: url_media,
        url_mignature: url_mignature,
        file_type: 'pdf',
        type_profil: JSON.stringify([]),
        url_video: '',
      }
    });
  };

  if (Platform.OS === 'web') {
    return (
      <View style={styles.container}>
        <Header 
          title={name as string} 
          onPressIcon={handleEdit}
        />
        <Toast />
        {pdfUrl && (
          <iframe
            src={getPdfViewerUrl(pdfUrl)}
            style={styles.webview}
            title="PDF Viewer"
          />
        )}
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header 
        title={name as string} 
        onPressIcon={handleEdit}
      />
      <Toast />
      {pdfUrl && (
        <WebView
          source={{ uri: getPdfViewerUrl(pdfUrl) }}
          style={styles.webview}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          startInLoadingState={true}
          scalesPageToFit={true}
          onLoadStart={() => console.log('Chargement du PDF démarré')}
          onLoadEnd={() => console.log('Chargement du PDF terminé')}
          onError={(syntheticEvent) => {
            const { nativeEvent } = syntheticEvent;
            console.error('Erreur WebView:', nativeEvent);
          }}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  webview: {
    flex: 1,
    width: '100%',
    height: Platform.OS === 'web' ? Dimensions.get('window').height - 60 : '100%',
  },
}); 