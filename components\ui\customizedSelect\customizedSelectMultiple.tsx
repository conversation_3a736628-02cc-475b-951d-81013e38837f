import { ScrollView, StyleSheet, Text, View, Pressable } from "react-native";
import { Controller } from "react-hook-form";
import {
  Select,
  SelectTrigger,
  SelectInput,
  SelectIcon,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectItem,
} from "../select";
import { useState } from "react";
import React from "react";
type TypeSelect = {
  label: string;
  value: string;
  isDisabled?: boolean;
};

type TypesProps = {
  label?: string;
  placeholder?: string;
  typeInput?: string;
  value?: any;
  onChangeText?: any;
  register?: any;
  name: string;
  control?: any;
  errors?: any;
  setError?: any;
  isDisabled?: boolean;
  isReadOnly?: boolean;
  data: TypeSelect[];
  selectedValues: any;
  setSelectedValues: any;
};

const CustomizedSelectMultiple = ({
  label,
  name,
  control,
  errors,
  data,
  isDisabled = false,
  selectedValues,
  setSelectedValues,
}: TypesProps) => {
  const toggleSelection = (
    value: string,
    onChange: (val: string[]) => void
  ) => {
    let newSelection;
    if (selectedValues.includes(value)) {
      newSelection = selectedValues.filter((item: any) => item !== value);
    } else {
      newSelection = [...selectedValues, value];
    }
    setSelectedValues(newSelection);
    onChange(newSelection);
  };

  console.log("test__", selectedValues);

  return (
    <View style={styles.containerInput}>
      {label && <Text style={styles.label}>{label}</Text>}

      <Controller
        control={control}
        name={name}
        render={({ field: { onChange } }) => (
          <Select isDisabled={isDisabled}>
            <SelectTrigger variant="outline" size="md">
              <SelectInput
                placeholder={
                  selectedValues.length > 0
                    ? selectedValues
                        .map(
                          (val: any) => data.find((d) => d.value === val)?.label
                        )
                        .join(", ")
                    : "Choisissez des options"
                }
                style={{ color: "black" }}
              />
              <SelectIcon className="mr-3" />
            </SelectTrigger>

            <SelectPortal>
              <SelectBackdrop />
              <SelectContent>
                {data.length === 0 ? (
                  <SelectItem label={"Aucune donnée"} value={""} isDisabled />
                ) : (
                  data.map((item, index) => (
                    <Pressable
                      key={index}
                      onPress={() => toggleSelection(item.value, onChange)}
                      style={{
                        padding: 10,
                        backgroundColor: selectedValues.includes(item.value)
                          ? "#ddd"
                          : "transparent",
                      }}
                    >
                      <Text>{item.label}</Text>
                    </Pressable>
                  ))
                )}
              </SelectContent>
            </SelectPortal>
          </Select>
        )}
      />

      {errors[name] && (
        <Text style={styles.errorText}>Ce champ est requis</Text>
      )}
    </View>
  );
};

export default CustomizedSelectMultiple;

const styles = StyleSheet.create({
  containerInput: {
    gap: 8,
    fontSize: 14,
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginTop: 5,
  },
  label: {
    fontSize: 14,
    color: "#262627",
    marginVertical: 1,
    fontWeight: "bold",
  },
});
