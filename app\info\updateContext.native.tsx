/*
app/info/updateContext.tsx

Mettre à jour le contexte de l'entreprise.

Informations pertinentes :

- Lo<PERSON> de la validation du formulaire on envoie les données dans la table `context`.
- Une entreprise ne peut avoir qu'un contexte. Si une entreprise a déjà un contexte on ne peut pas en créer un nouveau (on peut modifier le contexte existant).

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern` et `Extern` n'ont pas accès à cette page.
*/

import { useLocalSearchParams, useRouter } from "expo-router";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  Alert,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import TypeContext from "@/types/typeContext";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";

export default function UpdateContext() {
  const { user, companySelected } = useContext(AppContext);
  const router = useRouter();
  const { path } = useLocalSearchParams();
  const [contextData, setContextData] = useState<TypeContext | null>(null);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const {
    handleSubmit,
    formState: { errors },
    control,
    reset,
  } = useForm<TypeContext>({
    defaultValues: {
      history: "",
      context: "",
      organisation: "",
      website: "",
    },
  });

  useEffect(() => {
    const fetchContext = async () => {
      if (!companySelected?.id) return;

      const { data, error } = await supabase
        .from("contexts")
        .select("*")
        .eq("company_id", companySelected.id)
        .single();

      if (error) {
        console.error(
          "Erreur lors de la récupération du contexte :",
          error.message
        );
      } else {
        setContextData(data);
        reset(data); // Préremplissage du formulaire
      }
    };

    fetchContext();
  }, [companySelected, reset]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  // Valider le formulaire
  const handleUpdateContext = async (data: TypeContext) => {
    try {
      if (!contextData) {
        Alert.alert("Erreur", "Aucun contexte existant trouvé.");
        return;
      }

      const { error } = await supabase
        .from("contexts")
        .update({
          history: data.history,
          context: data.context,
          organisation: data.organisation,
          website: data.website,
        })
        .eq("company_id", companySelected?.id);

      if (error) {
        Alert.alert(
          "Échec de la mise à jour",
          error.message || "Une erreur est survenue."
        );
        return;
      }

      Alert.alert("Succès", "Le contexte a été mis à jour avec succès.", [
        {
          text: "OK",
          onPress: () => {
            router.back();
          },
        },
      ]);
    } catch (err) {
      Alert.alert("Erreur inattendue", "Veuillez réessayer plus tard.");
      console.error("Erreur inattendue :", err);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      keyboardVerticalOffset={Platform.select({ ios: 0, android: 100 })}
    >
      <View style={styles.container}>
        <ScrollView style={styles.form}>
          {/* Texte d'introduction */}
          <View style={styles.inputContainer}>
            <Text>
              Affinez votre contexte d’entreprise, pour le rendre plus pertient
            </Text>
          </View>

          {/* Champ Histoire */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Histoire de l'entreprise</Text>
            <Controller
              control={control}
              name="history"
              rules={{ required: "L'histoire est requise." }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  placeholderTextColor={"#6B7280"}
                  style={styles.inputXL}
                  placeholder="Histoire de l'entreprise"
                  onChangeText={onChange}
                  value={value}
                  multiline
                />
              )}
            />
            {errors.history && (
              <Text style={styles.errorText}>{errors.history.message}</Text>
            )}
          </View>

          {/* Champ Contexte */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Contexte actuel</Text>
            <Controller
              control={control}
              name="context"
              rules={{ required: "Le contexte est requis." }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  placeholderTextColor={"#6B7280"}
                  style={styles.inputXL}
                  placeholder="Contexte actuel"
                  onChangeText={onChange}
                  value={value}
                  multiline
                />
              )}
            />
            {errors.context && (
              <Text style={styles.errorText}>{errors.context.message}</Text>
            )}
          </View>

          {/* Champ Organisation */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Organisation actuelle</Text>
            <Controller
              control={control}
              name="organisation"
              rules={{ required: "L'organisation est requise." }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  placeholderTextColor={"#6B7280"}
                  style={styles.inputXL}
                  placeholder="Organisation actuelle"
                  onChangeText={onChange}
                  value={value}
                  multiline
                />
              )}
            />
            {errors.organisation && (
              <Text style={styles.errorText}>
                {errors.organisation.message}
              </Text>
            )}
          </View>

          {/* Champ Site internet */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Site internet</Text>
            <Controller
              control={control}
              name="website"
              rules={{ required: "Le site internet est requis." }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  placeholderTextColor={"#6B7280"}
                  style={styles.input}
                  placeholder="Site internet"
                  onChangeText={onChange}
                  value={value}
                />
              )}
            />
            {errors.website && (
              <Text style={styles.errorText}>{errors.website.message}</Text>
            )}
          </View>

          {/* Bouton de soumission */}
          <TouchableOpacity
            style={styles.confirmButton}
            onPress={() => {
              handleSubmit(handleUpdateContext)();
            }}
          >
            <Text style={styles.confirmButtonText}>Mettre à jour</Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    height: "100%",
    width: "100%",
    alignItems: "center",
    alignContent: "center",
    backgroundColor: "#FFFFFF",
  },
  headSection: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 30,
  },
  backButton: {
    width: 24,
    textAlign: "left",
  },
  titleWrapper: {
    display: "flex",
    flexDirection: "row",
    marginVertical: 15,
  },
  title: {
    color: "#262627",
    fontSize: 17,
    textAlign: "left",
  },
  form: {
    width: "100%",
    alignSelf: "center",
    paddingHorizontal: 30,
    marginTop: 20,
  },
  inputXL: {
    borderWidth: 2,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "auto",
    padding: 12,
    height: 88,
    marginTop: 10,
    color: "#000000",
  },
  input: {
    borderWidth: 2,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "auto",
    padding: 12,
    marginTop: 10,
    color: "#000000",
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginBottom: 5,
  },
  infoText: {
    fontSize: 14,
    marginVertical: 10,
    color: "#555",
  },
  inputContainer: {
    position: "relative",
    marginBottom: 20,
    width: "100%",
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
    textAlign: "left",
  },
  confirmButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    marginBottom: 100,
    backgroundColor: "#f99527",
    marginTop: 10,
  },
  confirmButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  imagePicker: {
    width: "100%",
    padding: 10,
    backgroundColor: "#FFFFFF",
    borderColor: "#CCC",
    borderRadius: 8,
    borderWidth: 2,
    alignItems: "center",
    marginBottom: 10,
    marginTop: 10,
  },
  imagePickerText: {
    color: "#1F2937",
    fontWeight: "bold",
  },
  imagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  imageContainer: {
    width: "100%",
    justifyContent: "center",
  },
  uploadPlaceholder: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  uploadIcon: {
    borderWidth: 1,
    borderColor: "#f99527",
    borderRadius: 7,
    padding: 5,
  },
  uploadText: {
    textAlign: "center",
    fontWeight: "normal",
  },
  fullImagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  dateTimePickerContainer: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    textAlign: "left",
    marginVertical: 20,
  },
  dateTimePickerLabel: {
    color: "#063E76",
    fontSize: 14,
    fontWeight: "bold",
    backgroundColor: "#FFF",
  },
  datePicker: {
    width: "100%",
    marginTop: 10,
    marginBottom: 10,
  },
});
