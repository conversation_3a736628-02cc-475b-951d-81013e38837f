import ContainedButton from "@/components/ui/buttons/ContainedButton";
import OutlinedButton from "@/components/ui/buttons/OutlinedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useRouter, useLocalSearchParams, useFocusEffect } from "expo-router";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Platform,
} from "react-native";

import { useCallback, useContext, useState } from "react";
import { Octicons } from "@expo/vector-icons";
import { AppContext } from "@/state/AppContext";
import PaperInfo from "@/components/common/paperInfo";
import { supabase } from "@/lib/supabase";
import Header from "@/components/common/Header";
import Paper from "@/components/common/paper";
import PaperInfoImg from "@/components/common/paperInfoImg";
import { Spinner } from "@/components/ui/spinner";

const inputs = [
  // {
  //   label: "Designation",
  //   name: "designation",
  //   placeholder: "Ecrire la designation",
  // },
  // {
  //   label: "Cible",
  //   name: "target",
  //   placeholder: "Ecrire la cible",
  // },
  // {
  //   label: "Indicateur de mesure",
  //   name: "measuring_indicator",
  //   placeholder: "Indicateur de mesure",
  // },
  //
];

export default function detailsProcess() {
  const { user } = useContext(AppContext);

  const [activities, setActivities] = useState([]);

  const [isLoading, setIsLoading] = useState(false);

  const router = useRouter();

  const { process_id, pilote, process_name } = useLocalSearchParams();
  const [processDetails, setProcessDetails] = useState<any>(null);

  const fetchData = async () => {
    setIsLoading(true);
    const { data: dataGetted, error } = await supabase
      .from("activities")
      .select("*")
      .eq("process_id", process_id);

    if (!error) {
      setActivities(dataGetted as any);
      setIsLoading(false);
    }
    setIsLoading(false);
  };

  const fetchProcessDetails = async () => {
    if (!process_id) return;
    
    const { data, error } = await supabase
      .from("process")
      .select(`
        *,
        users:process_pilot (
          first_name,
          last_name
        )
      `)
      .eq("id", process_id)
      .single();

    if (error) {
      console.error("Erreur lors de la récupération du processus:", error);
      return;
    }

    setProcessDetails(data);
  };

  useFocusEffect(
    useCallback(() => {
      fetchProcessDetails();
      fetchData();
    }, [])
  );

  return (
    <View style={{ flex: 1 }}>
      <Header
        title={processDetails?.process_name || process_name as string}
        onPressFlesh={() => router.back()}
        onPressIcon={() => 
          router.push({
            pathname: "/info/updateProcess",
            params: { id: process_id }
          })
        }
      />
      <View style={styles.mainContainer}>
        <ScrollView style={styles.container}>
          <View style={styles.containerContent}>
            <View style={styles.registerInfo}>
              <Paper data={[{ 
                label: processDetails?.process_name || process_name as string, 
                value: processDetails?.users ? `${processDetails.users.first_name} ${processDetails.users.last_name}` : pilote as string 
              }]} />

              <View style={{ gap: 10, width: "100%" }}>
                <View style={styles.header}>
                  <Text style={{ fontWeight: "bold" }}>Liste des activités</Text>
                  <TouchableOpacity
                    onPress={() =>
                      router.push({
                        pathname: "/info/createActivity",
                        params: {
                          uid_user: user?.uid,
                          process_name: process_name,
                          process_id: process_id,
                          path: "/info/detailsProcess",
                          pilote: pilote,
                        },
                      })
                    }
                  >
                    <Octicons name="diff-added" size={25} color="#F99527" />
                  </TouchableOpacity>
                </View>
                <View style={{ width: "100%" }}>
                  {isLoading ? (
                    <Spinner size="small" color="#1C3144" />
                  ) : (
                    <View style={{ gap: 10 }}>
                      {activities.length > 0 ? (
                        activities?.map((item: any, index: number) => (
                          <PaperInfoImg
                            key={index}
                            title={item.activity_name}
                            text2={item.description}
                            fleshShow={false}
                            badgeColor="#F99527"
                            onPress={() =>
                              router.push({
                                pathname: "/info/detailsActivity",
                                params: { id: item.id },
                              })
                            }
                          />
                        ))
                      ) : (
                        <Text>
                          Vous n'avez pas encore crée des activities pour ce
                          processus
                        </Text>
                      )}
                    </View>
                  )}
                </View>
              </View>
            </View>
          </View>
        </ScrollView>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    alignItems: 'center',
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  containerContent: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    width: "100%",
    gap: 40,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
  },
});
