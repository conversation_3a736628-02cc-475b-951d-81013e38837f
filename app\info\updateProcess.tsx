import ContainedButton from "@/components/ui/buttons/ContainedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useRouter, useLocalSearchParams } from "expo-router";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  Alert,
  Platform,
} from "react-native";

import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import TypeProcess from "@/types/typeProcess";
import typeUserByAdmin from "@/types/typeUserByAdmin";
import CustomizedSelect from "@/components/ui/customizedSelect/customizedSelect";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import Header from "@/components/common/Header";

export default function UpdateProcess() {
  const [loading, setLoading] = useState(false);
  const [usersAdmin, setUsersAdmin] = useState<typeUserByAdmin[] | null>([]);
  const [process, setProcess] = useState<TypeProcess | null>(null);
  const { user, companySelected } = useContext(AppContext);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const router = useRouter();
  const { id } = useLocalSearchParams();

  const inputs = [
    {
      label: "Nom",
      name: "process_name",
      placeholder: "Ecrire le nom du processus",
    },
    {
      label: "Pilote du processus",
      name: "process_pilot",
      placeholder: "Ecrire le nom du pilote",
      type: "Select",
      data: usersAdmin,
    },
  ];

  useEffect(() => {
    const fetchUsersAdmin = async () => {
      const { data, error } = await supabase
        .from("users")
        .select()
        .eq("uid_admin", user?.uid)
        .in("profil", ["Pilote de processus", "Responsable QSE", "Responsable QSE Adjoint"]);

      setUsersAdmin(
        (data?.map((item) => {
          return { label: item.first_name, value: item.uid };
        }) as any) || null
      );
    };
    fetchUsersAdmin();
  }, []);

  useEffect(() => {
    const fetchProcess = async () => {
      if (!id) return;
      
      const { data, error } = await supabase
        .from("process")
        .select("*")
        .eq("id", id)
        .single();

      if (error) {
        console.error("Erreur lors de la récupération du processus:", error);
        return;
      }

      setProcess(data);
      reset({
        process_name: data.process_name,
        process_pilot: data.process_pilot,
      });
    };

    fetchProcess();
  }, [id]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
    control,
    reset,
  } = useForm<TypeProcess>({
    defaultValues: {
      process_name: "",
      process_pilot: "",
    },
  });

  const updateProcess = async (data: TypeProcess) => {
    if (!user || !id) {
      console.log("Utilisateur non connecté ou ID manquant.");
      return;
    }

    if (!data.process_name || !data.process_pilot) {
      Toast.show({
        type: "error",
        text1: "Le nom et le pilote sont obligatoires",
        text1Style: { color: "#1C3144" },
      });
      return;
    }

    setLoading(true);

    try {
      const { error } = await supabase
        .from("process")
        .update({
          process_name: data.process_name,
          process_pilot: data.process_pilot,
        })
        .eq("id", id);

      if (error) {
        console.error("Erreur lors de la mise à jour:", error);
        Toast.show({
          type: "error",
          text1: "Une erreur est survenue lors de la mise à jour",
          text1Style: { color: "#1C3144" },
        });
        setLoading(false);
        return;
      }

      Toast.show({
        type: "success",
        text1: "Processus mis à jour avec succès",
        text1Style: { color: "#1C3144" },
      });

      router.back();
    } catch (err) {
      console.error("Erreur inattendue:", err);
      Toast.show({
        type: "error",
        text1: "Une erreur est survenue",
        text1Style: { color: "#1C3144" },
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={{ flex: 1 }}>

      <View style={styles.mainContainer}>
        <ScrollView style={styles.container}>
          <View style={styles.containerContent}>
            <View style={styles.registerInfo}>
              <View style={styles.inputs}>
                {inputs.map((input, index) => {
                  if (input?.type === "Select") {
                    return (
                      <View key={index}>
                        <CustomizedSelect
                          name={input.name}
                          label={input.label}
                          register={register}
                          control={control}
                          errors={errors}
                          setError={setError}
                          data={input.data as any}
                        />
                      </View>
                    );
                  } else {
                    return (
                      <View key={index}>
                        <InputCustomized
                          label={input.label}
                          placeholder={input.placeholder}
                          register={register}
                          name={input.name}
                          control={control}
                          errors={errors}
                          setError={setError}
                        />
                      </View>
                    );
                  }
                })}
              </View>
            </View>

            <View style={styles.buttons}>
              <ContainedButton
                label="Mettre à jour"
                backgroundColor="#F99527"
                onPress={handleSubmit(updateProcess)}
                disabled={loading}
              />
            </View>
          </View>
        </ScrollView>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    alignItems: 'center',
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  containerContent: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    width: "100%",
    gap: 20,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
    marginTop: 20,
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
}); 