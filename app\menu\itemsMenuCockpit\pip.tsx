/*
app/menu/itemsMenuCockpit/pip.tsx

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `Supplier` n'ont pas accès à cette page.
*/

import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  Platform,
} from "react-native";
import { useRouter, useFocusEffect } from "expo-router";
import { useCallback, useContext, useEffect, useState } from "react";
import { Spinner } from "@/components/ui/spinner";
import { supabase } from "@/lib/supabase";
import { AppContext } from "@/state/AppContext";
import PaperInfo from "@/components/common/paperInfo";
import { Octicons } from "@expo/vector-icons";
import TypePIP from "@/types/typePIP";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";

const impacts = [
  { value: 0, label: "Influence majeure, peut stopper l'activité", weight: 5 },
  { value: 1, label: "Influence significative sur les opérations", weight: 4 },
  { value: 2, label: "Peut affecter certaines activités", weight: 3 },
  { value: 3, label: "Influence légère, peu de conséquences", weight: 2 },
  { value: 4, label: "Pas d'influence sur l'entreprise", weight: 1 },
];

const interests = [
  {
    value: 0,
    label: "Essentiel pour la réussite de l'entreprise",
    weight: 5,
  },
  { value: 1, label: "Partenariat ou relation stratégique", weight: 4 },
  { value: 2, label: "Assez important pour être suivi", weight: 3 },
  {
    value: 3,
    label: "Faiblement lié aux objectifs de l'entreprise",
    weight: 2,
  },
  { value: 4, label: "Aucune préoccupation pour l'entreprise", weight: 1 },
];

export default function PIP() {
  const router = useRouter();
  const [data, setData] = useState<TypePIP[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useContext(AppContext);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  const fetchData = async () => {
    setIsLoading(true);
    const { data: dataGetted, error } = await supabase
      .from("pips")
      .select("*, companies(*)")
      .eq("uid_user", user?.uid);
    if (!error) {
      setData(dataGetted as any);
    }

    setIsLoading(false);
  };

  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [])
  );

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["User", "Supplier"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  return (
    <View style={styles.mainContainer}>
      <ScrollView style={styles.container}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={{ fontWeight: "bold" }}>Créer une PIP</Text>
            <TouchableOpacity onPress={() => router.navigate("/pip/createPIP")}>
              <Octicons name="diff-added" size={25} color="#F99527" />
            </TouchableOpacity>
          </View>

          <View style={styles.users}>
            {isLoading ? (
              <Spinner size="small" color="#1C3144" />
            ) : data?.length > 0 ? (
              data?.map((item, index) => {
                const impactsWeight =
                  impacts.find((p) => p.value === item.impact)?.weight || 1;
                const interestsWeight =
                  interests.find((c) => c.value === item.interest)?.weight || 1;

                // Calcul du score
                const score = impactsWeight * interestsWeight;
                return (
                  <PaperInfo
                    key={index}
                    title={item.pip_name}
                    text1={item.expectations}
                    level={item.level}
                    score={score}
                    onPress={() => {
                      router.push({
                        pathname: "/pip/detailsPIP",
                        params: {
                          id: item.id,
                          score: score,
                          pip_name: item.pip_name,
                        },
                      });
                    }}
                  />
                );
              })
            ) : (
              <Text>Vous n'avez pas encore crée des PIP</Text>
            )}
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  content: {
    gap: 10,
    marginBottom: 10,
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 14,
  },
  users: {
    paddingHorizontal: 10,
    gap: 10,
  },
});
