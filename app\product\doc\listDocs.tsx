/*
app/menu/itemsMenuCockpit/product.tsx

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `Supplier` n'ont pas accès à cette page.
*/

import React from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
} from "react-native";
import { useFocusEffect, useLocalSearchParams, useRouter } from "expo-router";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { useCallback, useContext, useEffect, useState } from "react";
import { Spinner } from "@/components/ui/spinner";
import { supabase } from "@/lib/supabase";
import { AppContext } from "@/state/AppContext";
import TypeProduct from "@/types/typeProduct";
import PaperInfo from "@/components/common/paperInfo";
import { Octicons } from "@expo/vector-icons";
import FilterPage from "@/components/common/filterPage";
import Header from "@/components/common/Header";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import PaperInfoImgEvent from "@/components/common/paperInfoImgEvent";
import PaperInfoImg from "@/components/common/paperInfoImg";
import TypeActionEvent from "@/types/typeActionEvent";

export default function Maintenance() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const { user, companySelected } = useContext(AppContext);
  const [refreshKey, setRefreshKey] = useState(0);

  const [productDocs, setProductDocs] = useState<
    { name: string; created_at: string; id: number }[]
  >([]);

  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  const { product_id } = useLocalSearchParams();

  const fetchDocs = async () => {
    const { data: dataGetted, error } = await supabase
      .from("docsProduct")
      .select("*")
      .eq("product_id", product_id)
      .eq("type", "doc");

    if (!error) {
      setProductDocs(dataGetted as any);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchDocs();
    }, [])
  );

  useEffect(() => {
    if (!companySelected?.id) {
      console.error("❌ Aucun company_id défini, requête annulée !");
      return;
    }

    fetchDocs();

    // ✅ Écoute les changements en temps réel et rafraîchit la liste
    const channel = supabase
      .channel("products-changes")
      .on(
        "postgres_changes",
        { event: "*", schema: "public", table: "products" },
        (payload: any) => {
          console.log("📌 Changement détecté dans products :", payload);
          setRefreshKey((prev) => prev + 1); // 🔥 Force un re-render en incrémentant `refreshKey`
        }
      )
      .subscribe(async (status: boolean) => {
        if (status === true) {
          console.log("✅ Abonné aux changements de la table `products` !");
        }
      });

    return () => {
      // Nettoyer l'écouteur lorsqu'on quitte la page
      supabase.removeChannel(channel);
    };
  }, [companySelected, refreshKey]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["User", "Supplier"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  return (
    <>
      <ScrollView style={styles.container}>
        <View style={{ gap: 10, marginBottom: 10 }}>
          <View style={styles.header}>
            <Text style={{ fontWeight: "bold" }}>Créer un document</Text>
            <TouchableOpacity
              onPress={() =>
                router.push({
                  pathname: "/product/doc/createDoc",
                  params: {
                    product_id: product_id,
                  },
                })
              }
            >
              <Octicons name="diff-added" size={25} color="#F99527" />
            </TouchableOpacity>
          </View>

          <View style={styles.users}>
            {isLoading ? (
              <Spinner size="small" color="#1C3144" />
            ) : productDocs.length > 0 ? (
              productDocs.map((item, index) => (
                <PaperInfo
                  title={item.name}
                  date={new Date(item.created_at)}
                  badgeShow={false}
                  onPress={() =>
                    router.push({
                      pathname: "/product/doc/detailsDoc",
                      params: { doc_id: item.id, product_id: product_id },
                    })
                  }
                />
              ))
            ) : (
              <Text>Vous n'avez pas encore crée des documents</Text>
            )}
          </View>
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "column",
    width: "100%",
    gap: 10,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 10,
    marginTop: 14,
  },
  users: {
    // backgroundColor: "red",
    // width: "100%",
    paddingHorizontal: 10,
    gap: 10,
  },
});
