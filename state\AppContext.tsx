import React, {
  createContext,
  useState,
  useEffect,
  ReactN<PERSON>,
  Dispatch,
  SetStateAction,
} from "react";
import { supabase } from "@/lib/supabase";
import { useRouter, usePathname } from "expo-router";
import TypeUser from "@/types/user";
import TypeCompany from "@/types/company";
import { Platform } from "react-native";
import { Session } from "@supabase/supabase-js";

const AsyncStorage =
  typeof document === "undefined" || Platform.OS === "web"
    ? null
    : require("@react-native-async-storage/async-storage").default;




// Définir le type pour le contexte
type AppContextType = {
  session: any;
  setSession: Dispatch<SetStateAction<any>>;
  user: TypeUser | any;
  setUser: Dispatch<SetStateAction<TypeUser | null>>;
  platformUser: boolean;
  setPlatformUser: Dispatch<SetStateAction<boolean>>;
  companySelected: TypeCompany | null;
  setCompanySelected: Dispatch<SetStateAction<TypeCompany | null>>;
  headerItems: { title: string; onPress: any };
  setHeaderItems: Dispatch<SetStateAction<{ title: string; onPress: any }>>;
  inCockpit: boolean;
  setInCockpit: Dispatch<SetStateAction<boolean>>;
};

// Initialiser le contexte avec un type ou une valeur par défaut
const AppContext = createContext<AppContextType>({} as AppContextType);

// Import conditionnel pour éviter l'erreur sur Web


type AppProviderProps = {
  children: ReactNode;
};

const AppProvider = ({ children }: AppProviderProps) => {
  const [session, setSession] = useState<any>(null);
  const [inCockpit, setInCockpit] = useState<boolean>(false);

  const [headerItems, setHeaderItems] = useState<{
    title: string;
    onPress: any;
  }>({ title: "", onPress: null });

  const [user, setUser] = useState<TypeUser | null>({
    uid: "",
    first_name: "",
    last_name: "",
    email: "",
    status: "",
    created_at: "",
    profil_picture: "",
    companies: [],
    profil: "",
  });
  const [companySelected, setCompanySelected] = useState<TypeCompany | null>(
    user?.companies[0] || null
  );
  const [platformUser, setPlatformUser] = useState<boolean>(true);
  const [isSessionLoading, setIsSessionLoading] = useState<boolean>(true);

  const router = useRouter();
  const pathname = usePathname();

  // console.log("pathname__", pathname);




  // Sauvegarder la session dans AsyncStorage
  const saveSession = async (newSession: any) => {
    if (!AsyncStorage) return; // Évite les erreurs sur Web

    try {
      if (newSession) {
        if (AsyncStorage) {
          await AsyncStorage.setItem("session", JSON.stringify(newSession));
        }
      } else {
        if (AsyncStorage) {
        await AsyncStorage.removeItem("session");
        }
      }
    } catch (error) {
      console.error("Erreur lors de la sauvegarde de la session :", error);
    }
  };

  // Charger la session depuis AsyncStorage au démarrage
  useEffect(() => {
    const loadSession = async () => {
      try {
        if (AsyncStorage) { // Vérification avant d'utiliser AsyncStorage
          const storedSession = await AsyncStorage.getItem("session");
          if (storedSession) {
            setSession(JSON.parse(storedSession));
          }
        }
      } catch (error) {
        console.error("Erreur lors du chargement de la session :", error);
      }
    };
    loadSession();
  }, []);

  // Charger la session depuis AsyncStorage au démarrage
  useEffect(() => {
    const loadSession = async () => {
      try {
        const storedSession = await AsyncStorage.getItem("session");
        if (storedSession) {
          setSession(JSON.parse(storedSession));
        }
      } catch (error) {
        console.error("Erreur lors du chargement de la session :", error);
      }
    };
    loadSession();
  }, []);

  // Récupération et restauration de la session
  useEffect(() => {
    const fetchSession = async () => {
      setIsSessionLoading(true); // Commence à charger la session

      // Récupère la session actuelle depuis Supabase
      const {
        data: { session },
        error,
      } = await supabase.auth.getSession();

      if (error) {
        console.error("Erreur lors de la récupération de la session :", error);
      } else {
        setSession(session);

        if (session) {
          try {
            // Récupère les données utilisateur depuis la table "users"
            const { data: userData, error: userError } = await supabase
              .from("users")
              .select("*, companies(*)")
              .eq("uid", session.user?.id)
              .single();

            if (userError) {
              console.error(
                "Erreur lors de la récupération des données utilisateur :",
                userError
              );
            } else {
              setUser(userData);
              setCompanySelected(userData.companies[0] || null);
            }
          } catch (err) {
            console.error(
              "Erreur inattendue lors de la récupération des données utilisateur :",
              err
            );
          }
        } else {
          setUser(null);
          setCompanySelected(null);
        }
      }

      setIsSessionLoading(false); // Session complètement chargée
    };

    fetchSession();

    // Écoute les changements de session (connexion, déconnexion)
    const { data: listener } = supabase.auth.onAuthStateChange(
      (_event: Event, session: Session) => {
        setSession(session);

        if (!session) {
          setUser(null);
          setCompanySelected(null);
        }
      }
    );

    return () => {
      listener?.subscription.unsubscribe();
    };
  }, []);

  // Gère les redirections en fonction de la session
  useEffect(() => {
    if (!isSessionLoading && !session?.access_token) {
      // Ne rediriger que si nous ne sommes pas déjà sur une page d'authentification
      if (pathname !== "/login" && pathname !== "/register" && pathname !== "/pre-login") {
        router.replace("/pre-login");
      }
    }

    if (
      pathname !== "/objectif/detailsObjecti" &&
      pathname !== "/objectif/updateObjectif"
    ) {
      setHeaderItems({ title: "", onPress: null });
    }
  }, [session, pathname, isSessionLoading]);

  // Mettre à jour AsyncStorage à chaque changement de session
  useEffect(() => {
    saveSession(session);
  }, [session]);

  return (
    <AppContext.Provider
      value={{
        session,
        setSession,
        user,
        setUser,
        platformUser,
        setPlatformUser,
        companySelected,
        setCompanySelected,
        headerItems,
        setHeaderItems,
        inCockpit,
        setInCockpit,
      }}
    >
      {children}
    </AppContext.Provider>
  );
};

export { AppContext, AppProvider };
