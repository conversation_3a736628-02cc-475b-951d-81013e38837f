import React, { useContext } from "react";
import { View, Text, StyleSheet, Image, TouchableOpacity } from "react-native";
import { Badge, BadgeText } from "../ui/badge";
import FontAwesome5 from "@expo/vector-icons/FontAwesome5";

type typePropPaper = {
  imgSrc?: string;
  title?: string;
  text1?: string;
  text2?: string;
  text3?: string;
  text4?: string;
  level?: number;
  score?: number;
  onPress?: any;
  date?: any;
  badgeShow?: boolean;
};

const PaperInfo = ({
  title,
  text1,
  text2,
  text3,
  text4,
  level,
  onPress,
  date,
  score,
  badgeShow = true,
}: typePropPaper) => {
  function formatDate(dateString: string) {
    const date = new Date(dateString);
    return date.toLocaleDateString("fr-FR");
  }

  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <View style={styles.contentContainer}>
        <View style={styles.mainContent}>
          <View style={styles.headerContainer}>
            <Text style={styles.title} numberOfLines={1}>
              {(title ?? "").length > 30 ? (title ?? "").slice(0, 30) + "..." : title}
            </Text>
            {date && (
              <Text style={styles.date}>{formatDate(date)}</Text>
            )}
          </View>

          <View style={styles.textContainer}>
            {text1 && <Text style={styles.text} numberOfLines={1}>{text1}</Text>}
            {text2 && <Text style={styles.text} numberOfLines={1}>{text2}</Text>}
            {text3 && <Text style={styles.text} numberOfLines={1}>{text3}</Text>}
            {text4 && <Text style={styles.text} numberOfLines={1}>{text4}</Text>}
          </View>

          {badgeShow && (
            <Badge
              variant="solid"
              style={{
                backgroundColor: level === 0 ? "#b91c1c" : level === 1 ? "#f99527" : "#2fc12b",
                borderRadius: 48,
                paddingHorizontal: 10,
                minWidth: 80,
                alignSelf: "flex-start",
              }}
            >
              <BadgeText style={{ color: "white", textAlign: "center" }}>
                <Text numberOfLines={1} adjustsFontSizeToFit>
                  {level === 0 ? "Non maîtrisée" : level === 1 ? "Partiel" : "Maîtrisée"}
                </Text>
              </BadgeText>
            </Badge>
          )}
        </View>

        <View style={styles.rightContent}>
          {score !== undefined && (
            <View style={styles.scoreContainer}>
              <Text style={styles.scoreLabel}>Score</Text>
              <Text style={styles.scoreValue}>{score}</Text>
            </View>
          )}
          {!date && <FontAwesome5 name="chevron-right" size={20} color="black" />}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: "#E6E6E6",
    borderRadius: 10,
    width: "100%",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 5,
    elevation: 5,
    padding: 12,
  },
  contentContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  mainContent: {
    flex: 1,
    marginRight: 12,
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  title: {
    fontWeight: "bold",
    flex: 1,
    marginRight: 8,
  },
  date: {
    color: "#666",
    fontSize: 11,
  },
  textContainer: {
    gap: 4,
    marginBottom: 8,
  },
  text: {
    color: "#525252",
    fontSize: 11,
  },
  rightContent: {
    alignItems: "center",
    justifyContent: "center",
  },
  scoreContainer: {
    alignItems: "center",
    marginBottom: 4,
  },
  scoreLabel: {
    fontSize: 12,
    color: "#666",
  },
  scoreValue: {
    fontWeight: "bold",
    fontSize: 16,
  },
});

export default PaperInfo;
