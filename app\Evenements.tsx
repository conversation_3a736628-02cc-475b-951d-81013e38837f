import Event from './menu/itemsMenuDefault/event';
import { useContext } from 'react';
import { AppContext } from '@/state/AppContext';
import { View, ActivityIndicator, StyleSheet } from 'react-native';

export default function Evenements() {
  const { companySelected } = useContext(AppContext);

  if (!companySelected) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#F99527" />
      </View>
    );
  }

  return <Event />;
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
}); 