import { StyleSheet, ScrollView, View, TextInput, TouchableWithoutFeedback, Platform, Keyboard } from "react-native";

import { useCallback, useContext, useEffect, useState } from "react";
import { AppContext } from "@/state/AppContext";
import UsersInfosSection from "@/components/infoSection/users";
import { FontAwesome, Octicons } from "@expo/vector-icons";
import { useFocusEffect, useRouter } from "expo-router";
import typeUserByAdmin from "@/types/typeUserByAdmin";
import { supabase } from "@/lib/supabase";
import ProcessInfoSection from "@/components/infoSection/process";
import TypeProcess from "@/types/typeProcess";

const inputs = [
  { label: "Nom", name: "last_name", placeholder: "Ecrire ton nom" },
  { label: "Prénom", name: "first_name", placeholder: "Ecrire ton prénom" },
  {
    label: "Email",
    name: "email",
    placeholder: "Ecrire ton email",
    isDisabled: true,
  },
];

export default function Process() {
  const router = useRouter();
  const [usersAdmin, setUsersAdmin] = useState<TypeProcess[] | null>(null);
  const [searchQuery, setSearchQuery] = useState(""); // État pour la barre de recherche
  const [filteredDataList, setFilteredDataList] = useState<any>([]); // Liste filtrée

  const { user } = useContext(AppContext);

  const fetchUsersAdmin = async () => {
    const { data, error } = await supabase
      .from("process")
      .select("*, companies(*), users(*)")
      .eq("uid_admin", user?.uid);

    if (!error) {
      setUsersAdmin(data as any);
    }
  };

  useEffect(() => {
    const filteredList = usersAdmin?.filter((item) =>
      `${item.process_name}`.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredDataList(filteredList);
  }, [searchQuery, usersAdmin]);

  useFocusEffect(
    useCallback(() => {
      fetchUsersAdmin();
    }, [])
  );

  return (
    <View style={styles.mainContainer}>
      <TouchableWithoutFeedback 
        onPress={Platform.OS !== "web" ? Keyboard.dismiss : undefined} 
        accessible={false}
      >
        <ScrollView style={styles.container}>
          {/* Barre de recherche */}
          <View style={styles.searchContainer}>
            <View style={styles.searchInputWrapper}>
              <FontAwesome
                name="search"
                size={24}
                color="#3C3c4399"
                style={styles.searchIcon}
              />
              <TextInput
                style={styles.searchInput}
                placeholder="Rechercher par nom..."
                placeholderTextColor="#3C3c4399"
                value={searchQuery}
                onChangeText={(text) => setSearchQuery(text)}
              />
            </View>
          </View>
          <ProcessInfoSection
            icon={
              <Octicons
                name="diff-added"
                size={24}
                color="#F99527"
                onPress={() => router.navigate("/info/createProcess")}
              />
            }
            data={filteredDataList as any}
          />
          <View style={{ height: 100 }} />
        </ScrollView>
      </TouchableWithoutFeedback>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    alignItems: 'center',
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  searchContainer: {
    width: "100%",
    marginBottom: 20,
    marginTop: 10,
    paddingHorizontal: 10,
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  searchInputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1C314414",
    borderRadius: 8,
    paddingHorizontal: 10,
    height: 60,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    padding: 10,
    fontSize: 16,
    color: "#000000",
  },
});
