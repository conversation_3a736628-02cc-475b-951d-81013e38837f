/*
app/screens/actions/UpdateActionDocument.tsx

Formulaire pour modifier un document d'action existant.

Informations pertinentes :
- Les informations du document sont récupérées depuis la navigation
*/

import React, { useContext, useEffect, useState } from "react";
import {
  Alert,
  StyleSheet,
  View,
  TextInput,
  Image,
  Text,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Linking,
} from "react-native";
import { supabase } from "@/lib/supabase";
import { Controller, useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { AntDesign } from "@expo/vector-icons";
import Octicons from "@expo/vector-icons/Octicons";
import * as DocumentPicker from "expo-document-picker";
import uploadDocumentfileStorage from "@/lib/uploadDocumentCoverStorage";
import { useRoute, RouteProp } from "@react-navigation/native";
import { useRouter } from "expo-router";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { ParamListBase, useNavigation } from "@react-navigation/native";

// Données de navigation
type UpdateActionDocumentRouteParams = {
  document: {
    id: number;
    name: string;
    description: string;
    file_url: string;
    action_id: number;
  };
};

export default function UpdateActionDocument() {
  const router = useRouter();
  const [file, setFile] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const route = useRoute<RouteProp<{ params: UpdateActionDocumentRouteParams }, "params">>();
  const { document } = route.params;
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const { user } = useContext(AppContext);

  const {
    handleSubmit,
    formState: { errors },
    control,
    reset,
  } = useForm({
    defaultValues: {
      name: document?.name || "",
      description: document?.description || "",
      file: document?.file_url || "",
    },
  });

  // Enregistrer le fichier
  const handleFilePicker = async () => {
    try {  
      const result = await DocumentPicker.getDocumentAsync({
        type: "application/pdf",
      });
  
      if (result.canceled) return;
  
      const selectedFile = result.assets?.[0];
      if (selectedFile && selectedFile.mimeType === "application/pdf") {
        setFile(selectedFile.uri);
      } else {
        alert("Seuls les fichiers PDF sont autorisés.");
      }
    } catch (error) {
      console.error("Erreur lors de la sélection du fichier :", error);
      alert("Impossible de sélectionner le fichier.");
    }
  };

  // Valider le formulaire
  const handleUpdateDocument = async (data: any) => {
    try {
      if (!user?.uid) {
        if (Platform.OS === 'web') {
          window.alert("Erreur: Aucun utilisateur connecté.");
        } else {
          Alert.alert("Erreur", "Aucun utilisateur connecté.");
        }
        return;
      }

      setIsSubmitting(true);

      // Vérifier et uploader le fichier PDF sur Supabase Storage
      let publicUrl = document?.file_url || "";
      if (file) {
        const uploadResult = await uploadDocumentfileStorage(file, user.uid);

        if (!uploadResult || !uploadResult.url) {
          if (Platform.OS === 'web') {
            window.alert("Erreur: Impossible de télécharger le fichier.");
          } else {
            Alert.alert("Erreur", "Impossible de télécharger le fichier.");
          }
          return;
        }

        publicUrl = uploadResult.url;
      }

      // Mettre à jour le document dans la table actions_documents
      const { error } = await supabase
        .from("actions_documents")
        .update({
          name: data.name,
          description: data.description,
          file_url: publicUrl,
        })
        .eq("id", document.id);

      if (error) {
        if (Platform.OS === 'web') {
          window.alert("Erreur: Échec de la modification du document.");
        } else {
          Alert.alert("Erreur", "Échec de la modification du document.");
        }
        return;
      }

      if (Platform.OS === 'web') {
        window.alert("Succès: Le document a été modifié avec succès.");
        setTimeout(() => {
          reset();
          router.back();
        }, 1000);
      } else {
        Alert.alert("Succès", "Le document a été modifié avec succès.", [
          {
            text: "OK",
            onPress: () => {
              setTimeout(() => {
                reset();
                router.back();
              }, 1000);
            },
          },
        ]);
      }
    } catch (err) {
      if (Platform.OS === 'web') {
        window.alert("Erreur: Une erreur est survenue lors de la modification du document.");
      } else {
        Alert.alert("Erreur", "Une erreur est survenue lors de la modification du document.");
      }
      console.error("Erreur inattendue :", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={[
        styles.container,
        Platform.OS === 'web' && { maxWidth: 800, alignSelf: 'center' }
      ]}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      keyboardVerticalOffset={Platform.select({ ios: 0, android: 100 })}
    >
      <ScrollView style={styles.form}>
        {/* Champ Nom */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Nom</Text>
          <Controller
            control={control}
            name="name"
            rules={{ required: "Le nom est requis." }}
            render={({ field: { onChange, value } }) => (
              <TextInput
                maxLength={20}
                placeholderTextColor={"#6B7280"}
                style={styles.input}
                placeholder="Nom du document"
                onChangeText={onChange}
                value={value}
              />
            )}
          />
          {errors.name && (
            <Text style={styles.errorText}>{errors.name.message}</Text>
          )}
        </View>

        {/* Champ Description */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Description</Text>
          <Controller
            control={control}
            name="description"
            rules={{ required: "La description est requise." }}
            render={({ field: { onChange, value } }) => (
              <TextInput
                maxLength={200}
                placeholderTextColor={"#6B7280"}
                style={styles.input}
                placeholder="Description"
                onChangeText={onChange}
                value={value}
                multiline
              />
            )}
          />
          {errors.description && (
            <Text style={styles.errorText}>{errors.description.message}</Text>
          )}
        </View>

        {/* Champ Fichier */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Fichier</Text>
          {file || document?.file_url ? (
            <View style={styles.fileContainer}>
              {Platform.OS === "web" ? (
                <View style={styles.pdfViewerContainer}>
                  <object
                    data={file || document?.file_url}
                    type="application/pdf"
                    style={{
                      width: "100%",
                      height: "100%",
                      border: "none",
                    }}
                  >
                    <p>Impossible d'afficher le PDF. <a href={file || document?.file_url} target="_blank">Télécharger le PDF</a></p>
                  </object>
                  <View style={styles.fileInfoContainer}>
                    <Text style={styles.fileName}>{(file || document?.file_url)?.split('/').pop()}</Text>
                    <TouchableOpacity
                      style={styles.openFileButton}
                      onPress={() => window.open(file || document?.file_url, '_blank')}
                    >
                      <Text style={styles.openFileButtonText}>Ouvrir dans un nouvel onglet</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              ) : (
                <View style={styles.filePreviewContainer}>
                  <Text style={styles.fileName}>{(file || document?.file_url)?.split('/').pop()}</Text>
                  <TouchableOpacity
                    style={styles.openFileButton}
                    onPress={() => Linking.openURL(file || document?.file_url)}
                  >
                    <Text style={styles.openFileButtonText}>Ouvrir le PDF</Text>
                  </TouchableOpacity>
                </View>
              )}
              <TouchableOpacity
                style={[styles.fileButton, styles.modifyButton]}
                onPress={handleFilePicker}
              >
                <Text style={styles.fileButtonText}>Modifier le PDF</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <TouchableOpacity
              style={styles.imagePicker}
              onPress={handleFilePicker}
            >
              <View style={styles.uploadContainer}>
                <Octicons
                  style={styles.uploadIcon}
                  name="upload"
                  size={24}
                  color="#f99527"
                />
                <Text style={styles.uploadText}>Choisir un fichier PDF</Text>
                <Text style={styles.uploadSubtext}>Glissez-déposez ou cliquez pour sélectionner</Text>
              </View>
            </TouchableOpacity>
          )}
        </View>

        {/* Bouton de soumission */}
        <TouchableOpacity
          style={[styles.confirmButton, isSubmitting && styles.disabledButton]}
          onPress={() => {
            if (!isSubmitting) {
              handleSubmit(handleUpdateDocument)();
            }
          }}
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <Text style={styles.confirmButtonText}>Modifier</Text>
          )}
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    height: "100%",
    width: "100%",
    alignItems: "center",
    alignContent: "center",
    backgroundColor: "#fff",
  },
  disabledButton: {
    backgroundColor: "#ccc",
  },
  form: {
    width: "90%",
    alignSelf: "center",
    marginTop: 20,
  },
  input: {
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "auto",
    padding: 12,
    marginTop: 10,
    color: "#000000",
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginBottom: 5,
  },
  inputContainer: {
    position: "relative",
    marginBottom: 20,
    width: "auto",
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
  },
  confirmButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    marginBottom: 16,
    backgroundColor: "#f99527",
    marginTop: 10,
  },
  confirmButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  imagePicker: {
    padding: 10,
    backgroundColor: "transparent",
    borderColor: "#E5E7EB",
    borderRadius: 8,
    borderWidth: 1,
    alignItems: "center",
    marginBottom: 10,
  },
  imagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  fileContainer: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    rowGap: 10,
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: "#f8f9fa",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#e9ecef",
    borderStyle: "dashed",
  },
  pdfViewerContainer: {
    width: "100%",
    height: 600,
    marginBottom: 20,
    borderRadius: 10,
    overflow: "hidden",
    backgroundColor: "#fff",
    ...Platform.select({
      web: {
        boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
        display: "flex",
        flexDirection: "column",
      },
    }),
  },
  filePreviewContainer: {
    width: "100%",
    alignItems: "center",
    padding: 20,
    backgroundColor: "#fff",
    borderRadius: 8,
    marginBottom: 10,
  },
  fileName: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 10,
    color: "#262627",
  },
  openFileButton: {
    backgroundColor: "#f99527",
    padding: 10,
    borderRadius: 8,
    marginBottom: 10,
  },
  openFileButtonText: {
    color: "#fff",
    fontWeight: "bold",
  },
  uploadContainer: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  uploadIcon: {
    borderWidth: 2,
    borderColor: "#f99527",
    borderRadius: 7,
    padding: 10,
    marginBottom: 10,
  },
  uploadText: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#262627",
    marginBottom: 5,
  },
  uploadSubtext: {
    fontSize: 14,
    color: "#6B7280",
    textAlign: "center",
  },
  fileButton: {
    backgroundColor: "#f99527",
    padding: 10,
    borderRadius: 8,
    marginTop: 10,
  },
  fileButtonText: {
    color: "#fff",
    fontWeight: "bold",
  },
  modifyButton: {
    backgroundColor: "#f99527",
    padding: 10,
    borderRadius: 8,
    marginTop: 10,
  },
  fileInfoContainer: {
    padding: 15,
    backgroundColor: "#f8f9fa",
    borderTopWidth: 1,
    borderTopColor: "#e9ecef",
    width: "100%",
  },
}); 