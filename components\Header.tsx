import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { Link, useRouter } from "expo-router";
import { useSupabase } from "../../lib/useSupabase";
import { Ionicons } from "@expo/vector-icons";
import { supabase } from '../../lib/supabase';

export default function Header() {
  const router = useRouter();
  const { session } = useSupabase();

  const handleLogout = async () => {
    const { error } = await supabase.auth.signOut();
    if (!error) {
      router.replace("/");
    }
  };

  return (
    <View style={styles.header}>
      <View style={styles.leftSection}>
        <Link href="/" asChild>
          <TouchableOpacity>
            <Text style={styles.title}>Management QSE</Text>
          </TouchableOpacity>
        </Link>
      </View>
      <View style={styles.rightSection}>
        {session ? (
          <>
            <Link href="/profile" asChild>
              <TouchableOpacity style={styles.button}>
                <Ionicons name="person" size={24} color="white" />
              </TouchableOpacity>
            </Link>
            <TouchableOpacity style={styles.button} onPress={handleLogout}>
              <Ionicons name="log-out" size={24} color="white" />
            </TouchableOpacity>
          </>
        ) : (
          <Link href="/login" asChild>
            <TouchableOpacity style={styles.button}>
              <Ionicons name="log-in" size={24} color="white" />
            </TouchableOpacity>
          </Link>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    backgroundColor: "#1a1a1a",
  },
  leftSection: {
    flex: 1,
  },
  rightSection: {
    flexDirection: "row",
    gap: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    color: "white",
  },
  button: {
    padding: 8,
  },
}); 