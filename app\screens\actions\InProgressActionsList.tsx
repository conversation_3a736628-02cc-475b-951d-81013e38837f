/*
app/screens/actions/InProgressActionsList.tsx

Liste des actions en cours.

Informations pertinentes :

- Les actions sont récupérées depuis la table `actions` de Supabase.
- Chaque action appartient à une entreprise (colonne `company_id`).
- Les données incluent les relations suivantes :
  -> Table `action_administrators` pour récupérer les administrateurs des actions (via `user_id` et jointure avec la table `users` pour obtenir les noms).
  -> Table `action_processes` pour récupérer les processus liés aux actions (via `process_id` et jointure avec la table `process` pour obtenir les noms des processus).
  -> Table `action_events` pour récupérer les événements associés aux actions (via `event_id` et jointure avec la table `events` pour obtenir les noms des événements).
- 🚨 Les actions sont groupées en fonction de la date et du statut (ici, `status : not_achieved` et `date` < date du jour`).
- Les données récupérées incluent :
  - `id` : Identifiant unique de l'action.
  - `name` : Nom de l'action.
  - `date` : Date attribuée à l'action.
  - `status` : Statut de l'action (`achieved` ou `not_achieved`).
  - `type` : Type de l'action (`immediate` ou `corrective`).
  - `administrator` : Informations sur le responsable de l'action (nom et prénom).
  - `process` : Nom du processus lié à l'action.
  - `event` : Nom de l'événement associé à l'action.
*/

import {
  FlatList,
  Keyboard,
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { useContext } from "react";
import { AppContext } from "@/state/AppContext";
import { useState, useEffect } from "react";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AntDesign } from "@expo/vector-icons";
import Octicons from "@expo/vector-icons/Octicons";
import { supabase } from "@/lib/supabase";
import Feather from "@expo/vector-icons/Feather";
import HeaderV2 from "@/components/common/HeaderV2.native";
import React from "react";
import moment from "moment";
import { router } from "expo-router";

export default function InProgressActionsList() {
  const [actionsList, setActionsList] = useState<any>([]);
  const [loading, setLoading] = useState(true);
  const { user, companySelected } = useContext(AppContext);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  // Récupérer la liste des actions de l'entreprise
  useEffect(() => {
    const fetchActions = async () => {
      try {

        // Vérifiez que l'utilisateur a une entreprise associée
        const companyId = companySelected?.id;
        if (!companyId) {
          setActionsList([]); // Aucun document
          setLoading(false); // Terminer le chargement
          return;
        }

        // Requête pour récupérer les actions avec les relations jointes
        const { data, error } = await supabase
          .from("actions")
          .select(
            `
            *,
            action_administrators (user_id, users (first_name, last_name)),
            action_processes (process_id, process (process_name)),
            action_events (event_id, events (wording))
          `
          )
          .eq("company_id", companyId);

        if (error) {
          console.error(
            "Erreur lors de la récupération des actions :",
            error.message
          );
          return;
        }

        // Récupération de la date actuelle
        const currentDate = moment();

        // Filtrer les actions
        const inProgressActions = data.filter(
          (action) =>
            action.status === "not_achieved" &&
            moment(action.date).isAfter(currentDate) // Date après aujourd'hui = en cours
        )
        .map((action) => ({
          ...action,
          administrator: action.action_administrators?.[0]?.users || null,
          process: action.action_processes?.[0]?.process || null,
          event: action.action_events?.[0]?.events || null,
        }));

        setActionsList(inProgressActions);
      } catch (err) {
        console.error("Erreur inattendue :", err);
      } finally {
        setLoading(false); // Fin du chargement
      }
    };

    fetchActions();
  }, [user]);

  return (
    <>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View style={{ flex: 1, paddingHorizontal: 30 }}>
          {/* Header */}
        <View style={styles.header}>
          <Text style={{ fontWeight: "bold" }}>Actions en cours ({actionsList?.length})</Text>
          <TouchableOpacity
            onPress={() => router.navigate("/screens/actions/CreateAction")}
          >
            <Octicons name="diff-added" size={25} color="#F99527" />
          </TouchableOpacity>
        </View>

          {/* Liste des actions */}
          {loading ? (
            <Text>Chargement...</Text>
          ) : actionsList.length === 0 ? (
            <Text
              style={{ textAlign: "center", marginTop: 20, color: "#8c8c8c" }}
            >
              Aucune action.
            </Text>
          ) : (
            <FlatList
              data={actionsList}
              keyExtractor={(item) => item.id.toString()|| Math.random().toString()}
              renderItem={({ item }) => (
                <TouchableOpacity
                  onPress={() =>
                    navigation.navigate("screens/actions/ActionDetails", {
                      action: item,
                    })
                  }
                  style={styles.listItem}
                >
                  {/* 1ère ligne d'éléments */}
                  <View style={styles.elementsTop}>
                    <Text style={styles.listName}>{item.name.length > 20 ? item.name.slice(0, 20) + "..." : item.name}</Text>
                    <Text style={styles.date}>
                      {moment(item.date).format("DD/MM/YYYY")}
                    </Text>
                  </View>
                  {/* 2ème ligne d'éléments */}
                  <View style={styles.elementsMiddle}>
                    <Feather name="clock" size={24} color="#F99527" />
                  </View>
                  {/* 3ème ligne d'éléments */}
                  <View style={styles.elementsBottom}>
                    <Text style={styles.texts}>
                      Responsable :{" "}
                      {item.administrator
                        ? `${item.administrator.first_name} ${item.administrator.last_name}`
                        : "Non attribué"}
                    </Text>
                    <Text style={styles.texts}>
                      Processus :{" "}
                      {item.process ? item.process.process_name : "Non défini"}
                    </Text>
                  </View>
                </TouchableOpacity>
              )}
              contentContainerStyle={styles.listContainer}
            />
          )}
        </View>
      </TouchableWithoutFeedback>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 10,
    marginTop: 14,
  },
  headSection: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  backButton: {
    width: 24,
    textAlign: "left",
  },
  titleWrapper: {
    display: "flex",
    flexDirection: "row",
    marginVertical: 15,
  },
  title: {
    color: "#262627",
    fontSize: 16,
    textAlign: "left",
  },
  listContainer: {
    width: "100%",
    paddingVertical: 10,
  },
  listItem: {
    width: "100%",
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    display: "flex",
    justifyContent: "flex-start",
    flexDirection: "column",
    gap: 10,
    borderWidth: 1,
    borderColor: "#CCC",
  },
  listTitle: {
    fontSize: 16,
    color: "#1C3144",
    fontWeight: "bold",
  },
  listDescription: {
    fontSize: 14,
    color: "#1c3144",
    fontStyle: "normal",
    flexWrap: "wrap",
  },
  listDescription2: {
    width: "100%",
    fontSize: 14,
    color: "#1c3144",
    fontStyle: "normal",
    flexWrap: "wrap",
  },
  topElementsCard: {
    // width: "100%",
    backgroundColor: "#FFFFFF",
  },
  qseCardImage: {
    width: 90,
    height: 90,
    borderRadius: 7,
  },
  bottomElementsCard: {
    // width: "100%",
    backgroundColor: "#FFFFFF",
    borderRadius: 7,
    justifyContent: "space-between",
    flexDirection: "column",
    rowGap: 5,
  },
  searchContainer: {
    width: "100%",
    marginBottom: 20,
    marginTop: 10,
    paddingHorizontal: 10,
  },
  searchInputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1C314414",
    borderRadius: 8,
    paddingHorizontal: 10,
    height: 60,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    padding: 10,
    fontSize: 16,
    color: "#000000",
  },
  elementsTop: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  elementsMiddle: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "flex-end",
    textAlign: "right",
  },
  elementsBottom: {
    display: "flex",
    flexDirection: "column",
  },
  date: {
    fontSize: 14,
    color: "#525252",
    fontStyle: "normal",
    flexWrap: "wrap",
    fontWeight: "normal",
  },
  texts: {
    fontSize: 14,
    color: "#525252",
    fontStyle: "normal",
    flexWrap: "wrap",
    fontWeight: "normal",
  },
  listName: {
    fontSize: 15,
    color: "#262627",
    fontStyle: "normal",
    flexWrap: "wrap",
    fontWeight: "semibold",
  },
  sectionContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#1C3144",
    marginVertical: 10,
  },
  sectionButton: {},
  sectionButtonText: {
    fontSize: 14,
    textDecorationLine: "underline",
    color: "#F99527",
    marginVertical: 10,
  },
});
