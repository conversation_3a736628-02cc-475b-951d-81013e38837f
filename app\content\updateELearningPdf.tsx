import ContainedButton from "@/components/ui/buttons/ContainedButton";
import { supabase } from "@/lib/supabase";
import { AppContext } from "@/state/AppContext";
import TypeELearning from "@/types/typeELearning";
import { AntDesign } from "@expo/vector-icons";
import {
  router,
  useFocusEffect,
  useLocalSearchParams,
  usePathname,
} from "expo-router";
import { useCallback, useContext, useEffect, useState } from "react";
import { Dimensions, ScrollView, Platform, Linking, Alert, StyleSheet, View, Text, TextInput, TouchableOpacity, Image } from "react-native";
import { WebView } from 'react-native-webview';
import Header from "@/components/common/Header";
import { Controller, useForm } from "react-hook-form";
import Toast from "react-native-toast-message";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import * as DocumentPicker from "expo-document-picker";
import uploadDocumentCoverStorage from "@/lib/uploadDocumentCoverStorage";
import { generatePdfThumbnail } from '@/lib/generatePdfThumbnail';

export default function UpdateELearningPdf() {
  const [eLearningVideos, setELearningVideos] = useState<TypeELearning>();
  const { width } = Dimensions.get("window");
  const { user, companySelected } = useContext(AppContext);
  const [loading, setLoading] = useState(false);
  const [file, setFile] = useState<string | null>(null);
  const [currentFile, setCurrentFile] = useState<string | null>(null);
  const [thumbnailPreview, setThumbnailPreview] = useState<string | null>(null);
  const { idELearning, title, description, url_media, url_mignature, file_type, type_profil, url_video } = useLocalSearchParams();

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm({
    defaultValues: {
      title: title as string || "",
      description: description as string || "",
    },
  });

  const updateELearningIsShown = async () => {
    const { data, error } = await supabase
      .from("elearnings")
      .update({
        isShown: true,
      })
      .eq("id", idELearning)
      .eq("uid_user", user?.uid);
  };

  const fetchDataELearnings = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("elearnings")
        .select()
        .eq("uid_user", user?.uid)
        .eq("id", idELearning)
        .single();

      if (error) {
        console.error('Erreur lors de la récupération des données:', error);
        return;
      }

      if (data) {
        console.log('Données eLearning récupérées:', data);
        setELearningVideos(data as TypeELearning);
        reset({
          title: data.title,
          description: data.description,
        });
      } else {
        // Si pas de données dans la base, utiliser les paramètres de l'URL
        setELearningVideos({
          id: parseInt(idELearning as string),
          title: title as string,
          description: description as string,
          url_media: url_media as string,
          url_mignature: url_mignature as string,
          file_type: file_type as string,
          type_profil: JSON.parse(type_profil as string || '[]'),
          url_video: url_video as string,
          uid_user: user?.uid,
          created_at: new Date().toISOString(),
          company_id: companySelected?.id,
          isShown: false
        } as TypeELearning);
      }
    } catch (error) {
      console.error('Erreur inattendue:', error);
    } finally {
      setLoading(false);
    }
  };

  const recordView = async () => {
    if (!user?.uid || !idELearning) return;

    try {
      const { data: existingRecord } = await supabase
        .from("elearnings_students")
        .select()
        .eq("elearning_id", idELearning)
        .eq("user_id", user.uid)
        .single();

      if (existingRecord) {
        // Ne mettre à jour que si view_at n'existe pas
        if (!existingRecord.view_at) {
          const { error } = await supabase
            .from("elearnings_students")
            .update({ view_at: new Date().toISOString() })
            .eq("elearning_id", idELearning)
            .eq("user_id", user.uid);

          if (error) {
            console.error('Erreur lors de la mise à jour de la vue:', error);
          }
        }
      } else {
        // Créer un nouvel enregistrement
        const { error } = await supabase.from("elearnings_students").insert({
          elearning_id: idELearning,
          user_id: user.uid,
          view_at: new Date().toISOString(),
          company_id: companySelected?.id
        });

        if (error) {
          console.error('Erreur lors de l\'enregistrement de la vue:', error);
        }
      }
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement de la vue:', error);
    }
  };

  const recordClick = async () => {
    await supabase.from("elearnings_students").upsert({
      elearning_id: idELearning,
      user_id: user?.uid,
      click_at: new Date().toISOString(),
    }, {
      onConflict: 'elearning_id,user_id'
    });
  };

  // Récupérer les données au chargement initial
  useEffect(() => {
    if (idELearning && user?.uid) {
      fetchDataELearnings();
    }
  }, [idELearning, user?.uid]);

  // Récupérer les données à chaque focus de l'écran
  useFocusEffect(
    useCallback(() => {
      if (idELearning && user?.uid) {
        fetchDataELearnings();
        updateELearningIsShown();
        recordView();
      }
    }, [idELearning, user?.uid])
  );

  const handleVoirPlus = async () => {
    if (!eLearningVideos?.url_media) {
      Alert.alert("Erreur", "Impossible d'ouvrir le PDF");
      return;
    }

    if (!user?.uid || !idELearning) return;

    try {
      const { data: existingRecord } = await supabase
        .from("elearnings_students")
        .select()
        .eq("elearning_id", idELearning)
        .eq("user_id", user.uid)
        .single();

      if (existingRecord) {
        // Ne mettre à jour que si click_at n'existe pas
        if (!existingRecord.click_at) {
          const { error } = await supabase
            .from("elearnings_students")
            .update({ click_at: new Date().toISOString() })
            .eq("elearning_id", idELearning)
            .eq("user_id", user.uid);

          if (error) {
            console.error('Erreur lors de l\'enregistrement du clic:', error);
          }
        }
      } else {
        // Créer un nouvel enregistrement avec click_at
        const { error } = await supabase.from("elearnings_students").insert({
          elearning_id: idELearning,
          user_id: user.uid,
          click_at: new Date().toISOString(),
          company_id: companySelected?.id
        });

        if (error) {
          console.error('Erreur lors de l\'enregistrement du clic:', error);
        }
      }

      Linking.openURL(eLearningVideos.url_media);
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du clic:', error);
    }
  };

  // Initialiser currentFile avec le fichier du document
  useEffect(() => {
    if (eLearningVideos?.url_media) {
      setCurrentFile(eLearningVideos.url_media);
    }
  }, [eLearningVideos]);

  // Modifier useEffect pour définir la miniature au chargement
  useEffect(() => {
    if (eLearningVideos?.url_media) {
      setThumbnailPreview(eLearningVideos.url_media);
    }
  }, [eLearningVideos?.url_media]);

  // Modifier handleFileSelect pour définir la miniature lors de la sélection d'un nouveau fichier
  const handleFileSelect = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ["application/pdf"],
        copyToCacheDirectory: true,
      });

      if (result.canceled) {
        return;
      }

      const fileUri = result.assets[0].uri;
      setFile(fileUri);
      setCurrentFile(null);
      setThumbnailPreview(fileUri);
    } catch (error: any) {
      Toast.show({
        type: "error",
        text1: "Erreur lors de la sélection du fichier",
        text2: error.message,
      });
    }
  };

  const handleRemoveCurrentFile = async () => {
    try {
      if (!currentFile) return;

      // Extraire le chemin du fichier de l'URL
      const urlParts = currentFile.split('/');
      const publicIndex = urlParts.indexOf('public');
      if (publicIndex === -1) {
        throw new Error("Format d'URL invalide");
      }

      const bucket = urlParts[publicIndex + 1];
      const filePath = urlParts.slice(publicIndex + 2).join('/');

      console.log('Suppression du fichier:', { bucket, filePath });

      // Supprimer le fichier du stockage
      const { error: storageError } = await supabase
        .storage
        .from(bucket)
        .remove([filePath]);

      if (storageError) {
        console.error('Erreur lors de la suppression du fichier dans le stockage:', storageError);
        throw storageError;
      }

      // Au lieu de mettre à null, on met une chaîne vide
      const { error: dbError } = await supabase
        .from("elearnings")
        .update({ url_media: "" })
        .eq("id", idELearning)
        .eq("uid_user", user?.uid);

      if (dbError) {
        console.error('Erreur lors de la mise à jour de la base de données:', dbError);
        throw dbError;
      }

      setCurrentFile(null);
      setFile(null);
      
      Toast.show({
        type: "success",
        text1: "Fichier supprimé avec succès",
      });
    } catch (error: any) {
      console.error('Erreur lors de la suppression du fichier:', error);
      Toast.show({
        type: "error",
        text1: "Erreur lors de la suppression du fichier",
        text2: error.message || "Une erreur est survenue",
      });
    }
  };

  const handleDelete = async () => {
    try {
      setLoading(true);
      const { error } = await supabase
        .from("elearnings")
        .delete()
        .eq("id", idELearning);

      if (error) throw error;

      Toast.show({
        type: "success",
        text1: "PDF supprimé avec succès",
      });

      if (Platform.OS === 'web') {
        router.back();
        router.back();
      } else {
        router.back();
        router.back();
      }
    } catch (error: any) {
      Toast.show({
        type: "error",
        text1: "Erreur lors de la suppression",
        text2: error.message,
      });
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: any) => {
    try {
      setLoading(true);
      let fileUrl = currentFile || "";
      let thumbnailUrl = eLearningVideos?.url_mignature || "";

      if (file) {
        const response = await fetch(file);
        const blob = await response.blob();
        const arrayBuffer = await new Response(blob).arrayBuffer();

        // Générer un nom de fichier unique
        const fileName = `${user?.uid}_${Date.now()}_document.pdf`;

        // Upload du PDF
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from("images")
          .upload(`documents/${fileName}`, arrayBuffer, {
            contentType: "application/pdf",
            upsert: false,
          });

        if (uploadError) {
          console.error("Upload error:", uploadError);
          throw new Error("Erreur lors de l'upload du fichier");
        }

        fileUrl = supabase.storage
          .from("images")
          .getPublicUrl(`documents/${fileName}`)
          .data.publicUrl;

        // Utiliser le même PDF comme miniature
        thumbnailUrl = fileUrl;
      }

      console.log('Données à envoyer:', {
        title: data.title,
        description: data.description,
        url_media: fileUrl,
        url_mignature: thumbnailUrl,
        file_type: 'pdf',
        type_profil: eLearningVideos?.type_profil || [],
      });

      const { error } = await supabase
        .from("elearnings")
        .update({
          title: data.title,
          description: data.description,
          url_media: fileUrl,
          url_mignature: thumbnailUrl,
          file_type: 'pdf',
          type_profil: eLearningVideos?.type_profil || [],
        })
        .eq("id", idELearning)
        .eq("uid_user", user?.uid);

      if (error) {
        console.error('Erreur lors de la mise à jour:', error);
        Toast.show({
          type: "error",
          text1: "Erreur lors de la mise à jour",
          text2: error.message,
        });
        return;
      }

      Toast.show({
        type: "success",
        text1: "Succès",
        text2: "Le PDF a été mis à jour avec succès",
        visibilityTime: 2000,
        onHide: () => {
          router.back();
        }
      });
    } catch (error: any) {
      console.error('Erreur lors de la mise à jour:', error);
      Toast.show({
        type: "error",
        text1: "Erreur inattendue",
        text2: error.message || "Une erreur est survenue lors de la mise à jour",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.mainContainer}>
      <Header
        onPressIcon={() => router.back()}
      />
      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <View style={styles.content}>
          <View style={styles.formContainer}>
            <View style={styles.inputs}>
              <InputCustomized
                label="Titre"
                placeholder="Titre du PDF"
                name="title"
                control={control}
                errors={errors}
                setError={() => {}}
              />

              <InputCustomized
                label="Description"
                placeholder="Description du PDF"
                name="description"
                control={control}
                errors={errors}
                setError={() => {}}
                multiline
              />

              <View style={styles.fileContainer}>
                <Text style={styles.label}>Fichier actuel</Text>
                {currentFile ? (
                  <View style={styles.currentFile}>
                    <Text style={styles.fileName}>{eLearningVideos?.title}</Text>
                    <Text style={styles.fileInfo}>PDF</Text>
                    <TouchableOpacity
                      style={styles.removeFileButton}
                      onPress={handleRemoveCurrentFile}
                    >
                      <Text style={styles.removeFileButtonText}>Supprimer le fichier</Text>
                    </TouchableOpacity>
                  </View>
                ) : (
                  <Text style={styles.noFileText}>Aucun fichier</Text>
                )}
              </View>

              <View style={styles.fileContainer}>
                <Text style={styles.label}>Nouveau fichier (optionnel)</Text>
                <View style={styles.fileInput}>
                  <TextInput
                    style={styles.fileInputText}
                    value={file ? "Fichier sélectionné" : "Aucun fichier sélectionné"}
                    editable={false}
                  />
                  <TouchableOpacity
                    style={styles.fileButton}
                    onPress={handleFileSelect}
                  >
                    <Text style={styles.fileButtonText}>Sélectionner</Text>
                  </TouchableOpacity>
                </View>
              </View>

              {/* Aperçu du PDF */}
              <View style={styles.thumbnailContainer}>
                <Text style={styles.label}>Aperçu du PDF</Text>
                {(thumbnailPreview || file) ? (
                  Platform.OS === 'web' ? (
                    <iframe
                      src={file || thumbnailPreview || ''}
                      style={styles.thumbnail}
                      title="PDF Preview"
                    />
                  ) : (
                    <WebView
                      source={{ uri: file || thumbnailPreview || '' }}
                      style={styles.thumbnail}
                      javaScriptEnabled={true}
                      domStorageEnabled={true}
                    />
                  )
                ) : (
                  <View style={styles.noThumbnail}>
                    <Text style={styles.noThumbnailText}>Aucun PDF disponible</Text>
                  </View>
                )}
              </View>

              {Platform.OS === "web" && (file || currentFile) && (
                <View style={styles.previewContainer}>
                  <Text style={styles.label}>Aperçu du PDF</Text>
                  <iframe
                    src={file || currentFile || ''}
                    style={styles.preview}
                    title="PDF Preview"
                  />
                </View>
              )}
            </View>

            <View style={styles.buttons}>
              <ContainedButton
                label="Mettre à jour"
                backgroundColor="#F99527"
                onPress={handleSubmit(onSubmit)}
                disabled={loading}
              />
              <TouchableOpacity 
                style={styles.deleteButton}
                onPress={() => {
                  if (Platform.OS === 'web') {
                    if (window.confirm("Êtes-vous sûr de vouloir supprimer ce PDF ?")) {
                      handleDelete();
                    }
                  } else {
                    Alert.alert(
                      "Supprimer le PDF",
                      "Êtes-vous sûr de vouloir supprimer ce PDF ?",
                      [
                        {
                          text: "Annuler",
                          style: "cancel"
                        },
                        {
                          text: "Supprimer",
                          onPress: handleDelete,
                          style: "destructive"
                        }
                      ]
                    );
                  }
                }}
              >
                <Text style={styles.deleteButtonText}>Supprimer le PDF</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>
      <Toast />
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  contentContainer: {
    flexGrow: 1,
  },
  content: {
    gap: 20,
    padding: 20,
    paddingBottom: Platform.OS === 'web' ? 40 : 20,
  },
  formContainer: {
    width: "100%",
    gap: 20,
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
  fileContainer: {
    width: "100%",
    gap: 10,
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
  },
  currentFile: {
    padding: 15,
    backgroundColor: "#f5f5f5",
    borderRadius: 4,
    borderWidth: 1,
    borderColor: "#d3d3d3",
  },
  fileName: {
    fontSize: 16,
    color: "#262627",
    marginBottom: 5,
  },
  fileInfo: {
    fontSize: 14,
    color: "#666",
  },
  fileInput: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  fileInputText: {
    flex: 1,
    padding: 10,
    borderWidth: 1,
    borderColor: "#d3d3d3",
    borderRadius: 4,
    backgroundColor: "#f5f5f5",
  },
  fileButton: {
    backgroundColor: "#F99527",
    padding: 10,
    borderRadius: 4,
  },
  fileButtonText: {
    color: "#fff",
    fontWeight: "bold",
  },
  previewContainer: {
    width: "100%",
    marginTop: 20,
  },
  preview: {
    width: "100%",
    height: 500,
    borderRadius: 10,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  deleteButton: {
    backgroundColor: '#FF3B30',
    padding: 12,
    borderRadius: 7,
    alignItems: 'center',
    marginTop: 10,
  },
  deleteButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  removeFileButton: {
    backgroundColor: '#FF3B30',
    padding: 8,
    borderRadius: 4,
    marginTop: 10,
    alignSelf: 'flex-start',
  },
  removeFileButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  noFileText: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
  thumbnailContainer: {
    width: "100%",
    marginTop: 20,
    alignItems: 'center',
  },
  thumbnail: {
    width: 200,
    height: 280,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#d3d3d3',
  },
  noThumbnail: {
    width: 200,
    height: 280,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#d3d3d3',
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  noThumbnailText: {
    color: '#666',
    textAlign: 'center',
    padding: 10,
  },
});
