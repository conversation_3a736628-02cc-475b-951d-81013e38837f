import Toast from "react-native-toast-message";
import { supabase } from "./supabase.native";

export default async function uploadPhoto(
  fileUri: string,
  userId: string,
  nameBucket: string,
  path: string,
  contentType: string
) {
  try {
    console.log('Starting upload for:', { nameBucket, path, contentType });
    
    let arrayBuffer: ArrayBuffer;
    
    // Gérer les données base64
    if (fileUri.startsWith('data:')) {
      const base64Data = fileUri.split(',')[1];
      const binaryString = atob(base64Data);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      arrayBuffer = bytes.buffer;
    } else {
      // Gérer les URLs de fichiers
      const response = await fetch(fileUri);
      if (!response.ok) {
        throw new Error(`Failed to fetch file: ${response.statusText}`);
      }
      const blob = await response.blob();
      arrayBuffer = await new Response(blob).arrayBuffer();
    }

    console.log('File data prepared, size:', arrayBuffer.byteLength);

    // Générer un nom de fichier unique
    const fileName = `${userId}_${Date.now()}_${nameBucket}`;
    const filePath = `${path}/${fileName}`.replace(/\/+/g, '/');
    console.log('Uploading to path:', filePath);

    const { data: dataPicture, error: uploadError } = await supabase.storage
      .from(nameBucket)
      .upload(filePath, arrayBuffer, {
        contentType: contentType,
        upsert: false,
      });

    if (uploadError) {
      console.error("Upload error:", uploadError);
      throw new Error(`Upload failed: ${uploadError.message}`);
    }

    if (!dataPicture?.path) {
      throw new Error("Upload succeeded but no path returned");
    }

    console.log('File uploaded successfully:', dataPicture.path);

    // Générer une URL publique
    const { data: publicUrlData } = await supabase.storage
      .from(nameBucket)
      .getPublicUrl(filePath);

    if (!publicUrlData?.publicUrl) {
      throw new Error("Failed to get public URL");
    }

    console.log('Public URL generated:', publicUrlData.publicUrl);

    return { 
      url: publicUrlData.publicUrl, 
      idPicture: dataPicture.path 
    };

  } catch (err) {
    console.error("Error uploading file:", err);
    Toast.show({
      type: "error",
      text1: "Une erreur est survenue lors de l'upload",
      text2: err instanceof Error ? err.message : "Erreur inconnue",
      text1Style: { color: "#1C3144" },
      text2Style: { color: "#1C3144" },
    });
    return null;
  }
}
