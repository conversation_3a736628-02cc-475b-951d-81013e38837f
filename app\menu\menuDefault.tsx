import React, { useContext } from "react";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import {
  EvilIcons,
  Ionicons,
  MaterialCommunityIcons,
} from "@expo/vector-icons";
import FontAwesome6 from "@expo/vector-icons/FontAwesome6";
import Event from "./itemsMenuDefault/event";
import Audit from "./itemsMenuDefault/audit";
import AntDesign from "@expo/vector-icons/AntDesign";
import HomeScreen from "./home";
import ActionList from "./itemsMenuDefault/action";
import DocumentList from "./itemsMenuDefault/doc";
import { View } from "react-native";
import Toast from "react-native-toast-message";
import Header from "@/components/common/Header";
import Equipment from "./itemsMenuDefault/equipment";
import { AppContext } from "@/state/AppContext";

const Tab = createBottomTabNavigator();

export default function MenuDefault() {
  const { user } = useContext(AppContext);

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ color, size }) => {
          if (route.name === "Accueil") {
            return <AntDesign name="home" size={24} color={color} />;
          } else if (route.name === "Evenements") {
            return <EvilIcons name="calendar" size={35} color={color} />;
          } else if (route.name === "Audit") {
            return <Ionicons name="ear-outline" size={24} color={color} />;
          } else if (route.name === "Equipements") {
            return (
              <FontAwesome6 name="helmet-safety" size={24} color={color} />
            );
          } else if (route.name === "Action") {
            return (
              <MaterialCommunityIcons name="run-fast" size={24} color={color} />
            );
          } else if (route.name === "Documents") {
            return (
              <Ionicons name="documents-outline" size={24} color={color} />
            );
          }
        },
        tabBarActiveTintColor: "#F99527",
        tabBarInactiveTintColor: "#1c3144",
        headerShown: false, // Cacher le header
      })}
    >
      <Tab.Screen name="Accueil" component={HomeScreen} />
      <Tab.Screen name="Evenements" component={Event} />
      <Tab.Screen
        name="Documents"
        component={DocumentList}
        options={{
          headerShown: true,
          header: () => (
            <View>
              <Header />
              <Toast />
            </View>
          ),
        }}
      />
      {user?.status === "Admin" && (
        <Tab.Screen
          name="Equipements"
          component={Equipment}
          options={{
            headerShown: false,
          }}
        />
      )}
      {user?.status === "Admin" && (
        <Tab.Screen
          name="Audit"
          component={Audit}
          options={{
            headerShown: true,
            header: () => (
              <View>
                <Header />
                <Toast />
              </View>
            ),
          }}
        />
      )}
      <Tab.Screen name="Action" component={ActionList} />
    </Tab.Navigator>
  );
}
