/*
app/menu/itemsMenuCockpit/info.tsx

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `Supplier` n'ont pas accès à cette page.
*/

import { <PERSON><PERSON>, <PERSON><PERSON>View, StyleSheet, View, Text, Platform } from "react-native";
import ProcessInfosSection from "@/components/infoSection/process";
import ActivitiesInfosSection from "@/components/infoSection/activities";
import { useCallback, useContext, useEffect, useState } from "react";
import { AppContext } from "@/state/AppContext";
import typeUserByAdmin from "@/types/typeUserByAdmin";
import { supabase } from "@/lib/supabase";
import TypeProcess from "@/types/typeProcess";
import TypeActivity from "@/types/typeActivity";
import UsersInfosSection from "@/components/infoSection/users";
import { useFocusEffect } from "expo-router";
import ContextsInfoSection from "@/components/infoSection/context";
import IssuesInfoSection from "@/components/infoSection/issue";
import TypePolicy from "@/types/typePolicies";
import PoliciesInfoSection from "@/components/infoSection/policy";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import AccordionSection from "@/components/AccordionSection";
import { useRouter } from "expo-router";
import PaperCompany from "@/components/common/paperCompany";
import TypeContext from "@/types/typeContext";
import TypeIssue from "@/types/typeIssue";

export default function Info() {
  const [usersAdmin, setUsersAdmin] = useState<typeUserByAdmin[]>();
  const [process, setProcess] = useState<TypeProcess[]>();
  const [activites, setActivties] = useState<TypeActivity[]>();
  const [contexts, setContexts] = useState<TypeContext[]>();
  const [issues, setIssues] = useState<TypeIssue[]>();
  const [policies, setPolicies] = useState<TypePolicy[]>();
  const { user, companySelected } = useContext(AppContext);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const router = useRouter();

  const fetchDataUsers = async () => {
    const { data, error } = await supabase
      .from("company_users")
      .select(`
        user_id,
        users: user_id (
          id, uid, uid_admin, created_at, first_name, last_name, profil_picture, is_active, email, profil, status
        )
      `)
      .eq("company_id", companySelected?.id);
  
    if (error) {
      console.error("Erreur récupération utilisateurs :", error);
      return;
    }
  
    const usersList = data.map((entry: any) => entry.users).flat(); 
  
    setUsersAdmin(usersList);
  };

  const fetchDataProcess = async () => {
    const { data, error } = await supabase
      .from("process")
      .select("*, companies(*), users(*)s")
      .eq("uid_admin", user?.uid);

    if (!error) {
      setProcess(data as any);
    }
  };

  const fetchDataActivities = async () => {
    const { data, error } = await supabase
      .from("activities")
      .select()
      .eq("uid_admin", user?.uid);

    if (!error) {
      setActivties(data as any);
    }
  };

  // Récupérer le(s) contexte(s)
  const fetchDataContexts = async () => {
    const { data, error } = await supabase
      .from("contexts")
      .select()
      .eq("uid_admin", user?.uid);

    if (!error) {
      setContexts(data as any);
    }
  };

  // Récupérer les enjeux
  const fetchDataIssues = async () => {
    const { data, error } = await supabase
      .from("issues")
      .select()
      .eq("uid_admin", user?.uid);

    if (!error) {
      setIssues(data as any);
    }
  };

  // Récupérer les politiques
  const fetchDataPolicies = async () => {
    const { data, error } = await supabase
      .from("policies")
      .select()
      .eq("uid_admin", user?.uid);

    if (!error) {
      setPolicies(data as any);
    }
  };

  // Récupérer toutes les données
  useFocusEffect(
    useCallback(() => {
      fetchDataUsers();
      fetchDataProcess();
      fetchDataActivities();
      fetchDataContexts();
      fetchDataIssues();
      fetchDataPolicies();
    }, [])
  );

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["User", "Supplier"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  return (
    <View style={styles.mainContainer}>
      <ScrollView style={styles.container}>
        <View style={styles.content}>
          <AccordionSection 
            title="Contexte" 
            onEdit={() => router.push("/info/updateContext")}
            hasContent={contexts && contexts.length > 0}
          >
            {contexts && contexts.length > 0 ? (
              contexts.map((item, index) => (
                <View key={index} style={styles.itemContainer}>
                  <Text style={styles.itemTitle}>Contexte</Text>
                  <Text style={styles.itemText}>{item.context}</Text>
                  
                  <Text style={styles.itemTitle}>Organisation</Text>
                  <Text style={styles.itemText}>{item.organisation}</Text>
                  
                  <Text style={styles.itemTitle}>Site Web</Text>
                  <Text style={styles.itemText}>{item.website}</Text>
                  
                  <Text style={styles.itemTitle}>Historique</Text>
                  <Text style={styles.itemText}>{item.history}</Text>
                </View>
              ))
            ) : (
              <Text style={styles.emptyText}>Aucun contexte d'entreprise.</Text>
            )}
          </AccordionSection>

          <AccordionSection 
            title="Enjeux" 
            onEdit={() => router.push("/info/updateIssue")}
            hasContent={issues && issues.length > 0}
          >
            {issues && issues.length > 0 ? (
              issues.map((item, index) => (
                <View key={index} style={styles.itemContainer}>
                  <Text style={styles.itemTitle}>Description</Text>
                  <Text style={styles.itemText}>{item.context}</Text>
                </View>
              ))
            ) : (
              <Text style={styles.emptyText}>Aucun enjeu d'entreprise.</Text>
            )}
          </AccordionSection>

          <AccordionSection 
            title="Politiques" 
            onEdit={() => router.push("/info/updatePolicy")}
            hasContent={policies && policies.length > 0}
          >
            {policies && policies.length > 0 ? (
              policies.map((item, index) => (
                <View key={index} style={styles.itemContainer}>
                  <Text style={styles.itemTitle}>Description</Text>
                  <Text style={styles.itemText}>{item.context}</Text>
                </View>
              ))
            ) : (
              <Text style={styles.emptyText}>Aucune politique d'entreprise.</Text>
            )}
          </AccordionSection>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  content: {
    gap: 16,
    padding: 16,
  },
  itemContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  itemTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: "#1C3144",
    marginBottom: 4,
    marginTop: 8,
  },
  itemText: {
    fontSize: 14,
    color: "#525252",
    marginBottom: 4,
    lineHeight: 20,
  },
  emptyText: {
    fontSize: 14,
    color: '#525252',
    textAlign: 'center',
    padding: 20,
  },
});
