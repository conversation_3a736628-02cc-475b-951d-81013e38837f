/*
app/objectif/createSupplier.tsx

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern` et `Extern` n'ont pas accès à cette page.
*/

import ContainedButton from "@/components/ui/buttons/ContainedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useRouter } from "expo-router";
import { StyleSheet, Image, View, ScrollView, Alert, Platform } from "react-native";
import { useContext, useEffect } from "react";
import { useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import CustomizedSelect from "@/components/ui/customizedSelect/customizedSelect";
import TypeSupplier from "@/types/typeSupplier";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { generateSecurePassword } from "@/lib/utils";

const supplierStatus = [
  { value: "good", label: "Bon" },
  { value: "regular", label: "Moyen" },
  { value: "bad", label: "Mauvais" },
];

const inputs = [
  {
    label: "Nom de l'entreprise",
    name: "company_name",
    placeholder: "Nom de l'entreprise du fournisseur",
    type: "text",
  },
  {
    label: "Nom",
    name: "last_name",
    placeholder: "Nom de famille du fournisseur",
    type: "text",
  },
  {
    label: "Prénom",
    name: "first_name",
    placeholder: "Prénom du fournisseur",
    type: "text",
  },
  {
    label: "Email",
    name: "email",
    placeholder: "Email du fournisseur",
    type: "text",
  },
  {
    label: "Numéro de téléphone",
    name: "phone_number",
    placeholder: "Numéro de téléphone du fournisseur",
    type: "text",
  },
  {
    label: "Statut",
    name: "status",
    placeholder: "Statut du fournisseur",
    type: "Select",
    data: supplierStatus,
  },
];

export default function CreateSupplier() {
  const { user, companySelected } = useContext(AppContext);
  const router = useRouter();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern", "Supplier"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
    control,
    reset,
  } = useForm<TypeSupplier>({
    defaultValues: {
      company_name: "",
      last_name: "",
      first_name: "",
      email: "",
      phone_number: "",
      status: "",
    },
  });

  const handleCreateSupplier = async (data: TypeSupplier) => {
    // Vérification des champs obligatoires
    if (
      !data.company_name ||
      !data.last_name ||
      !data.first_name ||
      !data.email ||
      !data.status
    ) {
      Toast.show({
        type: "error",
        text1: "Tous les champs sont obligatoires",
        text1Style: { color: "#1C3144" },
      });
      return;
    }

    try {
      // Étape 1: Créer l'utilisateur dans Supabase Auth
      const { data: authData, error: signUpError } = await supabase.auth.signUp({
        email: data.email,
        password: Math.random().toString(36).slice(-12)
      });

      if (signUpError || !authData.user) {
        console.error("Erreur création auth user:", signUpError);
        Toast.show({
          type: "error",
          text1: "Erreur lors de la création du compte",
          text1Style: { color: "#1C3144" },
        });
        return;
      }

      // Étape 2: Créer l'utilisateur dans la table users
      const { error: userError } = await supabase
        .from("users")
        .insert({
          uid: authData.user.id,
          uid_admin: user?.uid,
          first_name: data.first_name,
          last_name: data.last_name,
          email: data.email,
          is_active: false,
          status: "Supplier",
          profil: "Fournisseur",
          phone_number: data.phone_number,
        });

      if (userError) {
        console.error("Erreur insertion users :", userError);
        Toast.show({
          type: "error",
          text1: "Erreur lors de l'ajout de l'utilisateur",
          text1Style: { color: "#1C3144" },
        });
        return;
      }

      // Étape 3: Créer l'entrée dans la table suppliers
      const { error: supplierError } = await supabase
        .from("suppliers")
        .insert({
          uid_user: authData.user.id,
          company_name: data.company_name,
          status: data.status,
          created_at: new Date()
        });

      if (supplierError) {
        console.error("Erreur lors de l'ajout du fournisseur :", supplierError);
        Toast.show({
          type: "error",
          text1: "Erreur lors de la création du fournisseur",
          text1Style: { color: "#1C3144" },
        });
        return;
      }

      // Étape 4: Créer le lien dans company_users
      const { error: companyUserError } = await supabase
        .from("company_users")
        .insert({
          company_id: companySelected?.id,
          user_id: authData.user.id,
          is_active: false,
        });

      if (companyUserError) {
        console.error("Erreur insertion company_users :", companyUserError);
        Toast.show({
          type: "error",
          text1: "Erreur lors de l'affectation à l'entreprise",
          text1Style: { color: "#1C3144" },
        });
        return;
      }

      Toast.show({
        type: "success",
        text1: "Fournisseur créé avec succès",
        text1Style: { color: "#1C3144" },
      });

      router.back();
      reset();
    } catch (err) {
      console.error("Erreur inattendue :", err);
      Toast.show({
        type: "error",
        text1: "Une erreur est survenue",
        text1Style: { color: "#1C3144" },
      });
    }
  };

  return (
    <View style={styles.mainContainer}>
      <ScrollView style={styles.container}>
        <View style={styles.containerContent}>
          <View style={styles.registerInfo}>
            <View style={styles.inputs}>
              {inputs.map((input, index) => {
                if (input?.type === "Select") {
                  return (
                    <View key={index}>
                      <CustomizedSelect
                        name={input.name}
                        label={input.label}
                        register={register}
                        control={control}
                        errors={errors}
                        setError={setError}
                        data={input.data as any}
                      />
                    </View>
                  );
                } else {
                  return (
                    <View key={index}>
                      <InputCustomized
                        label={input.label}
                        placeholder={input.placeholder}
                        register={register}
                        name={input.name}
                        control={control}
                        errors={errors}
                        setError={setError}
                      />
                    </View>
                  );
                }
              })}
            </View>
          </View>

          <View style={styles.buttons}>
            <ContainedButton
              label="Créer"
              backgroundColor="#F99527"
              onPress={handleSubmit(handleCreateSupplier)}
            />
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  containerContent: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    width: "100%",
    gap: 20,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
    marginTop: 20,
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
});
