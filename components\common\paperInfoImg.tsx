import React, { useContext } from "react";
import { View, Text, StyleSheet, Image, TouchableOpacity } from "react-native";
import { Badge, BadgeText } from "../ui/badge";
import FontAwesome5 from "@expo/vector-icons/FontAwesome5";
import { MaterialIcons } from "@expo/vector-icons";

type typePropPaper = {
  imgSrc?: string;
  title?: string;
  badge?: string;
  text1?: string;
  text2?: string;
  text3?: string;
  text4?: string;
  fleshShow?: boolean;
  onPress?: any;
  badgeColor: string;
};

const PaperInfoImg = ({
  imgSrc = "",
  title,
  badge,
  text1,
  text2,
  text3,
  text4,
  fleshShow = true,
  onPress,
  badgeColor,
}: typePropPaper) => {
  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <View style={styles.containerImgInfo}>
        {imgSrc ? (
          <Image source={{ uri: imgSrc }} style={styles.img} />
        ) : (
          <MaterialIcons name="account-circle" size={80} color="#1C3144" />
        )}
        <View style={styles.containerInfo}>
          <Text style={{ fontWeight: "bold" }}>
            {(title ?? "").length > 40
              ? (title ?? "").slice(0, 40) + "..."
              : title}
          </Text>
          {badge && (
            <Badge
              size="md"
              variant="solid"
              style={{
                backgroundColor: badgeColor ? badgeColor : "#F99527",
                borderRadius: 48,
                alignSelf: "flex-start", // 🔥 Permet au badge de s'adapter à son contenu
              }}
            >
              <BadgeText style={{ color: "white", fontSize: 11 }}>{badge}</BadgeText>
            </Badge>
          )}
          {text1 && (
            <Text style={{ color: "#525252", fontSize: 11 }}>
              {text1.length > 40 ? text1.slice(0, 40) + "..." : text1}
            </Text>
          )}
          {text2 && (
            <Text style={{ color: "#525252", fontSize: 11 }}>
              {text2.length > 40 ? text2.slice(0, 40) + "..." : text2}
            </Text>
          )}
          {text3 && (
            <Text style={{ color: "#525252", fontSize: 11 }}>
              {text3.length > 40 ? text3.slice(0, 40) + "..." : text3}
            </Text>
          )}
          {text4 && (
            <Text style={{ color: "#525252", fontSize: 11 }}>
              {text4.length > 40 ? text4.slice(0, 40) + "..." : text4}
            </Text>
          )}
        </View>
      </View>
      {fleshShow && (
        <FontAwesome5 name="chevron-right" size={30} color="black" />
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: "#E6E6E6",
    borderRadius: 10,
    // width: "95%",
    // Ombre pour iOS
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 5,
    // Ombre pour Android
    elevation: 2,
    margin: 2,
  },
  img: {
    width: 80,
    height: 80,
    borderRadius: 10,
  },
  containerImgInfo: {
    gap: 30,
    flexDirection: "row",
    padding: 10,
    alignItems: "center",
    width: "50%",
  },
  containerInfo: {
    gap: 5,
  },
});

export default PaperInfoImg;
