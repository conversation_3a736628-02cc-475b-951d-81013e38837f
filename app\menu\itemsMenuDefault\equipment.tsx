/*
app/menu/itemsMenuDefault/equipment.tsx

Liste des équipements.

- On récupère les équipements de l'entreprise depuis la table `equipments`

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Extern`, `Equipment` n'ont pas accès à cette page.
*/

import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Alert,
  FlatList,
  ScrollView,
  BackHandler,
  TextInput,
  Platform,
} from "react-native";
import { useRouter } from "expo-router";
import {
  ParamListBase,
  useFocusEffect,
  useNavigation,
} from "@react-navigation/native";
import React, { useCallback, useContext, useEffect, useState } from "react";
import { supabase } from "@/lib/supabase";
import { AppContext } from "@/state/AppContext";
import TypeEquipment from "@/types/typeEquipment";
import { Octicons } from "@expo/vector-icons";
import FilterPage from "@/components/common/filterPage";
import Header from "@/components/common/Header";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import moment from "moment";
import { Spinner } from "@/components/ui/spinner";
import PaperInfoImg from "@/components/common/paperInfoImg";
import PaperInfo from "@/components/common/paperInfo";
import AsyncStorage from "@react-native-async-storage/async-storage";
import FontAwesome from "@expo/vector-icons/FontAwesome";
import { Dropdown } from "react-native-element-dropdown";

const filterOptions = [
  { label: "Conforme", value: "compliant" },
  { label: "À vérifier", value: "to_be_checked" },
  { label: "En cours", value: "under_verification" },
  { label: "Hors service", value: "out_of_service" },
];

const subFilterOptions = [
  { label: "En retard", value: "late" },
  { label: "À venir", value: "upcoming" },
];

export default function Equipment() {
  const router = useRouter();
  const [data, setData] = useState<TypeEquipment[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { user, companySelected } = useContext(AppContext);
  const [showFilter, setShowFilter] = useState(false);
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
  const [selectedSubFilters, setSelectedSubFilters] = useState<string[]>([]);
  const [selectedUser, setSelectedUser] = useState<string>("");
  const [companyUsers, setCompanyUsers] = useState<Array<{id: string, full_name: string}>>([]);
  const [filteredData, setFilteredData] = useState<TypeEquipment[]>([]);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  // Restrictions utilisateur
  useEffect(() => {
    if (!user) return;

    // Redirection pour les rôles interdits
    if (!["Admin"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      if (router.canGoBack()) {
        router.back();
      } else {
        router.replace("/menu/home");
      }
    }
  }, [user]);

  // Récupérer la liste des Equipments de l'entreprise
  const fetchData = async () => {
    setIsLoading(true);

    let query = supabase
      .from("equipments")
      .select("*, users(first_name, last_name)")
      .eq("company_id", companySelected?.id);

    // Si l'utilisateur n'est pas Admin, on ne récupère que ses équipements
    if (user?.status !== "Admin") {
      query = query.eq("user_id", user?.uid);
    }

    const { data: dataGetted, error } = await query;

    if (!error) {
      // Trier les équipements par date de vérification
      const sortedData = [...dataGetted].sort((a, b) => {
        // Si l'un des équipements n'a pas de date de vérification, le mettre à la fin
        if (!a.verification_date) return 1;
        if (!b.verification_date) return -1;

        const dateA = new Date(a.verification_date);
        const dateB = new Date(b.verification_date);
        const today = new Date();

        // Calculer la différence en jours entre la date de vérification et aujourd'hui
        const diffA = Math.floor((dateA.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
        const diffB = Math.floor((dateB.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

        // Trier par ordre croissant (les dates les plus proches en premier)
        return diffA - diffB;
      });

      setData(sortedData);
    }
    setIsLoading(false);
  };

  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [])
  );

  // Ajouter un listener pour les changements en temps réel
  useEffect(() => {
    if (!companySelected?.id) return;

    const channel = supabase
      .channel('equipments-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'equipments',
          filter: `company_id=eq.${companySelected.id}`
        },
        (payload: { new: TypeEquipment; old: TypeEquipment; eventType: string }) => {
          console.log('Changement détecté dans equipments:', payload);
          fetchData(); // Rafraîchir les données
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [companySelected?.id]);

  console.log(data);

  useEffect(() => {
    if (!companySelected?.id) {
      console.error("Aucun company_id défini, requête annulée !");
      return;
    }

    fetchData();

    // Écoute les changements en temps réel et rafraîchit la liste
    const channel = supabase
      .channel("equipments-changes")
      .on(
        "postgres_changes",
        { event: "*", schema: "public", table: "equipments" },
        (payload: any) => {
          console.log("Changement détecté dans équipments :", payload);
          setRefreshKey((prev) => prev + 1);
        }
      )
      .subscribe(async (status: boolean) => {
        if (status === true) {
          console.log("Abonné aux changements de la table `equipments` !");
        }
      });

    return () => {
      // Nettoyer l'écouteur lorsqu'on quitte la page
      supabase.removeChannel(channel);
    };
  }, [companySelected, refreshKey]);

  // Vérifier et mettre à jour le statut des équipements
  useEffect(() => {
    const checkAndUpdateEquipmentStatus = async () => {
      if (!companySelected?.id) return;

      const today = new Date();
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(today.getDate() + 30);

      const { data: equipments, error } = await supabase
        .from("equipments")
        .select("*")
        .eq("company_id", companySelected.id);

      if (error) {
        console.error("Erreur lors de la récupération des équipements:", error);
        return;
      }

      // Filtrer les équipements qui nécessitent une mise à jour
      const equipmentsToUpdate = equipments.filter((equipment: TypeEquipment) => {
        if (!equipment.verification_date) return false;
        const verificationDate = new Date(equipment.verification_date);
        return (verificationDate < today || 
                (verificationDate >= today && verificationDate <= thirtyDaysFromNow)) && 
               equipment.status === "compliant";
      });

      // Mettre à jour le statut des équipements concernés
      for (const equipment of equipmentsToUpdate) {
        const { error: updateError } = await supabase
          .from("equipments")
          .update({ status: "to_be_checked" })
          .eq("uuid", equipment.uuid);

        if (updateError) {
          console.error("Erreur lors de la mise à jour du statut:", updateError);
        }
      }

      // Si des mises à jour ont été effectuées, rafraîchir la liste
      if (equipmentsToUpdate.length > 0) {
        setRefreshKey((prev) => prev + 1);
      }
    };

    // Vérifier toutes les heures
    const interval = setInterval(checkAndUpdateEquipmentStatus, 3600000);
    
    // Vérifier immédiatement au chargement
    checkAndUpdateEquipmentStatus();

    return () => clearInterval(interval);
  }, [companySelected]);

  // Gestion des types
  const getEquipmentsTypes = (type: string) => {
    switch (type?.toLowerCase()) {
      case "cyclic":
        return { title: "Cyclique" };
      case "custom":
        return { title: "Personnalisé" };
      default:
        return { title: "Cyclique" };
    }
  };

  // Gestion des statuts
  const getEquipmentsStatusStyle = (status: string | undefined, verificationDate: string | undefined) => {
    console.log("Status reçu:", status); // Debug log
    if (!status) return { text: "Conforme", backgroundColor: "#2FC12B" };

    // Vérifier si la date de vérification est dépassée
    if (verificationDate) {
      const today = new Date();
      const verificationDateObj = new Date(verificationDate);
      if (verificationDateObj < today && status === "compliant") {
        return { text: "À vérifier", backgroundColor: "#F99527" };
      }
    }
    
    switch (status) {
      case "compliant":
        return { text: "Conforme", backgroundColor: "#2FC12B" };
      case "to_be_checked":
        return { text: "À vérifier", backgroundColor: "#F99527" };
      case "under_verification":
        return { text: "En cours de vérification", backgroundColor: "#277FF9" };
      case "out_of_service":
        return { text: "Hors service", backgroundColor: "#737373" };
      default:
        return { text: "Conforme", backgroundColor: "#2FC12B" };
    }
  };

  const filterLabels = {
    compliantStatus: "Conforme",
    toBeCheckedStatus: "À vérifier",
    underVerificationStatus: "En cours de vérification",
    outOfServiceStatus: "Hors service",
  };

  // Récupérer les utilisateurs de l'entreprise
  useEffect(() => {
    const fetchCompanyUsers = async () => {
      if (!companySelected?.id) return;

      const { data, error } = await supabase
        .from("company_users")
        .select("user_id, users (first_name, last_name, status)")
        .eq("company_id", companySelected.id)
        .neq("users.status", "Supplier");

      if (error) {
        console.error("Erreur lors de la récupération des utilisateurs :", error);
        return;
      }

      // Filtrer les entrées où users est null et formater les données
      const formattedUsers = data
        .filter((entry: any) => entry.users && entry.users.first_name && entry.users.last_name)
        .map((entry: any) => ({
          id: entry.user_id,
          full_name: `${entry.users.first_name} ${entry.users.last_name}`,
        }));

      setCompanyUsers(formattedUsers);
    };

    fetchCompanyUsers();
  }, [companySelected]);

  // Mettre à jour la liste filtrée en fonction de la recherche et des filtres
  useEffect(() => {
    let filtered = data;
    
    // Filtre par recherche
    if (searchQuery) {
      filtered = filtered.filter((item) =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.reference?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    
    // Filtre par statut
    if (selectedFilters.length > 0) {
      filtered = filtered.filter((item) => 
        item.status && selectedFilters.includes(item.status)
      );

      // Si "À vérifier" est sélectionné et qu'il y a des sous-filtres
      if (selectedFilters.includes("to_be_checked") && selectedSubFilters.length > 0) {
        const today = new Date();
        filtered = filtered.filter((item) => {
          if (!item.verification_date) return false;
          const verificationDate = new Date(item.verification_date);
          
          if (selectedSubFilters.includes("late") && verificationDate < today) {
            return true;
          }
          
          if (selectedSubFilters.includes("upcoming")) {
            const thirtyDaysFromNow = new Date();
            thirtyDaysFromNow.setDate(today.getDate() + 30);
            return verificationDate >= today && verificationDate <= thirtyDaysFromNow;
          }
          
          return false;
        });
      }
    }

    // Filtre par utilisateur
    if (selectedUser) {
      filtered = filtered.filter((item) => 
        item.user_id === selectedUser
      );
    }
    
    setFilteredData(filtered);
  }, [searchQuery, selectedFilters, selectedSubFilters, selectedUser, data]);

  const toggleFilter = (value: string) => {
    setSelectedFilters((prevSelected) => {
      const newFilters = prevSelected.includes(value)
        ? prevSelected.filter((item) => item !== value)
        : [...prevSelected, value];
      
      // Si on désélectionne "À vérifier", on réinitialise les sous-filtres
      if (!newFilters.includes("to_be_checked")) {
        setSelectedSubFilters([]);
      }
      
      return newFilters;
    });
  };

  const toggleSubFilter = (value: string) => {
    setSelectedSubFilters((prevSelected) =>
      prevSelected.includes(value)
        ? prevSelected.filter((item) => item !== value)
        : [...prevSelected, value]
    );
  };

  // Réinitialiser l'état du filtre lors du retour sur la page
  useFocusEffect(
    useCallback(() => {
      const resetFilterState = async () => {
        try {
          await AsyncStorage.removeItem('equipmentFilterState');
          setShowFilter(false);
        } catch (error) {
          console.error('Erreur lors de la réinitialisation du filtre:', error);
        }
      };
      resetFilterState();
    }, [])
  );

  // Sauvegarder l'état du filtre
  const saveFilterState = async (state: boolean) => {
    try {
      await AsyncStorage.setItem('equipmentFilterState', JSON.stringify(state));
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du filtre:', error);
    }
  };

  // Gestion du bouton retour
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if (showFilter) {
        setShowFilter(false);
        return true; // Empêche le retour à la page précédente
      }
      return false; // Permet le retour à la page précédente
    });

    return () => backHandler.remove();
  }, [showFilter]);

  // Fonction pour générer le texte résumant les filtres actifs
  const getActiveFiltersText = () => {
    const parts = [];
    
    if (selectedFilters.length > 0) {
      const statusLabels = selectedFilters.map(
        value => filterOptions.find(opt => opt.value === value)?.label
      ).filter(Boolean);
      parts.push(`Statut: ${statusLabels.join(", ")}`);
    }
    
    if (selectedSubFilters.length > 0) {
      const subLabels = selectedSubFilters.map(
        value => subFilterOptions.find(opt => opt.value === value)?.label
      ).filter(Boolean);
      parts.push(`Type: ${subLabels.join(", ")}`);
    }
    
    if (selectedUser) {
      const userName = companyUsers.find(u => u.id === selectedUser)?.full_name;
      if (userName) parts.push(`Détenteur: ${userName}`);
    }
    
    return parts.length > 0 ? parts.join(" | ") : "Aucun filtre actif";
  };

  return (
    <View style={styles.mainContainer}>
      <Header
        title="Équipements"
        onPressFlesh={() => {
          if (router.canGoBack()) {
            router.back();
          } else {
            router.replace("/");
          }
        }}
      />
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollViewContent}>
        <View style={styles.container}>
          <View style={styles.header}>
            <Text style={{ fontWeight: "bold" }}>Créer un nouvel EPI:</Text>
            <TouchableOpacity
              onPress={() => router.navigate("/equipment/createEquipment")}
            >
              <Octicons name="diff-added" size={25} color="#F99527" />
            </TouchableOpacity>
          </View>

          {/* Barre de recherche */}
          <View style={styles.searchContainer}>
            <View style={styles.searchInputWrapper}>
              <FontAwesome
                name="search"
                size={24}
                color="#3C3c4399"
                style={styles.searchIcon}
              />
              <TextInput
                style={styles.searchInput}
                placeholder="Rechercher par nom ou référence..."
                placeholderTextColor="#3C3c4399"
                value={searchQuery}
                onChangeText={(text) => setSearchQuery(text)}
              />
            </View>
          </View>

          {/* Panneau de filtres déroulant */}
          <View style={styles.filterPanelContainer}>
            <TouchableOpacity 
              style={styles.filterPanelHeader}
              onPress={() => setIsFilterPanelOpen(!isFilterPanelOpen)}
            >
              <View style={styles.filterPanelHeaderContent}>
                <Text style={styles.filterPanelTitle}>Filtres</Text>
                <Text style={styles.activeFiltersText}>{getActiveFiltersText()}</Text>
              </View>
              <FontAwesome 
                name={isFilterPanelOpen ? "chevron-up" : "chevron-down"} 
                size={16} 
                color="#1C3144"
              />
            </TouchableOpacity>

            {isFilterPanelOpen && (
              <View style={styles.filterPanelContent}>
                {/* Filtres par statut */}
                <View style={styles.filterContainer}>
                  <Text style={styles.filterLabel}>Filtrer par statut</Text>
                  <View style={styles.filterButtons}>
                    {filterOptions.map((filter) => (
                      <TouchableOpacity
                        key={filter.value}
                        style={[
                          styles.filterButton,
                          selectedFilters.includes(filter.value) && styles.filterButtonSelected,
                        ]}
                        onPress={() => toggleFilter(filter.value)}
                      >
                        <Text
                          style={
                            selectedFilters.includes(filter.value)
                              ? styles.filterTextSelected
                              : styles.filterText
                          }
                        >
                          {filter.label}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>

                  {/* Sous-filtres pour "À vérifier" */}
                  {selectedFilters.includes("to_be_checked") && (
                    <View style={styles.subFilterContainer}>
                      <Text style={styles.subFilterLabel}>Type de vérification</Text>
                      <View style={styles.filterButtons}>
                        {subFilterOptions.map((filter) => (
                          <TouchableOpacity
                            key={filter.value}
                            style={[
                              styles.filterButton,
                              selectedSubFilters.includes(filter.value) && styles.filterButtonSelected,
                            ]}
                            onPress={() => toggleSubFilter(filter.value)}
                          >
                            <Text
                              style={
                                selectedSubFilters.includes(filter.value)
                                  ? styles.filterTextSelected
                                  : styles.filterText
                              }
                            >
                              {filter.label}
                            </Text>
                          </TouchableOpacity>
                        ))}
                      </View>
                    </View>
                  )}

                  {/* Filtre par utilisateur */}
                  <View style={[styles.filterContainer, styles.userFilterContainer]}>
                    <Text style={styles.filterLabel}>Filtrer par détenteur</Text>
                    <Dropdown
                      style={styles.userDropdown}
                      placeholderStyle={styles.userDropdownPlaceholder}
                      selectedTextStyle={styles.userDropdownSelectedText}
                      data={[
                        { id: "", full_name: "Tous les utilisateurs" },
                        ...companyUsers
                      ]}
                      placeholder="Sélectionner un détenteur"
                      value={selectedUser}
                      onChange={(item: { id: string; full_name: string }) => setSelectedUser(item.id)}
                      labelField="full_name"
                      valueField="id"
                    />
                  </View>
                </View>
              </View>
            )}
          </View>

          <View style={styles.equipmentList}>
            {isLoading ? (
              <Spinner size="small" color="#1C3144" />
            ) : filteredData.length > 0 ? (
              filteredData.map((item, index) => {
                const { text, backgroundColor } = getEquipmentsStatusStyle(item.status, item.verification_date);
                return (
                  <PaperInfoImg
                    key={index}
                    title={item.name}
                    badge={text}
                    badgeColor={backgroundColor}
                    text1={item.users ? `Détenteur: ${item.users.first_name} ${item.users.last_name}` : ""}
                    text2={item.reference}
                    text3={item.verification_date ? `Vérification: ${moment(item.verification_date).format("DD/MM/YYYY")}` : ""}
                    imgSrc={item.image}
                    onPress={() =>
                      router.push({
                        pathname: "/equipment/detailsEquipment",
                        params: {
                          id: item.id,
                          uuid: item.uuid,
                          name: item.name,
                          reference: item.reference,
                          type: item.type,
                          status: item.status,
                          price: item.price,
                          document: item.document,
                          image: item.image,
                          purchase_date: item.purchase_date,
                          verification_date: item.verification_date,
                        },
                      })
                    }
                  />
                );
              })
            ) : (
              <Text>Vous n'avez aucun équipement.</Text>
            )}
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  container: {
    gap: 10,
    paddingHorizontal: 10,
    maxWidth: 800,
    width: "100%",
    alignSelf: "center",
  },
  equipmentList: {
    gap: 10,
    paddingBottom: 20,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 10,
    marginTop: 14,
  },
  searchContainer: {
    width: "100%",
    marginBottom: 20,
    marginTop: 10,
    paddingHorizontal: 10,
  },
  searchInputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1C314414",
    borderRadius: 8,
    paddingHorizontal: 10,
    height: 60,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    padding: 10,
    fontSize: 16,
    color: "#000000",
  },
  filterContainer: {
    width: "100%",
    marginBottom: 20,
    paddingHorizontal: 10,
  },
  filterLabel: {
    fontSize: 14,
    color: "#262627",
    marginBottom: 10,
    fontWeight: "bold",
  },
  filterButtons: {
    display: "flex",
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 10,
  },
  filterButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "#F99527",
  },
  filterButtonSelected: {
    backgroundColor: "#F99527",
    borderColor: "#F99527",
  },
  filterText: {
    color: "#F99527",
  },
  filterTextSelected: {
    color: "white",
  },
  userFilterContainer: {
    marginTop: 20,
  },
  userDropdown: {
    width: "100%",
    height: 50,
    borderWidth: 1,
    borderColor: "#F99527",
    borderRadius: 8,
  },
  userDropdownPlaceholder: {
    color: "#F99527",
  },
  userDropdownSelectedText: {
    color: "#F99527",
  },
  subFilterContainer: {
    marginTop: 15,
  },
  subFilterLabel: {
    fontSize: 14,
    color: "#262627",
    marginBottom: 10,
    fontWeight: "bold",
  },
  filterPanelContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    marginHorizontal: 10,
    marginVertical: 5,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  filterPanelHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderRadius: 8,
  },
  filterPanelHeaderContent: {
    flex: 1,
    marginRight: 10,
  },
  filterPanelTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1C3144',
    marginBottom: 4,
  },
  activeFiltersText: {
    fontSize: 12,
    color: '#666666',
  },
  filterPanelContent: {
    padding: 15,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
});
