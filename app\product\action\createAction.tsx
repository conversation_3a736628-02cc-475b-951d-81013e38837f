import React, { useContext, useEffect, useState } from "react";
import {
  Alert,
  StyleSheet,
  View,
  TextInput,
  Text,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Modal,
} from "react-native";
import { supabase } from "@/lib/supabase";
import { Controller, useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AntDesign } from "@expo/vector-icons";
import { Dropdown } from "react-native-element-dropdown";
import DateTimePicker from "@react-native-community/datetimepicker";
import { DateTimePickerAndroid } from "@react-native-community/datetimepicker";
import { useRouter, useLocalSearchParams } from "expo-router";

// Choix du dropdown (actions)
const actionStatus = [
  { label: "Réalisée", value: "achieved" },
  { label: "Non réalisée", value: "not_achieved" },
];

// Choix du champ d'action
const actionChamp = [
  { label: "Interne", value: "interne" },
  { label: "Externe", value: "externe" },
];

export default function CreateAction() {
  const [process, setProcess] = useState([]);
  const [loadingProcess, setLoadingProcess] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [users, setUsers] = useState<Array<{ label: string; value: string }>>([]);
  const router = useRouter();
  const { user, companySelected } = useContext(AppContext);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const companyId = companySelected?.id;
  const { product_id } = useLocalSearchParams();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const [showModal, setShowModal] = useState(false);
  const [modalMessage, setModalMessage] = useState("");
  const [modalTitle, setModalTitle] = useState("");
  const [modalType, setModalType] = useState<"success" | "error">("success");

  const {
    handleSubmit,
    formState: { errors },
    control,
    reset,
  } = useForm({
    defaultValues: {
      name: "",
      date: "",
      administrator: "",
      process: "",
      action_champ: "",
      status: "not_achieved",
    },
    mode: "onBlur",
    criteriaMode: "all",
  });

  // Récupérer les informations de l'utilisateur connecté
  useEffect(() => {
    if (!companyId) {
      console.error("❌ Aucun `companyId` fourni pour récupérer les utilisateurs.");
      return;
    }

    const fetchData = async () => {
      try {
        // Récupérer les utilisateurs de l'entreprise
        const { data: usersData, error: usersError } = await supabase
          .from("users")
          .select()
          .eq("uid_admin", user?.uid);

        if (usersError) {
          console.error("Erreur lors de la récupération des utilisateurs :", usersError.message);
          return;
        }

        // Récupérer les fournisseurs de l'entreprise
        const { data: suppliersData, error: suppliersError } = await supabase
          .from("company_users")
          .select(`
            user_id,
            users (
              uid,
              first_name,
              last_name,
              profil
            )
          `)
          .eq("company_id", companyId);

        if (suppliersError) {
          console.error("Erreur lors de la récupération des fournisseurs :", suppliersError.message);
          return;
        }

        // Formatage des utilisateurs (exclure les fournisseurs)
        const formattedUsers = usersData
          .filter((item: any) => item.profil !== "Fournisseur")
          .map((item: any) => ({
            label: `${item.first_name} ${item.last_name}`,
            value: item.uid,
          }));

        // Formatage des fournisseurs
        const formattedSuppliers = suppliersData
          .filter((item: any) => item.users?.profil === "Fournisseur")
          .map((item: any) => ({
            label: `${item.users.first_name} ${item.users.last_name} - Fournisseur`,
            value: item.users.uid,
          }));

        // Fusionner les listes
        setUsers([...formattedUsers, ...formattedSuppliers]);

        // Récupérer les processus de l'entreprise
        const { data: processData, error: processError } = await supabase
          .from("process")
          .select("id, process_name")
          .eq("company_id", companyId);

        if (processError) {
          console.error("Erreur lors de la récupération des processus :", processError.message);
        } else {
          const formattedProcess = processData.map((process: any) => ({
            label: process.process_name,
            value: process.id,
          }));
          setProcess(formattedProcess);
        }
      } catch (err) {
        console.error("Erreur inattendue lors de la récupération :", err);
      }
    };

    fetchData();
  }, [user]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert("Accès restreint", "Vous n'avez pas les droits pour accéder à cette page.");
      navigation.goBack();
    }
  }, [user]);

  // Date (iOS)
  const handleDateChange = (event: any, date: any) => {
    if (date) {
      setSelectedDate(date);
    }
  };

  // Date (Android)
  const showDatePicker = () => {
    DateTimePickerAndroid.open({
      value: selectedDate,
      mode: "date",
      onChange: handleDateChange,
      display: "default",
    });
  };

  const showNotification = (title: string, message: string, type: "success" | "error") => {
    setModalTitle(title);
    setModalMessage(message);
    setModalType(type);
    setShowModal(true);
  };

  // Valider le formulaire
  const handleSubmitCreateAction = async (data: any) => {
    try {
      setIsSubmitting(true);

      if (!data.name || !data.administrator || !data.process || !data.action_champ) {
        showNotification("Erreur", "Veuillez remplir tous les champs obligatoires.", "error");
        setIsSubmitting(false);
        return;
      }

      // Formatage de la date pour Supabase
      const formattedDate = selectedDate.toISOString();

      // Préparation des données pour la création de l'action
      const actionData = {
        name: data.name,
        date: formattedDate,
        type: "preventive",
        status: "not_achieved",
        company_id: companyId,
        created_at: new Date().toISOString(),
      };

      console.log("Données envoyées à actions:", actionData);

      // Étape 1 : Création de l'action dans la table actions
      const { data: createdAction, error: actionError } = await supabase
        .from("actions")
        .insert(actionData)
        .select("id")
        .single();

      if (actionError) {
        console.error("Erreur détaillée création action:", actionError);
        showNotification("Erreur", "Échec de la création de l'action.", "error");
        setIsSubmitting(false);
        return;
      }

      // Préparation des données pour la création de l'event_action
      const actionProductData = {
        action_id: createdAction.id,
        product_id: product_id,
        created_at: new Date().toISOString(),
      };

      console.log("Données envoyées à action_products:", actionProductData);

      // Étape 2 : Création dans action_products pour lier l'action au produit
      const { error: actionProductError } = await supabase
        .from("action_products")
        .insert(actionProductData);

      if (actionProductError) {
        console.error("Erreur détaillée création action_products:", actionProductError);
        showNotification("Erreur", "Échec de la liaison de l'action au produit.", "error");
        setIsSubmitting(false);
        return;
      }

      // Étape 3 : Création de la liaison avec l'administrateur
      const { error: administratorError } = await supabase
        .from("action_administrators")
        .insert({
          action_id: createdAction.id,
          user_id: data.administrator,
          created_at: new Date().toISOString(),
        });

      if (administratorError) {
        console.error("Erreur détaillée création action_administrators:", administratorError);
        showNotification("Erreur", "Échec de la liaison de l'administrateur à l'action.", "error");
        setIsSubmitting(false);
        return;
      }

      // Étape 4 : Création de la liaison avec le processus
      const { error: processError } = await supabase
        .from("action_processes")
        .insert({
          action_id: createdAction.id,
          process_id: data.process,
          created_at: new Date().toISOString(),
        });

      if (processError) {
        console.error("Erreur détaillée création action_processes:", processError);
        showNotification("Erreur", "Échec de la liaison du processus à l'action.", "error");
        setIsSubmitting(false);
        return;
      }

      showNotification("Succès", "L'action a été créée avec succès.", "success");
      reset();
      setTimeout(() => {
        router.back();
      }, 1500);
    } catch (err) {
      console.error("Erreur inattendue détaillée:", err);
      showNotification("Erreur", "Une erreur inattendue est survenue.", "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.form}>
        {/* Champ Nom */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Libellé de l'action</Text>
          <Controller
            control={control}
            name="name"
            rules={{ required: "Le nom est requis." }}
            render={({ field: { onChange, value } }) => (
              <TextInput
                maxLength={50}
                placeholderTextColor={"#6B7280"}
                style={styles.input}
                placeholder="Libellé de l'action"
                onChangeText={onChange}
                value={value}
              />
            )}
          />
          {errors.name && <Text style={styles.errorText}>{errors.name.message}</Text>}
        </View>

        {/* Date */}
        {Platform.OS === "web" ? (
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Date</Text>
            <Controller
              control={control}
              name="date"
              rules={{ required: "La date est requise." }}
              render={({ field: { onChange, value } }) => (
                <input
                  type="date"
                  style={styles.webDateInput}
                  onChange={(e) => {
                    const date = new Date(e.target.value);
                    setSelectedDate(date);
                    onChange(date);
                  }}
                  value={selectedDate.toISOString().split('T')[0]}
                />
              )}
            />
            {errors.date && <Text style={styles.errorText}>{errors.date.message}</Text>}
          </View>
        ) : Platform.OS === "android" ? (
          <TouchableOpacity onPress={showDatePicker} style={styles.dateTimePickerContainer}>
            <Text style={styles.dateTimePickerLabel}>Date</Text>
            <Text style={styles.datePicker}>{selectedDate.toLocaleDateString()}</Text>
          </TouchableOpacity>
        ) : (
          <View style={styles.dateTimePickerContainer}>
            <Text style={styles.label}>Date</Text>
            <DateTimePicker
              value={selectedDate}
              mode="date"
              display="default"
              onChange={handleDateChange}
              style={styles.datePicker}
            />
          </View>
        )}

        {/* Champ Responsable */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Responsable de l'action</Text>
          <Controller
            control={control}
            name="administrator"
            rules={{ required: "Veuillez sélectionner un responsable." }}
            render={({ field: { onChange, value } }) => (
              <Dropdown
                style={dropdownStyles.dropdown}
                placeholderStyle={dropdownStyles.placeholderStyle}
                selectedTextStyle={dropdownStyles.selectedTextStyle}
                data={users}
                placeholder="Sélectionner un responsable"
                value={value}
                onChange={(item) => onChange(item.value)}
                labelField="label"
                valueField="value"
              />
            )}
          />
          {errors.administrator && (
            <Text style={styles.errorText}>{errors.administrator.message}</Text>
          )}
        </View>

        {/* Champ Processus */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Processus concerné</Text>
          <Controller
            control={control}
            name="process"
            rules={{ required: "Le processus est requis." }}
            render={({ field: { onChange, value } }) => (
              <Dropdown
                style={dropdownStyles.dropdown}
                placeholderStyle={dropdownStyles.placeholderStyle}
                selectedTextStyle={dropdownStyles.selectedTextStyle}
                data={process}
                placeholder="Sélectionner un processus"
                value={value}
                onChange={(item) => onChange(item.value)}
                labelField="label"
                valueField="value"
              />
            )}
          />
          {errors.process && <Text style={styles.errorText}>{errors.process.message}</Text>}
        </View>

        {/* Champ d'action */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Champ d'action</Text>
          <Controller
            control={control}
            name="action_champ"
            rules={{ required: "Le champ d'action est requis." }}
            render={({ field: { onChange, value } }) => (
              <Dropdown
                style={dropdownStyles.dropdown}
                placeholderStyle={dropdownStyles.placeholderStyle}
                selectedTextStyle={dropdownStyles.selectedTextStyle}
                data={actionChamp}
                placeholder="Sélectionner un champ d'action"
                value={value}
                onChange={(item) => onChange(item.value)}
                labelField="label"
                valueField="value"
              />
            )}
          />
          {errors.action_champ && (
            <Text style={styles.errorText}>{errors.action_champ.message}</Text>
          )}
        </View>

        {/* Bouton de soumission */}
        <TouchableOpacity
          style={[styles.confirmButton, isSubmitting && styles.disabledButton]}
          onPress={handleSubmit(handleSubmitCreateAction)}
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <ActivityIndicator color="#FFF" />
          ) : (
            <Text style={styles.confirmButtonText}>Créer</Text>
          )}
        </TouchableOpacity>
      </ScrollView>

      {/* Modal de notification */}
      <Modal
        visible={showModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[
            styles.modalContent,
            modalType === "success" ? styles.successModal : styles.errorModal
          ]}>
            <Text style={styles.modalTitle}>{modalTitle}</Text>
            <Text style={styles.modalMessage}>{modalMessage}</Text>
            <TouchableOpacity
              style={styles.modalButton}
              onPress={() => {
                setShowModal(false);
                if (modalType === "success") {
                  router.back();
                }
              }}
            >
              <Text style={styles.modalButtonText}>OK</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    height: "100%",
    width: "100%",
    alignItems: "center",
    alignContent: "center",
    backgroundColor: "#FFFFFF",
    ...Platform.select({
      web: {
        maxWidth: 800,
        marginHorizontal: "auto",
      },
    }),
  },
  disabledButton: { backgroundColor: "#ccc" },
  form: {
    width: "100%",
    alignSelf: "center",
    paddingHorizontal: 30,
    ...Platform.select({
      web: {
        maxWidth: 800,
        marginHorizontal: "auto",
        marginBottom: 50,
        marginTop: 20,
      },
      default: {
        marginBottom: 250,
        marginTop: 20,
      },
    }),
  },
  input: {
    borderWidth: 2,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "auto",
    padding: 12,
    marginTop: 10,
    color: "#000000",
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginBottom: 5,
  },
  inputContainer: {
    position: "relative",
    marginBottom: 20,
    width: "100%",
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
    textAlign: "left",
  },
  confirmButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    marginBottom: 16,
    backgroundColor: "#f99527",
    marginTop: 10,
  },
  confirmButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  dateTimePickerContainer: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    textAlign: "left",
    marginVertical: 20,
  },
  dateTimePickerLabel: {
    color: "#063E76",
    fontSize: 14,
    fontWeight: "bold",
    backgroundColor: "#FFF",
  },
  datePicker: {
    width: "100%",
    marginTop: 10,
    marginBottom: 10,
  },
  webDateInput: {
    width: "100%",
    padding: 12,
    borderWidth: 2,
    borderColor: "#ccc",
    borderRadius: 8,
    marginTop: 10,
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '80%',
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
  },
  successModal: {
    backgroundColor: '#4CAF50',
  },
  errorModal: {
    backgroundColor: '#f44336',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 10,
  },
  modalMessage: {
    fontSize: 16,
    color: 'white',
    textAlign: 'center',
    marginBottom: 20,
  },
  modalButton: {
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  modalButtonText: {
    color: '#333',
    fontWeight: 'bold',
  },
});

const dropdownStyles = StyleSheet.create({
  dropdown: {
    marginVertical: 16,
    height: 50,
    backgroundColor: "white",
    borderRadius: 12,
    padding: 12,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
  },
  icon: {
    marginRight: 5,
  },
  item: {
    padding: 17,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  textItem: {
    flex: 1,
    fontSize: 16,
  },
  placeholderStyle: {
    fontSize: 16,
  },
  selectedTextStyle: {
    fontSize: 16,
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 16,
  },
});
