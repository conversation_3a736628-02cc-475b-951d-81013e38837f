import { StyleSheet, Text, TouchableOpacity } from "react-native";
import Ionicons from "@expo/vector-icons/Ionicons";

type TypeProps = {
  title: string;
  onPress?: any;
};

export const ContainerImgText = ({ title, onPress }: TypeProps) => {
  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <Ionicons name="calendar-outline" size={80} color="black" />
      <Text>{title}</Text>
    </TouchableOpacity>
  );
};
const styles = StyleSheet.create({
  container: {
    borderWidth: 1,
    borderColor: "#F99527",
    alignItems: "center",
    borderRadius: 8,
    padding: 20,
    gap: 10,
  },
});
