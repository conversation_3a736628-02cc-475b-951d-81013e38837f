import ContainedButton from "@/components/ui/buttons/ContainedButton";
import OutlinedButton from "@/components/ui/buttons/OutlinedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useRouter, useLocalSearchParams, useFocusEffect } from "expo-router";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Platform,
  Alert,
  Linking,
  Image,
} from "react-native";

import { useCallback, useContext, useEffect, useState } from "react";
import { Octicons } from "@expo/vector-icons";
import { AppContext } from "@/state/AppContext";
import PaperInfo from "@/components/common/paperInfo";
import { supabase } from "@/lib/supabase";
import TypeValue from "@/types/typeValue";
import { Dimensions } from "react-native";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { Badge, BadgeText } from "@/components/ui/badge";

const screenWidth = Dimensions.get("window").width;

export default function detailsDoc() {
  const { user } = useContext(AppContext);

  const [values, setValues] = useState<TypeValue[]>([]);

  const [doc, setDoc] = useState<{
    name: string;
    created_at: string;
    image: string;
    id: number;
  }>();

  const router = useRouter();

  const { doc_id, product_id } = useLocalSearchParams();

  const fetchDoc = async () => {
    const { data: dataGetted, error } = await supabase
      .from("docsProduct")
      .select("*")
      .eq("id", doc_id)
      .eq("type", "doc")
      .single();

    if (!error) {
      setDoc(dataGetted as any);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchDoc();
    }, [])
  );

  function formatDate(dateString: string) {
    const date = new Date(dateString);
    return date.toLocaleDateString("fr-FR");
  }

  return (
    <View>
      <ScrollView>
        <View style={styles.containerContent}>
          <View style={styles.registerInfo}>
            <View style={styles.inputs}>
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  gap: 10,
                  flexWrap: "wrap",
                }}
              >
                <View
                  style={{
                    gap: 10,
                    width: "100%",
                  }}
                >
                  <Text style={styles.documentName}>{doc?.name}</Text>
                  {Platform.OS === "web" ? (
                    doc?.image?.endsWith(".pdf") ? (
                      <iframe
                        src={doc?.image}
                        style={{
                          width: "100%",
                          height: "500px",
                          border: "none",
                        }}
                      />
                    ) : (
                      <Image
                        source={{ uri: doc?.image }}
                        style={styles.detailImage}
                      />
                    )
                  ) : (
                    <TouchableOpacity
                      style={styles.imageTouchable}
                      onPress={() => {
                        if (doc?.image) {
                          Linking.openURL(doc.image);
                        } else {
                          Alert.alert("Erreur", "Aucun fichier disponible pour ce document.");
                        }
                      }}
                    >
                      <Image
                        source={{ uri: doc?.image }}
                        style={styles.detailImage}
                      />
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            </View>
          </View>

          {/* Boutons pour ouvrir et modifier le document */}
          <View style={styles.buttonsContainer}>
            <TouchableOpacity
              style={styles.openDocumentButton}
              onPress={() => {
                if (doc?.image) {
                  Linking.openURL(doc.image);
                } else {
                  Alert.alert("Erreur", "Aucun fichier disponible pour ce document.");
                }
              }}
            >
              <Text style={styles.openDocumentButtonText}>Ouvrir le document</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.updateDocumentButton}
              onPress={() => {
                router.push({
                  pathname: "/product/doc/updateDoc",
                  params: {
                    doc_id: doc?.id,
                    name: doc?.name,
                    image: doc?.image,
                    product_id: product_id,
                  },
                });
              }}
            >
              <Text style={styles.updateDocumentButtonText}>Modifier le document</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  containerContent: {
    flexDirection: "column",
    paddingHorizontal: 20,
    gap: 20,
    marginBottom: 100,
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    width: "100%",
    gap: 40,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  buttonPassword: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  titleContainer: {
    flexDirection: "row",
    gap: 8,
  },
  imgIsLoadingPage: {
    height: 75,
    width: 130,
    alignSelf: "center",
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
  input: {
    backgroundColor: "white",
    borderColor: "none",
    height: 40,
    padding: 10,
    borderRadius: 4,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
  },
  deleteButtonContainer: {
    width: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 30,
  },
  deleteButton: {
    width: 200,
    backgroundColor: "#ff4f42",
    padding: 10,
    borderRadius: 8,
    alignItems: "center",
  },
  deleteButtonText: {
    color: "#FFFFFF",
    fontWeight: "normal",
  },
  detailImage: {
    width: "100%",
    height: 230,
    borderRadius: 10,
    marginBottom: 20,
  },
  imageTouchable: {
    width: "100%",
  },
  buttonsContainer: {
    flexDirection: "column",
    gap: 10,
    width: "100%",
  },
  openDocumentButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    backgroundColor: "#1C3144",
    marginTop: 10,
  },
  updateDocumentButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    backgroundColor: "#F99527",
    marginTop: 10,
  },
  openDocumentButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  updateDocumentButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  documentName: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1C3144",
    marginBottom: 10,
    textAlign: "center",
  },
});
