/*
app/(tabs)/action.tsx

Liste des Actions.

Informations pertinentes :

- Les actions sont récupérées depuis la table `actions` de Supabase.
- Chaque action appartient à une entreprise (colonne `company_id`).
- Les données incluent les relations suivantes :
  -> Table de jointure `action_administrators` pour récupérer les administrateurs des actions (via `user_id` et jointure avec la table `users` pour obtenir les noms).
  -> Table de jointure `action_processes` pour récupérer les processus liés aux actions (via `process_id` et jointure avec la table `process` pour obtenir les noms des processus).
  -> Table de jointure `action_events` pour récupérer les événements associés aux actions (via `event_id` et jointure avec la table `events` pour obtenir les noms des événements).
- Les données récupérées incluent :
  - `id` : Identifiant unique de l'action.
  - `name` : Nom de l'action.
  - `date` : Date d'échéance de l'action.
  - `type` : Type de l'action (`immediate`, `corrective`).
  - `status` : Statut de l'action (`achieved`, `not_achieved`).
  - `administrator` : Informations sur le responsable de l'action (nom et prénom).
  - `process` : Nom du processus lié à l'action.
  - `event` : Nom de l'événement associé à l'action.
- Si aucune action n'est trouvée, un bouton pour créer une nouvelle action est affiché.
- Les données sont chargées à l'aide d'un `useFocusEffect` et affichées dans des sections organisées par `status` d'action.

----- Affichage des actions -----

Le formulaire de création d'une action permet d'ajouter une date (colonne `date`).
En fonction de cette date d'échéance (colonne `date`) et du statut de l'action (colonne `status`) nous aurons les conditions suivantes :

- Actions (validées) :
   -> `status` : `achieved`
- Actions en cours :
   -> `status` : `not_achieved`
   -> `date` : `date` < date du jour
- Actions en retard :
   -> `status` : `not_achieved`
   -> `date` : `date` > date du jour
- 🚨 Les actions sont groupées en fonction de la date d'échéance (colonne `date`) et du statut (colonne `status`) et limitées à 2 par groupe :
  -> Les actions de type `achieved` sont dans "Actions validées".
  -> Les actions de type `not_achieved` et `date` : `date` < date du jour sont dans "Actions en cours".
  -> Les actions de type `not_achieved` et `date` : `date` > date du jour sont dans "Actions en retard".
*/

import {
  FlatList,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Platform,
  TextInput,
} from "react-native";
import {
  ParamListBase,
  useFocusEffect,
  useNavigation,
} from "@react-navigation/native";
import { useContext, useEffect } from "react";
import { AppContext } from "@/state/AppContext";
import { useState } from "react";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { Octicons } from "@expo/vector-icons";
import { supabase } from "@/lib/supabase";
import React from "react";
import Feather from "@expo/vector-icons/Feather";
import moment from "moment";
import { useRouter } from "expo-router";
import Header from "@/components/common/Header";
import FontAwesome from "@expo/vector-icons/FontAwesome";

const filterOptions = [
  { label: "En cours", value: "in_progress" },
  { label: "En retard", value: "late" },
  { label: "Terminé", value: "achieved" },
];

interface Action {
  id: number;
  name: string;
  date: string;
  status: string;
  isLate: boolean;
  administrator: {
    first_name: string;
    last_name: string;
  } | null;
  process: {
    process_name: string;
  } | null;
  event: {
    wording: string;
  } | null;
}

export default function ActionList() {
  const router = useRouter();
  const [actionsList, setActionsList] = useState<any>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredActionsList, setFilteredActionsList] = useState<any>([]);
  const { user } = useContext(AppContext);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const [selectedFilters, setSelectedFilters] = useState<string[]>(["in_progress", "late"]);
  
  const toggleFilter = (value: string) => {
    setSelectedFilters((prevSelected) =>
      prevSelected.includes(value)
        ? prevSelected.filter((item) => item !== value)
        : [...prevSelected, value]
    );
  };

  // Récupérer la liste des actions de l'entreprise
  useFocusEffect(
    React.useCallback(() => {
      const fetchActions = async () => {
        try {
          if (!user || !user.uid) {
            console.error("Utilisateur non connecté.");
            return;
          }

          // Vérifiez que l'utilisateur a une entreprise associée
          const companyId = user.companies?.[0]?.id;
          if (!companyId) {
            setActionsList([]);
            setLoading(false);
            return;
          }

          // Requête pour récupérer les actions avec les relations jointes
          const { data, error } = await supabase
            .from("actions")
            .select(
              `
            *,
            action_administrators (user_id, users (first_name, last_name, uid)),
            action_processes (process_id, process (process_name)),
            action_events (event_id, events (wording))
          `
            )
            .eq("company_id", companyId);

          if (error) {
            console.error(
              "Erreur lors de la récupération des actions :",
              error.message
            );
            return;
          }

          // Récupération de la date actuelle
          const currentDate = moment();

          // Créer une seule liste d'actions triée par date
          const sortedActions = data
            .map((action: any) => ({
              ...action,
              administrator: action.action_administrators?.[0]?.users || null,
              process: action.action_processes?.[0]?.process || null,
              event: action.action_events?.[0]?.events || null,
              isLate: action.status === "not_achieved" && moment(action.date).isBefore(currentDate),
            }))
            .sort((a: Action, b: Action) => moment(a.date).diff(moment(b.date)));

          // Filtrer les actions selon les filtres sélectionnés
          const filteredActions = sortedActions.filter((action: Action) => {
            if (selectedFilters.length === 0) return true;
            if (selectedFilters.includes("in_progress") && action.status === "not_achieved" && !action.isLate) return true;
            if (selectedFilters.includes("late") && action.status === "not_achieved" && action.isLate) return true;
            if (selectedFilters.includes("achieved") && action.status === "achieved") return true;
            return false;
          });

          setActionsList(filteredActions);
        } catch (err) {
          console.error("Erreur inattendue :", err);
        } finally {
          setLoading(false);
        }
      };

      fetchActions();
    }, [user, selectedFilters])
  );

  // Mettre à jour la liste filtrée en fonction de la recherche
  useEffect(() => {
    const filteredList = actionsList.filter((item: any) =>
      item.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredActionsList(filteredList);
  }, [searchQuery, actionsList]);

  return (
    <View style={{ flex: 1 }}>
      <View style={{ width: "100%" }}>
        <Header onPressFlesh={() => router.back()} />
        <View style={styles.container}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={{ fontWeight: "bold" }}>
              Créer une nouvelle action :
            </Text>
            <TouchableOpacity
              onPress={() => router.navigate("/screens/actions/CreateAction")}
            >
              <Octicons name="diff-added" size={25} color="#F99527" />
            </TouchableOpacity>
          </View>

          {/* Barre de recherche */}
          <View style={styles.searchContainer}>
            <View style={styles.searchInputWrapper}>
              <FontAwesome
                name="search"
                size={24}
                color="#3C3c4399"
                style={styles.searchIcon}
              />
              <TextInput
                style={styles.searchInput}
                placeholder="Rechercher par nom..."
                placeholderTextColor="#3C3c4399"
                value={searchQuery}
                onChangeText={(text) => setSearchQuery(text)}
              />
            </View>
          </View>

          {/* Filtres */}
          <View style={styles.filterContainer}>
            <Text style={styles.filterLabel}>Filtrer par statut</Text>
            <View style={styles.filterButtons}>
              {filterOptions.map((filter) => (
                <TouchableOpacity
                  key={filter.value}
                  style={[
                    styles.filterButton,
                    selectedFilters.includes(filter.value) && styles.filterButtonSelected,
                  ]}
                  onPress={() => toggleFilter(filter.value)}
                >
                  <Text
                    style={
                      selectedFilters.includes(filter.value)
                        ? styles.filterTextSelected
                        : styles.filterText
                    }
                  >
                    {filter.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      </View>
      <View style={{ flex: 1, gap: 10, paddingHorizontal: 10, maxWidth: 800, width: "100%", alignSelf: "center" }}>
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>
            Actions en cours et en retard
          </Text>
          <TouchableOpacity
            onPress={() =>
              navigation.navigate("screens/actions/RegularActionsList")
            }
            style={styles.sectionButton}
          >
            {/* <Text style={styles.sectionButtonText}>Voir plus</Text> */}
          </TouchableOpacity>
        </View>
        {loading ? (
          <Text>Chargement...</Text>
        ) : filteredActionsList?.length === 0 ? (
          <View style={styles.noDataContainer}>
            <Text style={styles.noDataText}>Aucune action trouvée</Text>
          </View>
        ) : (
          <FlatList
            data={filteredActionsList}
            keyExtractor={(item) => item.id.toString()|| Math.random().toString()}
            renderItem={({ item }) => (
              <TouchableOpacity
                onPress={() =>
                  navigation.navigate("screens/actions/ActionDetails", {
                    action: item,
                  })
                }
                style={styles.listItem}
              >
                {/* 1ère ligne d'éléments */}
                <View style={styles.elementsTop}>
                  <Text style={styles.listName}>{Platform.OS === "web" ? 
                    (item.name.length > 40 ? item.name.slice(0, 40) + "..." : item.name) : 
                    (item.name.length > 20 ? item.name.slice(0, 20) + "..." : item.name)}
                  </Text>
                  <Text style={styles.date}>
                    {moment(item.date).format("DD/MM/YYYY")}
                  </Text>
                </View>
                {/* 2ème ligne d'éléments */}
                <View style={styles.elementsMiddle}>
                  <Feather 
                    name="clock" 
                    size={24} 
                    color={
                      item.status === "achieved" 
                        ? "#2FC12B" 
                        : item.isLate 
                          ? "#FF4f42" 
                          : "#F99527"
                    } 
                  />
                </View>
                {/* 3ème ligne d'éléments */}
                <View style={styles.elementsBottom}>
                  <Text style={styles.texts}>
                    Responsable :{" "}
                    {item.administrator
                      ? `${item.administrator.first_name} ${item.administrator.last_name}`
                      : "Non attribué"}
                  </Text>
                  <Text style={styles.texts}>
                    Événement :{" "}
                    {item.event ? item.event.wording : "Aucun événement"}
                  </Text>
                  <Text style={styles.texts}>
                    Processus :{" "}
                    {item.process ? item.process.process_name : "Non défini"}
                  </Text>
                </View>
              </TouchableOpacity>
            )}
            contentContainerStyle={styles.listContainer}
          />
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    gap: 10,
    paddingHorizontal: 10,
    maxWidth: 800,
    width: "100%",
    alignSelf: "center",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginVertical: 14,
    width: "100%",
  },
  searchContainer: {
    width: "100%",
    marginBottom: 20,
    marginTop: 10,
    paddingHorizontal: 10,
  },
  searchInputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1C314414",
    borderRadius: 8,
    paddingHorizontal: 10,
    height: 60,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    padding: 10,
    fontSize: 16,
    color: "#525252",
  },
  filterContainer: {
    width: "100%",
    marginBottom: 20,
  },
  filterLabel: {
    fontSize: 14,
    color: "#525252",
    marginBottom: 10,
    fontWeight: "bold",
  },
  filterButtons: {
    display: "flex",
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 10,
  },
  filterButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "#F99527",
  },
  filterButtonSelected: {
    backgroundColor: "#F99527",
    borderColor: "#F99527",
  },
  filterText: {
    color: "#F99527",
    fontSize: 14,
  },
  filterTextSelected: {
    color: "white",
    fontSize: 14,
  },
  sectionContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    alignItems: "center",
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#1C3144",
    marginVertical: 10,
  },
  sectionButton: {},
  listContainer: {
    width: "100%",
    paddingVertical: 10,
  },
  listItem: {
    width: "100%",
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    display: "flex",
    justifyContent: "space-between",
    flexDirection: "column",
    gap: 10,
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  elementsTop: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  elementsMiddle: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "flex-end",
    textAlign: "right",
  },
  elementsBottom: {
    display: "flex",
    flexDirection: "column",
  },
  listName: {
    fontSize: 15,
    color: "#000000",
    fontStyle: "normal",
    flexWrap: "wrap",
    fontWeight: "bold",
  },
  date: {
    fontSize: 14,
    color: "#525252",
    fontStyle: "normal",
    flexWrap: "wrap",
    fontWeight: "normal",
  },
  texts: {
    fontSize: 14,
    color: "#525252",
    fontStyle: "normal",
    flexWrap: "wrap",
    fontWeight: "normal",
  },
  noDataContainer: {
    height: Platform.OS === "web" ? 150 : 100,
    display: "flex",
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  noDataText: {
    fontSize: 14,
    color: "#525252",
    fontStyle: "normal",
    flexWrap: "wrap",
    fontWeight: "normal",
  },
});
