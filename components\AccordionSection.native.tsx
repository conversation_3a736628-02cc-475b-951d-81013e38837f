import { StyleSheet, View, Text, TouchableOpacity, Animated, LayoutAnimation, Platform } from "react-native";
import { useState, useRef, useEffect } from "react";
import { Ionicons } from "@expo/vector-icons";
import { Svg, Circle } from 'react-native-svg';

type AccordionSectionProps = {
  title: string;
  children: React.ReactNode;
  onEdit?: () => void;
  hasContent?: boolean;
};

export default function AccordionSection({ title, children, onEdit, hasContent }: AccordionSectionProps) {
  const [expanded, setExpanded] = useState(false);
  const rotateAnimation = useRef(new Animated.Value(0)).current;
  const heightAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Animation de rotation de l'icône
    Animated.timing(rotateAnimation, {
      toValue: expanded ? 1 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start();

    // Animation de hauteur du contenu
    Animated.timing(heightAnimation, {
      toValue: expanded ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();

    // Configuration de l'animation de layout pour iOS
    if (Platform.OS === 'ios') {
      LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    }
  }, [expanded]);

  const rotate = rotateAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg']
  });

  const contentHeight = heightAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1]
  });

  return (
    <View style={styles.container}>
      <TouchableOpacity 
        style={styles.header} 
        onPress={() => setExpanded(!expanded)}
        activeOpacity={0.7}
      >
        <View style={styles.titleContainer}>
          <View style={styles.titleWrapper}>
            <Text style={styles.title}>{title}</Text>
            <View style={styles.statusIndicator}>
              <Svg width="24" height="24" viewBox="0 0 24 24">
                <Circle
                  cx="12"
                  cy="12"
                  r="10"
                  stroke={hasContent ? "#F99527" : "#E6E6E6"}
                  strokeWidth="2"
                  fill="none"
                />
                {hasContent && (
                  <Circle
                    cx="12"
                    cy="12"
                    r="6"
                    fill="#F99527"
                  />
                )}
              </Svg>
            </View>
          </View>
          <View style={styles.actions}>
            {onEdit && (
              <TouchableOpacity 
                onPress={(e) => {
                  e.stopPropagation();
                  onEdit();
                }} 
                style={styles.editButton}
              >
                <Ionicons name="pencil" size={20} color="#F99527" />
              </TouchableOpacity>
            )}
            <Animated.View style={{ transform: [{ rotate }] }}>
              <Ionicons 
                name="chevron-down" 
                size={24} 
                color="#1C3144" 
              />
            </Animated.View>
          </View>
        </View>
      </TouchableOpacity>
      
      <Animated.View 
        style={[
          styles.content,
          {
            maxHeight: expanded ? 'auto' : 0,
            opacity: expanded ? 1 : 0,
            transform: [{ scaleY: contentHeight }]
          }
        ]}
      >
        <View style={styles.contentInner}>
          {children}
        </View>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
    borderRadius: 8,
    marginBottom: 10,
    overflow: "hidden",
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  header: {
    padding: 16,
  },
  titleContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  titleWrapper: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1C3144",
  },
  statusIndicator: {
    width: 24,
    height: 24,
    marginLeft: 8,
  },
  actions: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  editButton: {
    padding: 4,
  },
  content: {
    overflow: "hidden",
  },
  contentInner: {
    padding: 16,
  },
}); 