import React, { useContext } from "react";
import { View, Text, StyleSheet, Image, TouchableOpacity } from "react-native";
import { Badge, BadgeText } from "../ui/badge";
import FontAwesome5 from "@expo/vector-icons/FontAwesome5";
import { MaterialIcons } from "@expo/vector-icons";
type typePropPaper = {
  title?: string;
  data?: any;
  score?: number;
};
const Paper = ({ title, data, score }: typePropPaper) => {
  return (
    <TouchableOpacity style={styles.container}>
      <View style={styles.containerImgInfo}>
        <View style={styles.containerInfo}>
          <View style={styles.headerContainer}>
            <View style={styles.titleContainer}>
              {title && (
                <Text style={styles.title} numberOfLines={2}>
                  {title}
                </Text>
              )}
            </View>

            {score && (
              <Text style={styles.scoreText}>
                Score: {score}
              </Text>
            )}
          </View>
          {data?.map((item: any) => {
            return (
              <View style={styles.itemContainer} key={item.label}>
                <Text style={styles.labelText}>
                  {item.label}
                </Text>

                {item.label === "Niveau de maîtrise de <PERSON> PIP ?" ? (
                  <Badge
                    variant="solid"
                    style={styles.badge}
                  >
                    <BadgeText style={styles.badgeText}>
                      <Text numberOfLines={1} adjustsFontSizeToFit>
                        {item.value}
                      </Text>
                    </BadgeText>
                  </Badge>
                ) : (
                  <Text style={styles.valueText} numberOfLines={3}>
                    {item.value}
                  </Text>
                )}
              </View>
            );
          })}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 10,
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: "#E6E6E6",
    borderRadius: 10,
    width: "100%",
    paddingVertical: 15,
    marginVertical: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  containerImgInfo: {
    gap: 30,
    flexDirection: "row",
    padding: 10,
    alignItems: "flex-start",
    width: "100%",
  },
  containerInfo: {
    gap: 5,
    flex: 1,
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 10,
  },
  titleContainer: {
    flex: 1,
    marginRight: 10,
  },
  title: {
    fontWeight: "bold",
    fontSize: 20,
  },
  scoreText: {
    fontWeight: "bold",
    color: "#F99527",
  },
  itemContainer: {
    marginBottom: 10,
  },
  labelText: {
    color: "#1C3144",
    fontWeight: "bold",
    marginBottom: 5,
  },
  valueText: {
    color: "#525252",
  },
  badge: {
    backgroundColor: "#b91c1c",
    borderRadius: 48,
    paddingHorizontal: 10,
    minWidth: 80,
    alignSelf: "flex-start",
  },
  badgeText: {
    color: "white",
    textAlign: "center",
  },
});

export default Paper;
