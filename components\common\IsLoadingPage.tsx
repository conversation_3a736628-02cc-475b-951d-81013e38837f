import { Image, StyleSheet, Platform, View } from "react-native";

const IsLoadingPage = () => {
  return (
    <View style={styles.Container}>
      {/* <Image
        source={require("@/assets/images/login/imgIsLodingPage.png")}
        style={styles.imgIsLoadingPage}
      /> */}
    </View>
  );
};

export default IsLoadingPage;

const styles = StyleSheet.create({
  Container: {
    backgroundColor: "#1C3144",
  },
  imgIsLoadingPage: {
    height: 178,
    width: 290,
    bottom: 0,
    left: 0,
    position: "absolute",
  },
});
