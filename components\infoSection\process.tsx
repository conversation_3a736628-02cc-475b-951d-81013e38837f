import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  TextInput,
} from "react-native";
import { useRouter, usePathname } from "expo-router";

import { useEffect, useState } from "react";
import PaperInfoImg from "../common/paperInfoImg";
import typeUserByAdmin from "@/types/typeUserByAdmin";
import { Spinner } from "../ui/spinner";
// import IsLoadingPage from "@/components/common/isLoadingPage";
import TypeProcess from "@/types/typeProcess";

type typeActivityProps = {
  icon?: any;
  data: TypeProcess[];
  isLanding?: boolean;
};

export default function ProcessInfoSection({
  icon,
  data,
  isLanding,
}: typeActivityProps) {
  const router = useRouter();
  const path = usePathname();

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={{ fontWeight: "bold" }}>Mes Processus</Text>

        <TouchableOpacity onPress={() => router.push("/info/process")}>
          {icon ? (
            icon
          ) : (
            <Text
              style={{
                color: "#F99527",
                textDecorationLine: "underline",
                fontFamily: "Poppins",
                fontWeight: "bold",
              }}
            >
              Voir plus
            </Text>
          )}
        </TouchableOpacity>
      </View>

      <View style={styles.users}>
        {isLanding ? (
          <Spinner size="small" color="#1C3144" />
        ) : data?.length > 0 ? (
          (path === "/Informations"|| path === "/Accueil"  || path === "/menu/itemsMenuCockpit/info"
            ? data?.slice(-2)
            : data
          ).map((item, index) => {
            return (
              <PaperInfoImg
                key={index}
                imgSrc={item.profil_picture}
                title={item?.process_name} // Afficher prénom + nom
                text2={item?.users?.first_name + " " + item?.users?.last_name}
                onPress={() =>
                  router.push({
                    pathname: "/info/detailsProcess",
                    params: {
                      process_name: item.process_name,
                      pilote:
                        item.users.first_name + " " + item?.users.last_name,
                      process_id: item.id,
                    },
                  })
                }
              />
            );
          })
        ) : (
          <Text>Vous n'avez pas encore créé des processus</Text>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "column",
    width: "100%",
    gap: 10,
    // marginBottom: 100,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 10,
  },
  users: {
    // backgroundColor: "red",
    // width: "100%",
    paddingHorizontal: 10,
    gap: 10,
  },
});
