import React from "react";
import ContainedButton from "@/components/ui/buttons/ContainedButton";
import OutlinedButton from "@/components/ui/buttons/OutlinedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useRouter, useLocalSearchParams, useFocusEffect } from "expo-router";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Platform,
  Alert,
} from "react-native";

import { useCallback, useContext, useEffect, useState } from "react";
import { Octicons } from "@expo/vector-icons";
import { AppContext } from "@/state/AppContext";
import PaperInfo from "@/components/common/paperInfo";
import { supabase } from "@/lib/supabase";
import Header from "@/components/common/Header";
import TypeValue from "@/types/typeValue";
import { Dimensions } from "react-native";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { Badge, BadgeText } from "@/components/ui/badge";
import { Image } from "react-native";

const screenWidth = Dimensions.get("window").width; // ✅ Récupère la largeur de l'écran

export default function detailsDoc() {
  const { user } = useContext(AppContext);

  const [values, setValues] = useState<TypeValue[]>([]);

  const [doc, setDoc] = useState<{
    name: string;
    created_at: string;
    image: string;
    id: number;
  }>();

  const router = useRouter();

  const { doc_id, product_id } = useLocalSearchParams();

  const fetchDoc = async () => {
    const { data: dataGetted, error } = await supabase
      .from("docsProduct")
      .select("*")
      .eq("id", doc_id)
      .eq("type", "action")
      .single();

    if (!error) {
      setDoc(dataGetted as any);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchDoc();
    }, [])
  );

  function formatDate(dateString: string) {
    const date = new Date(dateString);
    return date.toLocaleDateString("fr-FR");
  }

  return (
    <ScrollView>
      <View style={styles.containerContent}>
        <View style={styles.registerInfo}>
          <View style={styles.inputs}>
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                gap: 10,
                flexWrap: "wrap",
              }}
            >
              <View
                style={{
                  gap: 10,
                  width: "100%",
                }}
              >
                <Image
                  source={{ uri: doc?.image as string }}
                  style={{
                    width: "100%",
                    height: 200,
                    marginTop: 10,
                    alignSelf: "center",
                  }}
                />
              </View>
            </View>
          </View>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  containerContent: {
    flexDirection: "column",
    paddingHorizontal: 20,
    gap: 20,
    marginBottom: 300,
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    // backgroundColor: "yellow",
    width: "100%",
    gap: 40,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  buttonPassword: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  titleContainer: {
    flexDirection: "row",
    gap: 8,
  },
  imgIsLoadingPage: {
    height: 75,
    width: 130,
    alignSelf: "center",
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
  input: {
    backgroundColor: "white",
    borderColor: "none",
    height: 40,
    padding: 10,
    borderRadius: 4,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    // paddingHorizontal: 10,
    width: "100%",
  },
  deleteButtonContainer: {
    width: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 30,
  },
  deleteButton: {
    width: 200,
    backgroundColor: "#ff4f42",
    padding: 10,
    borderRadius: 8,
    alignItems: "center",
  },
  deleteButtonText: {
    color: "#FFFFFF",
    fontWeight: "normal",
  },
});
