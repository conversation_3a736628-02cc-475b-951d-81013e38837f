/*
app/risk/updateRisk.tsx

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern` et `Extern` n'ont pas accès à cette page.
*/

import ContainedButton from "@/components/ui/buttons/ContainedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useLocalSearchParams, useRouter } from "expo-router";
import { StyleSheet, View, ScrollView, Alert, Platform } from "react-native";
import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import CustomizedSelect from "@/components/ui/customizedSelect/customizedSelect";
import TypeRisk from "@/types/typeRisk";
import TypeProcess from "@/types/typeProcess";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";

const possibilities = [
  { value: 0, label: "Quasi impossible" },
  { value: 1, label: "Possible" },
  { value: 2, label: "Fortes chances" },
  { value: 3, label: "Quasi certain !" },
];

const consequences = [
  { value: 0, label: "Impact critique" },
  { value: 1, label: "Impact important" },
  { value: 2, label: "Impact modéré" },
  { value: 3, label: "Impact mineur" },
];

const levels = [
  { value: 0, label: "Non maîtrisée" },
  { value: 1, label: "Partiel" },
  { value: 2, label: "Maîtrisée" },
];

export default function UpdateRisk() {
  const { riskId } = useLocalSearchParams();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const [loading, setLoading] = useState(false);
  const { user, companySelected } = useContext(AppContext);
  const [process, setProcess] = useState<TypeProcess[]>([]);
  const router = useRouter();
  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
    control,
    reset,
    setValue,
  } = useForm<TypeRisk>({
    defaultValues: {
      process_id: 0,
      uid_user: "",
      event: "",
      possibility: 0,
      consequence: 0,
      description: "",
      level: 0,
    },
  });
  useEffect(() => {
    const fetchProcesses = async () => {
      const { data } = await supabase
        .from("process")
        .select()
        .eq("uid_admin", user?.uid)
        .eq("company_id", companySelected?.id);

      setProcess(
        data?.map((item: any) => ({
          label: item.process_name,
          value: item.id,
        })) || ([] as any)
      );
    };

    const fetchRisk = async () => {
      const { data } = await supabase
        .from("risks")
        .select("*")
        .eq("id", riskId)
        .single();

      if (data) {
        reset(data);
        setValue("process_id", data.process_id);
        setValue("possibility", data.possibility);
        setValue("consequence", data.consequence);
        setValue("level", data.level);
      }
    };

    fetchProcesses();
    fetchRisk();
  }, [riskId]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const inputs = [
    {
      label: "Processus concerné",
      name: "process_id",
      placeholder: "Processus concerné",
      type: "Select",
      data: process,
    },
    {
      label: "Événement redouté",
      name: "event",
      placeholder: "Événement redouté",
      type: "text",
      multiline: true,
    },
    {
      label: "Possibilité que cela arrive ?",
      name: "possibility",
      placeholder: "Possibilité que cela arrive ?",
      type: "Select",
      data: possibilities,
    },
    {
      label: "Conséquence potentielle ?",
      name: "consequence",
      placeholder: "Conséquence potentielle ?",
      type: "Select",
      data: consequences,
    },
    {
      label: "Description du dispositif de prévention",
      name: "description",
      placeholder: "Description",
      type: "text",
      multiline: true,
    },
    {
      label: "Niveau de maîtrise de risque ?",
      name: "level",
      placeholder: "Niveau de maîtrise de risque ?",
      type: "Select",
      data: levels,
    },
  ];

  const updateRisk = async (data: TypeRisk) => {
    setLoading(true);
    try {
      const { error } = await supabase
        .from("risks")
        .update(data)
        .eq("id", riskId);
      if (error) throw error;
      Toast.show({ type: "success", text1: "Mise à jour réussie" });

      router.back();
    } catch (err) {
      Toast.show({ type: "error", text1: "Erreur lors de la mise à jour" });
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.mainContainer}>
      <ScrollView style={styles.container}>
        <View style={styles.containerContent}>
          <View style={styles.registerInfo}>
            {inputs.map((input, index) => (
              <View key={index}>
                {input.type === "Select" ? (
                  <CustomizedSelect
                    name={input.name}
                    label={input.label}
                    register={register}
                    control={control}
                    errors={errors}
                    setError={setError}
                    data={input.data as any}
                  />
                ) : (
                  <View key={index}>
                    <InputCustomized
                      label={input.label}
                      placeholder={input.placeholder}
                      register={register}
                      name={input.name}
                      control={control}
                      errors={errors}
                      setError={setError}
                      multiline={input.multiline}
                    />
                  </View>
                )}
              </View>
            ))}
          </View>

          <View style={styles.buttons}>
            <ContainedButton
              label="Modifier"
              backgroundColor="#F99527"
              onPress={handleSubmit(updateRisk)}
              disabled={loading}
            />
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  containerContent: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  registerInfo: {
    width: "100%",
    gap: 20,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    gap: 20,
    width: "100%",
    marginTop: 20,
  },
  image: { width: 150, height: 150, marginVertical: 20, borderRadius: 150 },
});
