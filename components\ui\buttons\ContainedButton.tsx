import { StyleSheet, TouchableOpacity, Text, ActivityIndicator } from "react-native";
import React, { useState } from "react";

type TypesProps = {
  label: string;
  labelColor?: string;
  backgroundColor?: string;
  onPress?: () => Promise<void> | void; // Permet les fonctions async
  disabled?: boolean;
};

const ContainedButton = ({
  label,
  labelColor = "white",
  backgroundColor,
  onPress,
  disabled,
}: TypesProps) => {
  const [loading, setLoading] = useState(false);

  const handlePress = async () => {
    if (onPress) {
      setLoading(true);
      try {
        await onPress(); // Exécute la fonction et attend qu'elle se termine
      } catch (error) {
        console.error("Erreur lors de l'exécution du bouton :", error);
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <TouchableOpacity
      style={[styles.button, { backgroundColor: backgroundColor }, disabled || loading ? styles.disabledButton : {}]}
      onPress={handlePress}
      disabled={disabled || loading} // Désactive le bouton pendant le chargement
    >
      {loading ? (
        <ActivityIndicator color={labelColor} /> // ✅ Affiche un spinner pendant le chargement
      ) : (
        <Text style={[styles.label, { color: labelColor }]}>{label}</Text>
      )}
    </TouchableOpacity>
  );
};

export default ContainedButton;

const styles = StyleSheet.create({
  button: {
    width: "100%",
    padding: 12,
    borderRadius: 8,
    backgroundColor: "#f99527",
    justifyContent: "center",
    alignItems: "center",
  },
  disabledButton: {
    opacity: 0.5, // ✅ Réduit l'opacité du bouton quand il est désactivé
  },
  label: {
    textAlign: "center",
    fontSize: 16,
  },
});
