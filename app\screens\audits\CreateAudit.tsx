/*
app/screens/audits/CreateAudit.tsx

Formulaire pour créer un audit.

Informations pertinentes :

- Un audit est reliée à l'entreprise de l'utilisateur connecté qui la crée.
- On récupère les constats de l'audit depuis la table de jointure `audit_observations`
- Quand le formulaire est validé les données sont envoyées dans -> la table `audits`.
- Le champ `type` est en cours (`in_progress`) automatiquement.

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User` et `Extern` n'ont pas accès à cette page.
*/

import React, { useContext, useEffect, useState } from "react";
import {
  Alert,
  StyleSheet,
  View,
  TextInput,
  Text,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import { supabase } from "../../../lib/supabase.native";
import { Controller, useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AntDesign } from "@expo/vector-icons";
import { Dropdown } from "react-native-element-dropdown";
import DateTimePicker from "@react-native-community/datetimepicker";
import { DateTimePickerAndroid } from "@react-native-community/datetimepicker";
import { useRouter } from "expo-router";
import HeaderV2 from "@/components/common/HeaderV2.native";
import { WebDateTimePicker } from "@/components/ui/datepicker/WebDateTimePicker";

/* Choix du dropdown (audits)
const auditTypes = [
  { label: "Finalisé", value: "validated" },
  { label: "En cours", value: "in_progress" },
  { label: "En relecture", value: "invalid" },
];
*/

export default function CreateAudit() {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const router = useRouter();
  const { user, companySelected } = useContext(AppContext);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const {
    handleSubmit,
    formState: { errors },
    control,
    reset,
  } = useForm({
    defaultValues: {
      name: "",
      date: "",
      field: "",
      type: "in_progress",
      uid_company: "",
    },
    mode: "onBlur", // Validation sur perte de focus
    criteriaMode: "all", // Retourne toutes les erreurs par champ
  });

  // Fonction pour gérer les alertes selon la plateforme
  const showAlert = (title: string, message: string, onPress?: () => void) => {
    if (Platform.OS === 'web') {
      if (window.confirm(`${title}\n${message}`)) {
        onPress?.();
      }
    } else {
      Alert.alert(title, message, onPress ? [{ text: "OK", onPress }] : undefined);
    }
  };

  // Date (iOS)
  const handleDateChange = (event: any, date: any) => {
    if (date) {
      setSelectedDate(date); // Mettre à jour avec la date sélectionnée
    }
  };

  // Date (Android)
  const showDatePicker = () => {
    DateTimePickerAndroid.open({
      value: selectedDate,
      mode: "date",
      onChange: handleDateChange,
      display: "default",
    });
  };

  // Valider le formulaire
  const handleSubmitCreateAudit = async (data: any) => {
    try {
      setIsSubmitting(true);

      // Préparation des données pour la table `audits`
      const auditData = {
        name: data.name,
        date: selectedDate,
        field: data.field,
        type: "in_progress",
        company_id: companySelected?.id,
        created_at: new Date(),
      };

      // Insertion dans la table `audits`
      const { data: createdAudit, error: auditError } = await supabase
        .from("audits")
        .insert(auditData)
        .select("id")
        .single();

      if (auditError) {
        showAlert(
          "Échec de la création",
          auditError.message || "Une erreur est survenue."
        );
        return;
      }

      const auditId = createdAudit.id;

      // Confirmation de la création
      showAlert("Succès", "Audit créé avec succès.", () => {
        reset();
        router.back();
      });
    } catch (err) {
      showAlert("Erreur inattendue", "Veuillez réessayer plus tard.");
      console.error("Erreur inattendue :", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User"].includes(user.status)) {
      showAlert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page.",
        () => navigation.goBack()
      );
    }
  }, [user]);

  return (
    <>
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.select({ ios: 0, android: 100 })}
      >
        <ScrollView style={styles.form}>
          {/* Champ Nom */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Nom de l'audit</Text>
            <Controller
              control={control}
              name="name"
              rules={{ required: "Le nom est requis." }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  placeholderTextColor={"#6B7280"}
                  style={styles.input}
                  placeholder="Nom de l'audit"
                  onChangeText={onChange}
                  value={value}
                />
              )}
            />
            {errors.name && (
              <Text style={styles.errorText}>{errors.name.message}</Text>
            )}
          </View>

          {/* Date */}
          {/* Date Picker pour Web */}
          {Platform.OS === 'web' && (
            <View style={styles.dateTimePickerContainer}>
              <Text style={styles.label}>Date</Text>
              <WebDateTimePicker
                value={selectedDate}
                onChange={setSelectedDate}
                style={styles.datePicker}
              />
            </View>
          )}

          {/* Date Picker pour Android */}
          {Platform.OS === "android" && (
            <TouchableOpacity
              onPress={showDatePicker}
              style={styles.dateTimePickerContainer}
            >
              <Text style={styles.dateTimePickerLabel}>Date</Text>
              <Text style={styles.datePicker}>
                {selectedDate.toLocaleDateString()}
              </Text>
            </TouchableOpacity>
          )}

          {/* Date Picker pour iOS */}
          {Platform.OS === "ios" && (
            <View style={styles.dateTimePickerContainer}>
              <Text style={styles.label}>Date</Text>
              <DateTimePicker
                value={selectedDate}
                mode="date"
                display="default"
                onChange={handleDateChange}
                style={styles.datePicker}
              />
            </View>
          )}

          {/* Champ Champ */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Champ de l'audit</Text>
            <Controller
              control={control}
              name="field"
              rules={{ required: "Le champ est requis." }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  placeholderTextColor={"#6B7280"}
                  style={styles.input}
                  placeholder="Champ de l'audit"
                  onChangeText={onChange}
                  value={value}
                />
              )}
            />
            {errors.field && (
              <Text style={styles.errorText}>{errors.field.message}</Text>
            )}
          </View>

          {/* Champ Type 
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Types d'audits*</Text>
            <Controller
              control={control}
              name="type"
              rules={{ required: "Le type est requis." }}
              render={({ field: { onChange, value } }) => (
                <Dropdown
                  style={dropdownStyles.dropdown}
                  placeholderStyle={dropdownStyles.placeholderStyle}
                  selectedTextStyle={dropdownStyles.selectedTextStyle}
                  inputSearchStyle={dropdownStyles.inputSearchStyle}
                  iconStyle={dropdownStyles.iconStyle}
                  data={auditTypes}
                  search
                  maxHeight={300}
                  labelField="label"
                  valueField="value"
                  placeholder={"Choix du type"}
                  searchPlaceholder="Rechercher un type"
                  value={value}
                  onChange={(item) => {
                    onChange(item.value);
                  }}
                  renderLeftIcon={() => (
                    <AntDesign
                      style={dropdownStyles.icon}
                      color="black"
                      name="Safety"
                      size={20}
                    />
                  )}
                />
              )}
            />
          </View>
          */}

          {/* Bouton de soumission */}
          <TouchableOpacity
            style={styles.confirmButton}
            onPress={() => {
              handleSubmit(handleSubmitCreateAudit)();
            }}
            disabled={isSubmitting} // 🔥 Désactive le bouton pendant la soumission
          >
            {isSubmitting ? (
              <ActivityIndicator size="small" color="#fff" /> // 🔥 Affiche un spinner
            ) : (
              <Text style={styles.confirmButtonText}>Créer</Text>
            )}
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    height: "100%",
    width: "100%",
    alignItems: "center",
    alignContent: "center",
    backgroundColor: "#FFFFFF",
  },
  headSection: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 30,
  },
  backButton: {
    width: 24,
    textAlign: "left",
  },
  titleWrapper: {
    display: "flex",
    flexDirection: "row",
    marginVertical: 15,
  },
  title: {
    color: "#262627",
    fontSize: 17,
    textAlign: "left",
  },
  form: {
    width: "100%",
    alignSelf: "center",
    paddingHorizontal: 30,
    marginTop: 20,
    // marginBottom: 150,
  },
  input: {
    borderWidth: 2,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "auto",
    padding: 12,
    marginTop: 10,
    color: "#000000",
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginBottom: 5,
  },
  infoText: {
    fontSize: 14,
    marginVertical: 10,
    color: "#555",
  },
  inputContainer: {
    position: "relative",
    marginBottom: 20,
    width: "100%",
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
    textAlign: "left",
  },
  confirmButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    marginBottom: 16,
    backgroundColor: "#f99527",
    marginTop: 10,
  },
  confirmButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  imagePicker: {
    width: "100%",
    padding: 10,
    backgroundColor: "#FFFFFF",
    borderColor: "#CCC",
    borderRadius: 8,
    borderWidth: 2,
    alignItems: "center",
    marginBottom: 10,
    marginTop: 10,
  },
  imagePickerText: {
    color: "#1F2937",
    fontWeight: "bold",
  },
  imagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  imageContainer: {
    width: "100%",
    justifyContent: "center",
  },
  uploadPlaceholder: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  uploadIcon: {
    borderWidth: 1,
    borderColor: "#f99527",
    borderRadius: 7,
    padding: 5,
  },
  uploadText: {
    textAlign: "center",
    fontWeight: "normal",
  },
  fullImagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  dateTimePickerContainer: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    textAlign: "left",
    marginVertical: 20,
  },
  dateTimePickerLabel: {
    color: "#063E76",
    fontSize: 14,
    fontWeight: "bold",
    backgroundColor: "#FFF",
  },
  datePicker: {
    width: "100%",
    marginTop: 10,
    marginBottom: 10,
  },
});
