import TypeActionEvent from "./typeActionEvent";

type TypeEvent = {
  id: number;
  wording: string;
  description: string;
  causes: string;
  date: string;
  type: string;
  file: string[];
  created_at: string;
  event_actions?: any[]; // Si nécessaire
  uid_admin?: string; // Vérifie si cette clé est obligatoire
  name?: string;
  administrator?: string;
  immediateActions: any[]; // Ajout
  correctiveActions: any[]; // Ajout
};



export default TypeEvent;
