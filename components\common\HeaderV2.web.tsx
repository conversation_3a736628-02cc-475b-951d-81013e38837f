/*
components/common/HeaderV2.tsx
*/

import React, { useContext } from "react";
import { View, Text, StyleSheet, Image, TouchableOpacity } from "react-native";
import Octicons from "@expo/vector-icons/Octicons";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import Ionicons from "@expo/vector-icons/Ionicons";
import { usePathname, useRouter } from "expo-router";
import { Menu, MenuItem, MenuItemLabel } from "../ui/menu";
import { Divider } from "../ui/divider";
import { AppContext } from "@/state/AppContext";
import Feather from "@expo/vector-icons/Feather";
import { supabase } from "@/lib/supabase";

const tabs = [
  {
    path: "/event",
    title: "Événement QSE",
    icon: <Octicons name="diff-added" size={35} color="black" />,
  },
  {
    path: "/doc",
    title: "Mes documents",
    icon: <Octicons name="diff-added" size={35} color="black" />,
  },
  // {
  //   path: "/audit",
  //   title: "Audit",
  //   icon: <Octicons name="diff-added" size={35} color="black" />,
  // },
  {
    path: "/action",
    title: "Mes actions",
    icon: <Ionicons name="filter-sharp" size={35} color="black" />,
  },
  {
    path: "/objectif",
    title: "Objectifs",
    icon: <Ionicons name="filter-sharp" size={35} color="black" />,
  },
  {
    path: "/profil",
    title: "Profil",
  },
  {
    path: "/info/usersAdmin",
    title: "Mes utilisateurs",
  },
  {
    path: "/info/createUserByAdmin",
    title: "Créer un utilisateur",
  },
  {
    path: "/info/process",
    title: "Mes processus",
  },
  {
    path: "/info/createProcess",
    title: "Créer un processus",
  },
  {
    path: "/info/activities",
    title: "Mes activités",
  },
  {
    path: "/info/createActivity",
    title: "Créer une activité",
  },
  {
    path: "/objectif/createObjectif",
    title: "Créer un objectif",
  },
  {
    path: "/objectif/updateObjectif",
    title: "Modifier un objectif",
  },
  {
    path: "/objectif/detailsObjectf",
    title: "Objectif",
    icon: <Feather name="edit" size={24} color="black" />,
  },
  {
    path: "/objectif/createValue",
    title: "Ajouter une valeur",
  },
];

type propsType = {
  onPress?: any;
};

const HeaderV2 = ({ onPress }: propsType) => {
  const path_name = usePathname();
  const router = useRouter();

  const {
    setSession,
    session,
    user,
    platformUser,
    setPlatformUser,
    companySelected,
    setUser,
    setCompanySelected,
  } = useContext(AppContext);

  return (
    <View style={styles.headersContainer}>
      {/* top header */}

      <View style={styles.TopHeaderContainer}>
        <Menu
          placement={"bottom start" as any}
          offset={5}
          disabledKeys={["Settings"]}
          trigger={({ ...triggerProps }) => {
            return (
              <TouchableOpacity {...triggerProps}>
                <Octicons name="three-bars" size={30} color="white" />
              </TouchableOpacity>
            );
          }}
        >
          {platformUser ? (
            <MenuItem
              key="Cockpit"
              textValue="Cockpit"
              onPress={() => setPlatformUser(!platformUser)}
            >
              <MenuItemLabel size="sm">Cockpit</MenuItemLabel>
            </MenuItem>
          ) : (
            <MenuItem
              key="Espace utilisateur"
              textValue="Espace utilisateur"
              onPress={() => setPlatformUser(!platformUser)}
            >
              <MenuItemLabel size="sm">Espace utilisateur</MenuItemLabel>
            </MenuItem>
          )}
          <MenuItem key="E-learning" textValue="E-learning">
            <MenuItemLabel size="sm">E-learning</MenuItemLabel>
          </MenuItem>
          <MenuItem
            key="Se déconnecter"
            textValue="Se déconnecter"
            onPress={async () => {
              try {
                await supabase.auth.signOut(); // Déconnecte l'utilisateur de Supabase
                localStorage.removeItem("session");                
                setSession(null); // Met à jour l'état local
                setUser({
                  uid: "",
                  first_name: "",
                  last_name: "",
                  email: "",
                  status: "",
                  created_at: "",
                  profil_picture: "",
                  companies: [],
                  profil: "",
                }); // Réinitialise l'utilisateur
                setCompanySelected({
                  id: 0,
                  uid_user: "",
                  company_name: "",
                  created_at: "",
                }); // Supprime l'entreprise sélectionnée
                router.replace("/login"); // Utilise `replace` pour éviter le retour en arrière après la déconnexion
              } catch (error) {
                console.error("Erreur lors de la déconnexion :", error);
              }
            }}
          >
            <MenuItemLabel size="sm">Se déconnecter</MenuItemLabel>
          </MenuItem>

          {/* <MenuItem key="Settings" textValue="Settings">
            <Divider className="my-0.5" />
          </MenuItem> */}

          {user?.companies.map((company, index) => {
            return (
              <MenuItem
                key={index}
                textValue={company.company_name}
                onPress={() => setCompanySelected(company)}
                style={{
                  backgroundColor:
                    company?.company_name === companySelected?.company_name
                      ? "#f99527"
                      : "",
                }}
              >
                <MenuItemLabel size="sm">{company.company_name}</MenuItemLabel>
              </MenuItem>
            );
          })}

          <MenuItem
            key="Ajouter une nouvelle entreprise"
            textValue="Ajouter une nouvelle entreprise"
          >
            <MenuItemLabel size="sm">
              Ajouter une nouvelle entreprise
            </MenuItemLabel>
          </MenuItem>
        </Menu>

        {/* Logo */}

        <Image
          source={require("@/assets/images/login/logoIZYS_white.png")}
          style={styles.logo}
          resizeMode="contain"
        />

        {/* Profile Icon */}
        <TouchableOpacity onPress={() => router.navigate("/profil")}>
          {/* <Image
          source={require("../assets/icons/avatar.png")} // Remplacez par votre avatar
          style={styles.avatar}
        /> */}
          {user?.profil_picture ? (
            <Image
              source={{ uri: user?.profil_picture }}
              style={{
                width: 30,
                height: 30,
                // marginVertical: 20,
                borderRadius: 150,
              }}
            />
          ) : (
            <MaterialIcons name="account-circle" size={35} color="white" />
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  headersContainer: {},
  TopHeaderContainer: {
    height: 100,
    backgroundColor: "#1C3144",
    flexDirection: "row",
    alignItems: "flex-end",
    justifyContent: "space-between",
    paddingHorizontal: 30,
    paddingBlockEnd: 16,
  },
  BottomHeaderContainer: {
    height: 70,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 15,
  },
  logo: {
    width: 90,
    height: 40,
  },
});

export default HeaderV2;
