/*
app/pip/createPIP.tsx

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern` et `Extern` n'ont pas accès à cette page.
*/

import ContainedButton from "@/components/ui/buttons/ContainedButton";
import OutlinedButton from "@/components/ui/buttons/OutlinedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useRouter } from "expo-router";
import { StyleSheet, Image, View, ScrollView, Alert, Platform } from "react-native";

import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import CustomizedSelect from "@/components/ui/customizedSelect/customizedSelect";
import TypePIP from "@/types/typePIP";
import { useNavigation } from "expo-router";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { ParamListBase } from "@react-navigation/native";

const impacts = [
  { value: 0, label: "Influence majeure, peut stopper l'activité", weight: 5 },
  { value: 1, label: "Influence significative sur les opérations", weight: 4 },
  { value: 2, label: "Peut affecter certaines activités", weight: 3 },
  { value: 3, label: "Influence légère, peu de conséquences", weight: 2 },
  { value: 4, label: "Pas d'influence sur l'entreprise", weight: 1 },
];

const interests = [
  {
    value: 0,
    label: "Essentiel pour la réussite de l'entreprise",
    weight: 5,
  },
  { value: 1, label: "Partenariat ou relation stratégique", weight: 4 },
  { value: 2, label: "Assez important pour être suivi", weight: 3 },
  {
    value: 3,
    label: "Faiblement lié aux objectifs de l'entreprise",
    weight: 2,
  },
  { value: 4, label: "Aucune préoccupation pour l'entreprise", weight: 1 },
];

const levels = [
  { value: 0, label: "Non maîtrisée" },
  { value: 1, label: "Partiel" },
  { value: 2, label: "Maîtrisée" },
];

const inputs = [
  {
    label: "Nom de la partie intéressée",
    name: "pip_name",
    placeholder: "Nom de la partie intéressée",
    type: "text",
  },
  {
    label: "Attentes",
    name: "expectations",
    placeholder: "Attentes",
    type: "text",
    multiline: true, 

  },
  {
    label: "Impact de la PIP?",
    name: "impact",
    placeholder: "Possibilité que cela arrive ?",
    type: "Select",
    data: impacts,
  },
  {
    label: "Intérêt pour la PIP",
    name: "interest",
    placeholder: "Intérêt pour la PIP",
    type: "Select",
    data: interests,
  },
  {
    label: "Dispositif d'écoute",
    name: "listening_device",
    placeholder: "Dispositif d'écoute",
    type: "text",
    multiline: true, 

  },
  {
    label: "Niveau de maîtrise de la PIP ?",
    name: "level",
    placeholder: "Niveau de maîtrise de la PIP ?",
    type: "Select",
    data: levels,
  },
];

export default function CreatePIPs() {
  const [loading, setLoading] = useState(false);
  const { user, companySelected } = useContext(AppContext);
  const router = useRouter();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const {
    register,
    handleSubmit,
    setError,
    formState: { errors, touchedFields },
    control,
    reset,
    watch,
  } = useForm<TypePIP>({
    defaultValues: {
      created_at: "",
      uid_user: "",
      pip_name: "",
      expectations: "",
      interest: 0,
      level: 0,
      company_id: 0,
      listening_device: "",
    },
  });

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const createPIP = async (data: TypePIP) => {
    setLoading(true);

    const created_at = new Date();

    try {
      const { error } = await supabase.from("pips").insert({
        created_at: created_at,
        uid_user: user?.uid,
        pip_name: data.pip_name,
        expectations: data.expectations,
        interest: data.interest,
        listening_device: data.listening_device,
        level: data.level,
        company_id: companySelected?.id,
        impact: data?.impact,
      });

      if (error) {
        console.log("error__", error);
        Toast.show({
          type: "error",
          text1: "Erreur lors d'enregistrement de la pip",

          text1Style: { color: "#1C3144" },
        });
        setLoading(false);
        return;
      }

      Toast.show({
        type: "success",
        text1: "La PIP était bien crée",
        text1Style: { color: "#1C3144" },
      });

      router.back();

      reset();
      setLoading(false);
    } catch (err) {
      setLoading(false);
      Toast.show({
        type: "error",
        text1: "Une erreur est survenue",
        text1Style: { color: "#1C3144" },
      });
      // }
    }
  };

  return (
    <View style={styles.mainContainer}>
      <ScrollView style={styles.container}>
        <View style={styles.containerContent}>
          <View style={styles.registerInfo}>
            <View style={styles.inputs}>
              {inputs.map((input, index) => {
                if (input?.type === "Select") {
                  return (
                    <View key={index}>
                      <CustomizedSelect
                        name={input.name}
                        label={input.label}
                        register={register}
                        control={control}
                        errors={errors}
                        setError={setError}
                        data={input.data as any}
                      />
                    </View>
                  );
                } else {
                  return (
                    <View key={index}>
                      <InputCustomized
                        label={input.label}
                        placeholder={input.placeholder}
                        register={register}
                        name={input.name}
                        control={control}
                        errors={errors}
                        setError={setError}
                        multiline={input.multiline}
                      />
                    </View>
                  );
                }
              })}
            </View>
          </View>

          <View style={styles.buttons}>
            <ContainedButton
              label="Créer"
              backgroundColor="#F99527"
              onPress={handleSubmit(createPIP)}
              disabled={loading}
            />
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    alignItems: 'center',
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  containerContent: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    width: "100%",
    gap: 20,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
    marginTop: 20,
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
});
