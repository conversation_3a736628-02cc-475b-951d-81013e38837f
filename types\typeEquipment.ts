import { Timestamp } from "react-native-reanimated/lib/typescript/commonTypes";

type TypeEquipment = {
  id: number;
  uuid: string;
  user_id?: string;
  company_id: number;
  name: string;
  reference?: string;
  type: string;
  cycle?: string;
  status?: string;
  document?: string;
  image?: string;
  price: number;
  purchase_date: Date | Timestamp | any;
  verification_date: Date | Timestamp | any;
  created_at: Date | Timestamp | any;
  updated_at: Date | Timestamp | any;
  users?: {
    first_name: string;
    last_name: string;
  };
};

export default TypeEquipment;
