import {
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { Input, InputField } from "../input/index.native";
import { Controller } from "react-hook-form";
import { useEffect } from "react";
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";

type TypesProps = {
  label?: string;
  placeholder?: string;
  onPress?: any;
};

const InputFileCustomized = ({ label, placeholder, onPress }: TypesProps) => {
  return (
    <View style={styles.container}>
      {label && <Text style={styles.label}>{label}</Text>}

      <TouchableOpacity style={styles.containerInput} onPress={onPress}>
        <View style={styles.download}>
          <MaterialCommunityIcons name="download" size={24} color="black" />
        </View>
        <Text style={{ color: "#737373" }}>{placeholder}</Text>
      </TouchableOpacity>
    </View>
  );
};

export default InputFileCustomized;

const styles = StyleSheet.create({
  container: {
    width: "100%",
  },
  containerInput: {
    width: "100%",
    // height: 150,
    borderRadius: 8,
    borderStyle: "solid",
    borderWidth: 1,
    borderColor: "#d3d3d3",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    gap: 10,
    paddingVertical: 20,
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginTop: 5,
  },
  download: {
    borderRadius: 8,
    borderStyle: "solid",
    borderWidth: 1,
    borderColor: "#F99527",
    padding: 8,
  },
  label: {
    fontSize: 14,
    color: "#262627",
    marginBottom: 10,
    fontWeight: "bold",
  },
});
