/*
app/screens/audits/AuditDetails.tsx

Page de détails d'un audit.

Informations pertinentes :

- Les informations de l'audit sont récupérées depuis les paramètres de la navigation du composant `audit.tsx`.
- On récupère les constats d'un audit depuis la table de jointure `audit_observations`.
- Si l'audit n'a pas de constats on affiche un bouton `Créer` à la place.
*/

import {
  Alert,
  FlatList,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import {
  ParamListBase,
  useNavigation,
  useFocusEffect,
} from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { Octicons } from "@expo/vector-icons";
import { useRoute, RouteProp } from "@react-navigation/native";
import React, { useContext, useState } from "react";
import moment from "moment";
import { supabase } from "@/lib/supabase";
import { AppContext } from "@/state/AppContext";
import { useRouter, useLocalSearchParams } from "expo-router";
import Header from "@/components/common/Header";

// Données de navigation
type AuditDetailsRouteParams = {
  audit: {
    id: number;
    name: string;
    type: string;
    date: string;
    conclusion: string;
    observation?: {
      id: number;
      conclusion: string;
    };
    companyId: number;
    archived?: boolean;
  };
};

// Gestion des types
const getObservationTypeStyle = (type: string) => {
  switch (type) {
    case "sensitive_point":
      return { text: "Point sensible", backgroundColor: "#2FC12B" };
    case "non_compliant":
      return { text: "Non conforme", backgroundColor: "#F93C27" };
    case "note":
      return { text: "Note", backgroundColor: "#CACACA" };
    default:
      return { text: "Piste de progrès", backgroundColor: "#277FF9" }; // Valeur par défaut
  }
};

type AuditState = {
  id: number;
  name: string;
  type: string;
  date: string;
  conclusion: string;
  observation?: {
    id: number;
    conclusion: string;
  };
  companyId: number;
  archived: boolean;
};

export default function AuditDetails() {
  const params = useLocalSearchParams();
  const [observationsList, setObservationsList] = useState<any>([]);
  const [loading, setLoading] = useState(true);
  const { user, companySelected } = useContext(AppContext);
  const router = useRouter();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const [audit, setAudit] = useState<AuditState>({
    ...params as unknown as AuditState,
    archived: params.archived === 'true'
  }); // ✅ Stocker localement les données de l'audit avec une valeur par défaut pour archived
  
  if (!audit) {
    return <Text>Erreur : Audit introuvable.</Text>;
  }
  const fetchAuditDetails = async () => {
    try {
      const { data, error } = await supabase
        .from("audits")
        .select("*, archived")
        .eq("id", audit.id)
        .single();
  
      if (error) {
        console.error("Erreur lors du rafraîchissement de l'audit :", error);
        return;
      }
  
      setAudit(data); // ✅ Met à jour l'état de l'audit avec toutes les données incluant archived
    } catch (err) {
      console.error("Erreur inattendue :", err);
    }
  };

  
  // Récupérer la liste des constats de l'audit
  useFocusEffect(
    React.useCallback(() => {
      if (!audit.id || !companySelected) return; // ✅ Vérifier que l'audit et l'entreprise existent
  
      fetchAuditDetails(); // ✅ Rafraîchir l'audit
  
      const fetchObservations = async () => {
        try {
          // Étape 1 : Vérifier si l'audit a des constats
          const { data: auditObservations, error: auditObservationError } =
            await supabase
              .from("audit_observations")
              .select("observation_id")
              .eq("audit_id", audit.id);
  
          if (auditObservationError) {
            console.error(
              "Erreur lors de la récupération des constats liés à l'audit :",
              auditObservationError.message
            );
            return;
          }
  
          if (!auditObservations || auditObservations.length === 0) {
            console.warn("⚠ Aucun constat trouvé pour cet audit !");
            setObservationsList([]); // ✅ Met la liste vide mais ne casse pas l'affichage
            return;
          }
  
          const observationIds = auditObservations.map(
            (item) => item.observation_id
          );
  
          // Étape 2 : Récupérer les détails des constats via leurs IDs
          const { data: observationsData, error: observationsError } =
            await supabase
              .from("observations")
              .select("id, name, created_at, type, requirements, process_id")
              .in("id", observationIds);
  
          if (observationsError) {
            console.error(
              "Erreur lors de la récupération des observations :",
              observationsError.message
            );
            return;
          }
  
          setObservationsList(observationsData);
        } catch (err) {
          console.error("Erreur inattendue :", err);
        } finally {
          setLoading(false);
        }
      };
  
      fetchObservations();
    }, [audit.id, companySelected]) // ✅ Ajout de `audit.id` dans la dépendance
  );
  

  // 🔥 Fonction de suppression complète d'un Audit et de ses Observations associées
  const handleDeleteAudit = async () => {
    const confirmDelete =
      Platform.OS === "web"
        ? window.confirm("Êtes-vous sûr de vouloir supprimer cet Audit et toutes ses observations ?")
        : await new Promise((resolve) =>
            Alert.alert(
              "Confirmation",
              "Êtes-vous sûr de vouloir supprimer cet Audit et toutes ses observations associées ?",
              [
                { text: "Annuler", style: "cancel", onPress: () => resolve(false) },
                { text: "Oui, supprimer", onPress: () => resolve(true) },
              ]
            )
          );

    if (!confirmDelete) return;

    try {
      // 🔥 1. Récupérer toutes les observations associées à cet audit
      const { data: auditObservations, error: auditObservationsError } = await supabase
        .from("audit_observations")
        .select("observation_id")
        .eq("audit_id", audit.id);

      if (auditObservationsError) throw auditObservationsError;

      const observationIds = auditObservations.map((obs) => obs.observation_id);

      // 🔥 2. Supprimer les relations dans `audit_observations`
      const { error: deleteRelationsError } = await supabase
        .from("audit_observations")
        .delete()
        .eq("audit_id", audit.id);

      if (deleteRelationsError) throw deleteRelationsError;
      console.log(`✅ Relations audit_observations supprimées`);

      // 🔥 3. Supprimer les observations associées
      if (observationIds.length > 0) {
        const { error: deleteObservationsError } = await supabase
          .from("observations")
          .delete()
          .in("id", observationIds);

        if (deleteObservationsError) throw deleteObservationsError;
        console.log(`✅ Observations supprimées`);
      }

      // 🔥 4. Supprimer l'audit lui-même
      const { error: deleteAuditError } = await supabase
        .from("audits")
        .delete()
        .eq("id", audit.id);

      if (deleteAuditError) throw deleteAuditError;

      alert("L'audit et ses observations associées ont été supprimés avec succès.");
      navigation.goBack();
    } catch (error) {
      console.error("❌ Erreur lors de la suppression :", error);
      alert("Une erreur est survenue lors de la suppression.");
    }
  };

  // 🔄 Fonction pour archiver/désarchiver un audit
  const handleArchiveAudit = async () => {
    const isArchived = typeof audit.archived === 'string' ? audit.archived === 'true' : audit.archived;
    const confirmAction =
      Platform.OS === "web"
        ? window.confirm(
            `Êtes-vous sûr de vouloir ${isArchived ? "désarchiver" : "archiver"} cet audit ?`
          )
        : await new Promise((resolve) =>
            Alert.alert(
              "Confirmation",
              `Êtes-vous sûr de vouloir ${isArchived ? "désarchiver" : "archiver"} cet audit ?`,
              [
                { text: "Annuler", style: "cancel", onPress: () => resolve(false) },
                { text: "Oui", onPress: () => resolve(true) },
              ]
            )
          );

    if (!confirmAction) return;

    try {
      const { error } = await supabase
        .from("audits")
        .update({ archived: !isArchived })
        .eq("id", audit.id);

      if (error) throw error;

      // Mettre à jour l'état local de l'audit
      setAudit({ ...audit, archived: !isArchived });
      
      alert(
        `L'audit a été ${isArchived ? "désarchivé" : "archivé"} avec succès.`
      );
    } catch (error) {
      console.error("❌ Erreur lors de l'archivage :", error);
      alert("Une erreur est survenue lors de l'archivage.");
    }
  };

  return (
    <>
    <Header
      onPressIcon={() => {
        if (!!audit?.conclusion && audit?.conclusion !== "Non renseignée") {
          Alert.alert(
            "Audit clôturé",
            "Cet audit est clôturé, vous ne pouvez pas modifier ses informations."
          );
          return;
        }
        navigation.navigate("screens/audits/UpdateAudit", { audit });
      }}
    />

    <ScrollView 
      contentContainerStyle={{
        flexGrow: 1,
        paddingHorizontal: Platform.OS === "web" ? 0 : 30,
        paddingBottom: 500,
      }}
    >
      <View style={Platform.OS === "web" ? { flex: 1, alignItems: "center" } : { flex: 1 }}>
        <View style={Platform.OS === "web" ? { width: "100%", maxWidth: 800 } : { width: "100%" }}>
          {/* Informations de l'audit */}
          <View style={styles.auditCard}>
            <Text style={styles.cardName}>{audit?.name}</Text>
            <Text style={styles.date}>{moment(audit?.date).format("DD/MM/YYYY")}</Text>
          </View>

          <View style={styles.auditCard}>
            <Text style={styles.cardName}>
              Conclusion : {audit?.conclusion ?? "Non renseignée"}
            </Text>
          </View>

          {/* Section des constats */}
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>Les constats</Text>
            <TouchableOpacity
              onPress={() => {
                if (!!audit?.conclusion && audit?.conclusion !== "Non renseignée") {
                  Alert.alert(
                    "Audit clôturé",
                    "Cet audit est clôturé, vous ne pouvez pas modifier ses informations."
                  );
                  return;
                }
                navigation.navigate("screens/observations/CreateObservation", { audit });
              }}
              style={styles.sectionButton}
            >
              <Octicons name="diff-added" size={24} color="#F39415" />
            </TouchableOpacity>
          </View>

          {/* Liste des constats */}
          {loading ? (
            <Text>Chargement...</Text>
          ) : (
            observationsList.length > 0 ? (
              observationsList.map((item) => {
                const { text, backgroundColor } = getObservationTypeStyle(item.type);
                return (
                  <TouchableOpacity
                    key={item.id}
                    onPress={() =>
                      navigation.navigate("screens/observations/DetailObservation", {
                        audit,
                        observation: item,
                      })
                    }
                    style={styles.listItem}
                  >
                    <View style={styles.elementsTop}>
                      <Text style={styles.listName}>{item.name}</Text>
                      <Text style={styles.date}>
                        {moment(item.created_at).format("DD/MM/YYYY")}
                      </Text>
                    </View>
                    <View style={styles.elementsBottom}>
                      <Text style={[styles.texts, { backgroundColor }]}>{text}</Text>
                      <Octicons name="chevron-right" size={24} color="black" />
                    </View>
                  </TouchableOpacity>
                );
              })
            ) : (
              <Text>Aucun constat trouvé.</Text>
            )
          )}

          {/* Bouton de suppression visible uniquement pour les Admins et si l'audit n'est pas clôturé */}
          {user?.status === "Admin" && (!audit?.conclusion || audit?.conclusion === "Non renseignée") && (
            <View style={styles.deleteButtonContainer}>
              <TouchableOpacity style={styles.deleteButton} onPress={handleDeleteAudit}>
                <Text style={styles.deleteButtonText}>Supprimer cet Audit</Text>
              </TouchableOpacity>
            </View>
          )}

          {/* Bouton d'archivage/désarchivage */}
          {audit?.conclusion && audit?.conclusion !== "Non renseignée" && (
            <TouchableOpacity
              style={[
                styles.archiveButton,
                (typeof audit?.archived === 'string' && audit?.archived === 'true') || audit?.archived === true
                  ? styles.unarchiveButton
                  : undefined
              ]}
              onPress={handleArchiveAudit}
            >
              <Text style={styles.archiveButtonText}>
                {(typeof audit?.archived === 'string' && audit?.archived === 'true') || audit?.archived === true
                  ? "Désarchiver l'audit"
                  : "Archiver l'audit"}
              </Text>
            </TouchableOpacity>
          )}

          {/* Bouton placé tout en bas */}
          <TouchableOpacity
            style={[
              styles.confirmButton,
              audit?.conclusion && audit?.conclusion !== "Non renseignée"
                ? styles.disabledButton
                : {},
            ]}
            onPress={() =>
              navigation.navigate("screens/observations/CreateCloture", { audit })
            }
            disabled={!!audit?.conclusion && audit?.conclusion !== "Non renseignée"}
          >
            <Text style={styles.confirmButtonText}>Conclure l'audit</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>

      
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 20,
    marginBottom: 10,

  },
  scrollContainer: {
    flexGrow: 1, // ✅ Permet un bon scroll
    paddingHorizontal: 30,
    paddingBottom: 500, // ✅ Assure que le bouton ne soit pas collé en bas
  },
  headSection: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  confirmButton: {
    borderRadius: 7,
    width: "100%",
    padding: 12,
    backgroundColor: "#f99527",
    marginTop: 20, // ✅ Espacement avant le bouton
    marginBottom: 40, // ✅ Le fait descendre plus bas
    alignSelf: "center",
  },
  confirmButtonText: {
    textAlign: "center",
    color: "#FFFFFF",
    fontSize: 14,
  },
  disabledButton: {
    backgroundColor: "#ccc",
  },
  backButton: {
    width: 24,
    textAlign: "left",
  },
  titleWrapper: {
    display: "flex",
    flexDirection: "row",
    marginVertical: 15,
  },
  title: {
    color: "#262627",
    fontSize: 17,
    textAlign: "left",
  },
  sectionButton: {},
  sectionButtonText: {
    fontSize: 14,
    textDecorationLine: "underline",
    color: "#F99527",
    marginVertical: 10,
    
  },
  listContainer: {
    width: "100%",
    paddingVertical: 10,
    
  },
  listItem: {
    width: "100%",
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    display: "flex",
    justifyContent: "space-between",
    flexDirection: "column",
    gap: 10,
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    
  },
  listTitle: {
    fontSize: 16,
    color: "#1C3144",
    fontWeight: "bold",
  },
  auditCard: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "#CCC",
    borderRadius: 8,
    padding: 8,
    paddingVertical: 25,
    marginVertical: 10,
  },
  cardName: {
    fontSize: 14,
    flexWrap: "wrap",
  },
  leftElementCard: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  elementsTop: {
    display: "flex",
    flexDirection: "column",
    rowGap: 5,
  },
  elementsBottom: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  listName: {
    width: "100%",
    fontSize: 14,
    color: "#262627",
    fontStyle: "normal",
    flexWrap: "wrap",
    fontWeight: "semibold",
  },
  listDescription: {
    width: "70%",
    fontSize: 14,
    color: "#8c8c8c",
    fontStyle: "normal",
    flexWrap: "wrap",
  },
  topElementsCard: {
    backgroundColor: "#FFFFFF",
  },
  bottomElementsCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 7,
    justifyContent: "space-between",
    flexDirection: "column",
    rowGap: 5,
    
  },
  createAuditButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    marginBottom: 16,
    backgroundColor: "#f99527",
    marginTop: 10,
  },
  createAuditButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  sectionContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#1C3144",
    marginVertical: 10,
  },
  date: {
    fontSize: 14,
    color: "#525252",
    fontStyle: "normal",
    flexWrap: "wrap",
    fontWeight: "normal",
  },
  texts: {
    fontSize: 10,
    color: "#FFFFFF",
    padding: 8,
    borderRadius: 50,
    fontStyle: "normal",
    flexWrap: "wrap",
    fontWeight: "normal",
    textAlign: "center",
    minWidth: 100,
  },

  deleteButtonContainer: {
    width: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 30,
  },
  deleteButton: {
    width: "100%",
    backgroundColor: "#ff4f42",
    padding: 10,
    borderRadius: 8,
    alignItems: "center",
  },
  deleteButtonText: {
    color: "#FFFFFF",
    fontWeight: "normal",
  },
  archiveButton: {
    width: "100%",
    backgroundColor: "#4CAF50",
    padding: 12,
    borderRadius: 7,
    marginTop: 20,
    marginBottom: 20,
  },
  unarchiveButton: {
    backgroundColor: "#2196F3",
  },
  archiveButtonText: {
    textAlign: "center",
    color: "#FFFFFF",
    fontSize: 14,
  },
});
