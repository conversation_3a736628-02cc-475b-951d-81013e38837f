import ContainedButton from "@/components/ui/buttons/ContainedButton";
import OutlinedButton from "@/components/ui/buttons/OutlinedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useRouter, useLocalSearchParams, useFocusEffect } from "expo-router";
import {
  StyleSheet,
  Image,
  View,
  Text,
  Linking,
  Alert,
  TextInput,
  ScrollView,
  TouchableOpacity,
  Platform,
} from "react-native";

import { useCallback, useContext, useEffect, useState } from "react";
import { Octicons } from "@expo/vector-icons";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import TypeValues from "@/types/typeValues";
import PaperInfo from "@/components/common/paperInfo";
import Paper from "@/components/common/paper";
import TypePIP from "@/types/typePIP";
import Header from "@/components/common/Header";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";

const items = [
  {
    label: "Nom de la partie intéressée",
    name: "pip_name",
    placeholder: "Nom de la partie intéressée",
  },
  {
    label: "Attentes",
    name: "expectations",
    placeholder: "Attentes",
  },
  {
    label: "Impact de la PIP?",
    name: "impact",
    placeholder: "Possibilité que cela arrive ?",
  },
  {
    label: "Intérêt pour la PIP",
    name: "interest",
    placeholder: "Intérêt pour la PIP",
  },
  {
    label: "Dispositif d'écoute",
    name: "listening_device",
    placeholder: "Dispositif d'écoute",
  },
  {
    label: "Niveau de maîtrise de la PIP ?",
    name: "level",
    placeholder: "Niveau de maîtrise de la PIP ?",
  },
];

const impacts = [
  { value: 0, label: "Influence majeure, peut stopper l'activité", weight: 5 },
  { value: 1, label: "Influence significative sur les opérations", weight: 4 },
  { value: 2, label: "Peut affecter certaines activités", weight: 3 },
  { value: 3, label: "Influence légère, peu de conséquences", weight: 2 },
  { value: 3, label: "Pas d'influence sur l'entreprise", weight: 1 },
];

const interests = [
  {
    value: 0,
    label: "Essentiel pour la réussite de l'entreprise",
    weight: 5,
  },
  { value: 1, label: "Partenariat ou relation stratégique", weight: 4 },
  { value: 2, label: "Assez important pour être suivi", weight: 3 },
  {
    value: 3,
    label: "Faiblement lié aux objectifs de l'entreprise",
    weight: 2,
  },
  { value: 4, label: "Aucune préoccupation pour l'entreprise", weight: 1 },
];

const levels = [
  { value: 0, label: "Non maîtrisée" },
  { value: 1, label: "Partiel" },
  { value: 2, label: "Maîtrisée" },
];

export default function detailsPIP() {
  const { user } = useContext(AppContext);

  const router = useRouter();

  const [data, setData] = useState<TypePIP>();

  const { pip_name, id, score } = useLocalSearchParams();

    const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  

  const getLabel = (name: string, value: number | string) => {
    if (value === "Non renseigné") return value; // ✅ Retourne directement si c'est une valeur par défaut
  
    if (name === "impact") {
      return impacts.find((item) => item.value === value)?.label || "Valeur inconnue";
    }
    if (name === "interest") {
      return interests.find((item) => item.value === value)?.label || "Valeur inconnue";
    }
    if (name === "level") {
      return levels.find((item) => item.value === value)?.label || "Valeur inconnue";
    }
    return value; // ✅ Retourne la valeur brute si ce n'est pas un champ spécifique
  };

  // 🔥 Fonction de suppression d'un PIP
  const handleDeletePIP = async () => {
    const confirmDelete =
      Platform.OS === "web"
        ? window.confirm("Êtes-vous sûr de vouloir supprimer ce PIP ?")
        : await new Promise((resolve) =>
            Alert.alert(
              "Confirmation",
              "Êtes-vous sûr de vouloir supprimer ce PIP ?",
              [
                { text: "Annuler", style: "cancel", onPress: () => resolve(false) },
                { text: "Oui, supprimer", onPress: () => resolve(true) },
              ]
            )
          );

    if (!confirmDelete) return;

    try {
      const { error } = await supabase
        .from("pips")
        .delete()
        .eq("id", id);

      if (error) throw error;

      alert("Le PIP a été supprimé avec succès.");
      navigation.goBack();
    } catch (error) {
      console.error("❌ Erreur lors de la suppression :", error);
      alert("Une erreur est survenue lors de la suppression.");
    }
  };

  

  const fetchData = async () => {
    const { data: dataGetted, error } = await supabase
      .from("pips")
      .select("*, companies(*)")
      .eq("id", id);
  
    if (!error && dataGetted?.length > 0) {
      const pip = dataGetted[0];
  
      const formattedData = items.map(({ name, label }) => {
        let value = pip[name] ?? "Non renseigné"; // ✅ Évite les valeurs null ou undefined
  
        if (["impact", "interest", "level"].includes(name)) {
          value = getLabel(name, value) || "Non défini"; // ✅ Vérifie si getLabel() retourne bien un texte
        }
  
        return { label, value };
      });
  
      setData(formattedData as any);
    } else {
      console.error("Erreur de récupération des données :", error);
    }
  };
  

  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [])
  );

  return (
    <View style={styles.mainContainer}>
      <Header
        title={"PIP concerné"}
        onPressIcon={() =>
          router.push({
            pathname: "/pip/updatePIP",
            params: { pipId: id },
          })
        }
      />

      <ScrollView style={styles.container}>
        <View style={styles.containerContent}>
          <Paper title={pip_name as any} score={score as any} data={data} />
          {/* 🔥 Bouton de suppression visible uniquement pour les Admins */}
          {user?.status === "Admin" && (
            <View style={styles.deleteButtonContainer}>
              <TouchableOpacity style={styles.deleteButton} onPress={handleDeletePIP}>
                <Text style={styles.deleteButtonText}>Supprimer ce PIP</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  containerContent: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  deleteButtonContainer: {
    width: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 30,
  },
  deleteButton: {
    width: 200,
    backgroundColor: "#ff4f42",
    padding: 10,
    borderRadius: 8,
    alignItems: "center",
  },
  deleteButtonText: {
    color: "#FFFFFF",
    fontWeight: "normal",
  },
});
