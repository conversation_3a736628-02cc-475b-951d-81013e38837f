import React from 'react';
import { View, StyleSheet } from 'react-native';

interface WebDateTimePickerProps {
  value: Date;
  onChange: (event: any, date?: Date) => void;
  style?: any;
}

export const WebDateTimePicker: React.FC<WebDateTimePickerProps> = ({ value, onChange, style }) => {
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newDate = new Date(event.target.value);
    if (!isNaN(newDate.getTime())) {
      onChange(null, newDate);
    }
  };

  // Format the date to YYYY-MM-DD for the input
  const formattedDate = value.toISOString().split('T')[0];

  return (
    <View style={[styles.container, style]}>
      <input
        type="date"
        value={formattedDate}
        onChange={handleChange}
        className="date-picker-input"
        min="1900-01-01"
        max="2100-12-31"
      />
      <style>
        {`
          .date-picker-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ccc;
            border-radius: 8px;
            font-size: 16px;
            color: #000000;
            background-color: #FFFFFF;
            cursor: pointer;
          }
          .date-picker-input:focus {
            outline: none;
            border-color: #F99527;
          }
          .date-picker-input:hover {
            border-color: #F99527;
          }
        `}
      </style>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
}); 