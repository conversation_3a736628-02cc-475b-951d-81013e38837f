/*
app/objectif/updateSupplier.tsx

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern` et `Extern` n'ont pas accès à cette page.
*/

import ContainedButton from "@/components/ui/buttons/ContainedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useLocalSearchParams, useRouter } from "expo-router";
import { StyleSheet, View, ScrollView, Alert, Platform } from "react-native";
import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import TypeSupplier from "@/types/typeSupplier";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import CustomizedSelect from "@/components/ui/customizedSelect/customizedSelect";

const supplierStatus = [
  { value: "good", label: "Bon" },
  { value: "regular", label: "Moyen" },
  { value: "bad", label: "Mauvais" },
];

const inputs = [
  {
    label: "Nom de l'entreprise",
    name: "company_name",
    placeholder: "Nom de l'entreprise du fournisseur",
    type: "text",
  },
  {
    label: "Nom",
    name: "last_name",
    placeholder: "Nom de famille du fournisseur",
    type: "text",
  },
  {
    label: "Prénom",
    name: "first_name",
    placeholder: "Prénom du fournisseur",
    type: "text",
  },
  {
    label: "Numéro de téléphone",
    name: "phone_number",
    placeholder: "Numéro de téléphone du fournisseur",
    type: "text",
  },
  {
    label: "Statut",
    name: "status",
    placeholder: "Statut du fournisseur",
    type: "Select",
    data: supplierStatus,
  },
];

export default function handleCreateSuppliers() {
  const { user, companySelected } = useContext(AppContext);
  const params = useLocalSearchParams();
  const router = useRouter();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const [loading, setLoading] = useState(false);

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
    control,
    reset,
    setValue,
  } = useForm<TypeSupplier>({
    defaultValues: {
      company_name: "",
      last_name: "",
      first_name: "",
      phone_number: "",
      status: "regular",
    },
  });

  // Récupérer les informations du fournisseur
  useEffect(() => {
    const fetchSupplier = async () => {
      if (!params.supplierId || typeof params.supplierId !== "string") {
        console.error("Erreur : supplierId est invalide.", params.supplierId);
        return;
      }
    
      console.log("Recherche du fournisseur avec UID :", params.supplierId);
    
      // Récupérer les informations de la table suppliers
      const { data: supplierData, error: supplierError } = await supabase
        .from("suppliers")
        .select("*")
        .eq("uid_user", params.supplierId)
        .single();
    
      if (supplierError) {
        console.error("Erreur lors de la récupération du fournisseur :", supplierError);
        return;
      }

      // Récupérer les informations de la table users
      const { data: userData, error: userError } = await supabase
        .from("users")
        .select("*")
        .eq("uid", params.supplierId)
        .single();
    
      if (userError) {
        console.error("Erreur lors de la récupération des informations utilisateur :", userError);
        return;
      }
    
      if (supplierData && userData) {
        console.log("Données du fournisseur récupérées :", supplierData);
        console.log("Données utilisateur récupérées :", userData);
    
        // Mettre à jour le formulaire avec les données des deux tables
        setValue("company_name", supplierData.company_name);
        setValue("status", supplierData.status);
        setValue("first_name", userData.first_name);
        setValue("last_name", userData.last_name);
        setValue("phone_number", userData.phone_number || "");
      }
    };
    
    fetchSupplier();
  }, [params.supplierId]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern", "Supplier"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const handleUpdateSupplier = async (data: TypeSupplier) => {
    if (!params.supplierId || typeof params.supplierId !== "string") {
      Toast.show({ 
        type: "error", 
        text1: "ID du fournisseur invalide",
        text1Style: { color: "#1C3144" }
      });
      return;
    }

    setLoading(true);
    try {
      // Mise à jour dans la table suppliers
      const { error: supplierError } = await supabase
        .from("suppliers")
        .update({
          company_name: data.company_name,
          status: data.status,
        })
        .eq("uid_user", params.supplierId);

      if (supplierError) throw supplierError;

      // Mise à jour dans la table users
      const { error: userError } = await supabase
        .from("users")
        .update({
          first_name: data.first_name,
          last_name: data.last_name,
          phone_number: data.phone_number,
        })
        .eq("uid", params.supplierId);

      if (userError) throw userError;

      Toast.show({ 
        type: "success", 
        text1: "Mise à jour réussie",
        text1Style: { color: "#1C3144" }
      });

      router.back();
    } catch (err) {
      console.error("Erreur lors de la mise à jour :", err);
      Toast.show({ 
        type: "error", 
        text1: "Erreur lors de la mise à jour",
        text1Style: { color: "#1C3144" }
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.mainContainer}>
      <ScrollView style={styles.container}>
        <View style={styles.containerContent}>
          <View style={styles.registerInfo}>
            <View style={styles.inputs}>
              {inputs.map((input, index) => {
                if (input?.type === "Select") {
                  return (
                    <View key={index}>
                      <CustomizedSelect
                        name={input.name}
                        label={input.label}
                        register={register}
                        control={control}
                        errors={errors}
                        setError={setError}
                        data={input.data as any}
                      />
                    </View>
                  );
                } else {
                  return (
                    <View key={index}>
                      <InputCustomized
                        label={input.label}
                        placeholder={input.placeholder}
                        register={register}
                        name={input.name}
                        control={control}
                        errors={errors}
                        setError={setError}
                      />
                    </View>
                  );
                }
              })}
            </View>
          </View>

          <View style={styles.buttons}>
            <ContainedButton
              label="Mettre à jour"
              backgroundColor="#F99527"
              onPress={handleSubmit(handleUpdateSupplier)}
            />
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  containerContent: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    width: "100%",
    gap: 20,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
    marginTop: 20,
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
});
