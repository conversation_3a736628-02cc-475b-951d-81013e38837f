/*
app/menu/itemsMenuCockpit/risk.tsx

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `Supplier` n'ont pas accès à cette page.
*/

import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  Platform,
} from "react-native";
import { useRouter, useFocusEffect } from "expo-router";
import { useCallback, useContext, useEffect, useState } from "react";
import { Spinner } from "@/components/ui/spinner";
import { supabase } from "@/lib/supabase";
import { AppContext } from "@/state/AppContext";
import PaperInfo from "@/components/common/paperInfo";
import { Octicons } from "@expo/vector-icons";
import TypeRisk from "@/types/typeRisk";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";

const possibilities = [
  { value: 0, label: "Quasi impossible", weight: 1 },
  { value: 1, label: "Possible", weight: 2 },
  { value: 2, label: "Fortes chances", weight: 3 },
  { value: 3, label: "Quasi certain !", weight: 4 },
];

const consequences = [
  { value: 0, label: "Impact critique", weight: 4 },
  { value: 1, label: "Impact important", weight: 3 },
  { value: 2, label: "Impact modéré", weight: 2 },
  { value: 3, label: "Impact mineur", weight: 1 },
];

export default function Risk() {
  const router = useRouter();
  const [data, setData] = useState<TypeRisk[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useContext(AppContext);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  const fetchData = async () => {
    setIsLoading(true);
    const { data: dataGetted, error } = await supabase
      .from("risks")
      .select("*, process(*), companies(*)")
      .eq("uid_user", user?.uid);

    if (!error) {
      setData(dataGetted as any);
    }

    setIsLoading(false);
  };

  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [])
  );

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["User", "Supplier"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  return (
    <View style={styles.mainContainer}>
      <ScrollView style={styles.container}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={{ fontWeight: "bold" }}>Créer un risque</Text>
            <TouchableOpacity onPress={() => router.navigate("/risk/createRisk")}>
              <Octicons name="diff-added" size={25} color="#F99527" />
            </TouchableOpacity>
          </View>

          <View style={styles.users}>
            {isLoading ? (
              <Spinner size="small" color="#1C3144" />
            ) : data?.length > 0 ? (
              data?.map((item, index) => {
                // Trouver le poids correspondant pour possibility et consequence
                const possibilityWeight =
                  possibilities.find((p) => p.value === item.possibility)
                    ?.weight || 1;
                const consequenceWeight =
                  consequences.find((c) => c.value === item.consequence)
                    ?.weight || 1;

                // Calcul du score
                const score = possibilityWeight * consequenceWeight;

                return (
                  <PaperInfo
                    key={index}
                    title={item.event}
                    text2={item.description}
                    level={item.level}
                    score={score} // Utilisation du score calculé
                    onPress={() => {
                      router.push({
                        pathname: "/risk/detailsRisk",
                        params: {
                          id: item.id,
                          event: item.event,
                          score: score,
                        },
                      });
                    }}
                  />
                );
              })
            ) : (
              <Text>Vous n'avez pas encore crée des risques</Text>
            )}
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  content: {
    gap: 10,
    marginBottom: 10,
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 14,
  },
  users: {
    paddingHorizontal: 10,
    gap: 10,
  },
});
