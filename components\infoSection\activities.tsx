/*
components/infoSection/activities.tsx

Card pour les activités.
*/

import { StyleSheet, View, Text, TouchableOpacity } from "react-native";
import { useRouter, usePathname, useFocusEffect } from "expo-router";
import PaperInfoImg from "../common/paperInfoImg";
import { Spinner } from "../ui/spinner";
import TypeActivity from "@/types/typeActivity";
import { useCallback, useContext, useEffect, useState } from "react";
import { supabase } from "@/lib/supabase";
import { AppContext } from "@/state/AppContext";

type typeActivitiesProps = {
  icon?: any;
  data: TypeActivity[];
  isLanding?: boolean;
};

export default function ActivitiesInfoSection({
  icon,
  data,
  isLanding,
}: typeActivitiesProps) {
  const router = useRouter();
  const path = usePathname();
  const [activities, setActivites] = useState<any>(null);
  const { user } = useContext(AppContext);

  const fetchData = async () => {
    const { data, error } = await supabase
      .from("activities")
      .select()
      .eq("uid_admin", user?.uid);

    if (!error) {
      setActivites(data || null);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [])
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={{ fontWeight: "bold" }}>Mes activités</Text>

        <TouchableOpacity
          onPress={() => router.navigate({ pathname: "/info/activities" })}
        >
          {icon ? (
            icon
          ) : (
            <Text
              style={{
                color: "#F99527",
                textDecorationLine: "underline",
                fontFamily: "Poppins",
                fontWeight: "bold",
              }}
            >
              Voir plus
            </Text>
          )}
        </TouchableOpacity>
      </View>

      <View style={styles.users}>
        {isLanding ? (
          <Spinner size="small" color="#1C3144" />
        ) : data?.length > 0 ? (
          (path === "/Informations" ||
          path === "/Accueil" ||
          path === "/menu/itemsMenuCockpit/info"
            ? data?.slice(-2)
            : data
          ).map((item, index) => {
            return (
              <PaperInfoImg
                key={index}
                title={item.activity_name}
                text2={item.description}
                onPress={() =>
                  router.navigate({
                    pathname: "/info/detailsActivity",
                    params: { id: item.id },
                  })
                }
              />
            );
          })
        ) : (
          <Text>Vous n'avez pas encore crée des activities</Text>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "column",
    width: "100%",
    gap: 10,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 10,
  },
  users: {
    paddingHorizontal: 10,
    gap: 10,
  },
  searchContainer: {
    width: "100%",
    marginBottom: 20,
    marginTop: 10,
    paddingHorizontal: 10,
  },
  searchInputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1C314414",
    borderRadius: 8,
    paddingHorizontal: 10,
    height: 60,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    padding: 10,
    fontSize: 16,
    color: "#000000",
  },
});
