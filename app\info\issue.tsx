/*
app/info/issue.tsx

Affichage du l'enjeu de l'entreprise.
*/

import {
  StyleSheet,
  ScrollView,
  View,
  TextInput,
  TouchableOpacity,
} from "react-native";
import { useCallback, useContext, useEffect, useState } from "react";
import { AppContext } from "@/state/AppContext";
import { FontAwesome, Octicons } from "@expo/vector-icons";
import { useFocusEffect, useRouter } from "expo-router";
import { supabase } from "@/lib/supabase";
import TypeContext from "@/types/typeContext";
import IssuesInfoSection from "@/components/infoSection/issue";
import AntDesign from '@expo/vector-icons/AntDesign';

export default function Issues() {
  const router = useRouter();
  const [issues, setContexts] = useState<TypeContext[] | null>(null);
  const { user } = useContext(AppContext);
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredDataList, setFilteredDataList] = useState<any>([]);

  // Récupérer le issuee
  useEffect(() => {
    const filteredList = issues?.filter((item) =>
      `${item.history}`.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredDataList(filteredList);
  }, [searchQuery, issues]);

  // Récupérer le issuee
  const fetchData = async () => {
    const { data, error } = await supabase
      .from("issues")
      .select()
      .eq("uid_admin", user?.uid);

    if (!error) {
      setContexts(data || null);
    }
  };

  // Récupérer le issuee
  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [])
  );

  return (
    <ScrollView>
      {/* Barre de recherche */}
      {/* <View style={styles.searchContainer}>
        <View style={styles.searchInputWrapper}>
          <FontAwesome
            name="search"
            size={24}
            color="#3C3c4399"
            style={styles.searchIcon}
          />
          <TextInput
            style={styles.searchInput}
            placeholder="Rechercher par nom..."
            placeholderTextColor="#3C3c4399"
            value={searchQuery}
            onChangeText={(text) => setSearchQuery(text)}
          />
        </View>
      </View> */}

      <IssuesInfoSection
        icon={
          !issues || issues.length === 0 ? ( // Vérifie s'il n'y a pas encore de issuee
            <Octicons
              name="diff-added"
              size={24}
              color="#F99527"
              onPress={() => router.navigate("/info/createIssue")}
            />
          ) : (
            <AntDesign
              name="sync"
              size={24}
              color="#F99527"
              onPress={() => router.navigate("/info/updateIssue")}
            />
          )
        }
        data={filteredDataList as any}
      />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  searchContainer: {
    width: "100%",
    marginBottom: 20,
    marginTop: 10,
    paddingHorizontal: 10,
  },
  searchInputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1C314414",
    borderRadius: 8,
    paddingHorizontal: 10,
    height: 60,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    padding: 10,
    fontSize: 16,
    color: "#000000",
  },
});
