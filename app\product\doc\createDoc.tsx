import React from 'react';
import ContainedButton from "@/components/ui/buttons/ContainedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useLocalSearchParams, useRouter } from "expo-router";
import {
  StyleSheet,
  Image,
  View,
  ScrollView,
  Alert,
  TouchableOpacity,
  Text,
} from "react-native";

import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import CustomizedSelect from "@/components/ui/customizedSelect/customizedSelect";
import TypeProcess from "@/types/typeProcess";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import InputFileCustomized from "@/components/ui/inputs/InputFileCustomized";
import * as DocumentPicker from "expo-document-picker";
import uploadPDF from "@/lib/uploadDocumentCoverStorage";

type TypeDoc = {
  name: string;
  file: string;
};

export default function CreateDoc() {
  const [loading, setLoading] = useState(false);
  const [fileUri, setFileUri] = useState<string | null>(null);
  const [fileName, setFileName] = useState<string | null>(null);
  const { user, companySelected } = useContext(AppContext);

  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  const { product_id } = useLocalSearchParams();

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors, touchedFields },
    control,
    reset,
  } = useForm<TypeDoc>({
    defaultValues: {
      name: "",
      file: "",
    },
  });

  const inputs = [
    {
      label: "Nom du document",
      name: "name",
      placeholder: "Nom du document",
    },
    {
      label: "Fichier PDF",
      name: "file",
      placeholder: "Choisir le fichier PDF",
      type: "file",
    },
  ];

  const pickFile = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: "application/pdf",
      });

      if (result.canceled) return;

      const selectedFile = result.assets?.[0];
      if (selectedFile && selectedFile.mimeType === "application/pdf") {
        setFileUri(selectedFile.uri);
        setFileName(selectedFile.name);
      } else {
        Toast.show({
          type: "error",
          text1: "Format non supporté",
          text1Style: { color: "#1C3144" },
          text2: "Veuillez sélectionner un fichier PDF",
        });
      }
    } catch (error) {
      console.error("Erreur lors de la sélection du fichier :", error);
      Toast.show({
        type: "error",
        text1: "Erreur",
        text1Style: { color: "#1C3144" },
        text2: "Impossible de sélectionner le fichier",
      });
    }
  };

  const createDoc = async (data: TypeDoc) => {
    if (!data.name || !fileUri) {
      Toast.show({
        type: "error",
        text1: "Veuillez remplir tous les champs requis",
        text1Style: { color: "#1C3144" },
      });
      return;
    }

    setLoading(true);

    const created_at = new Date();

    // Upload du fichier PDF
    let publicUrl = "";
    if (fileUri) {
      const uploadResult = await uploadPDF(fileUri, user?.uid || "");

      if (!uploadResult || !uploadResult.url) {
        Alert.alert("Erreur", "Impossible de télécharger le fichier.");
        setLoading(false);
        return;
      }

      publicUrl = uploadResult.url;
    }

    try {
      const { error } = await supabase.from("docsProduct").insert({
        uid_user: user?.uid,
        created_at: created_at,
        company_id: companySelected?.id,
        name: data.name,
        image: publicUrl,
        product_id: product_id,
        type: "doc",
      });

      if (error) {
        console.log("error__", error);
        Toast.show({
          type: "error",
          text1: "Erreur lors de l'enregistrement du document",
          text1Style: { color: "#1C3144" },
        });
        setLoading(false);
        return;
      }

      Toast.show({
        type: "success",
        text1: "Le document a été créé avec succès",
        text1Style: { color: "#1C3144" },
      });

      router.push({
        pathname: "/product/detailsProduct",
        params: { product_id: product_id },
      });

      reset();
      setLoading(false);
    } catch (err) {
      setFileUri(null);
      setFileName(null);
      setLoading(false);

      Toast.show({
        type: "error",
        text1: "Une erreur est survenue",
        text1Style: { color: "#1C3144" },
      });
    }
  };

  return (
    <ScrollView>
      <View style={styles.containerContent}>
        <View style={styles.registerInfo}>
          <View style={styles.inputs}>
            {inputs.map((input, index) => {
              if (input?.type === "file") {
                return (
                  <React.Fragment key={index}>
                    {!fileUri && (
                      <InputFileCustomized
                        label={"Fichier PDF"}
                        placeholder={"Choisir le fichier PDF"}
                        onPress={pickFile}
                      />
                    )}
                    {fileUri && (
                      <View style={{ display: "flex" }}>
                        <Text style={styles.label}>Fichier PDF sélectionné</Text>
                        <TouchableOpacity onPress={pickFile}>
                          <Text style={styles.fileName}>{fileName}</Text>
                        </TouchableOpacity>
                      </View>
                    )}
                  </React.Fragment>
                );
              } else {
                return (
                  <View key={index}>
                    <InputCustomized
                      label={input.label}
                      placeholder={input.placeholder}
                      register={register}
                      name={input.name}
                      control={control}
                      errors={errors}
                      setError={setError}
                    />
                  </View>
                );
              }
            })}
          </View>
        </View>

        <View style={styles.buttons}>
          <ContainedButton
            label="Créer"
            backgroundColor="#F99527"
            onPress={handleSubmit(createDoc)}
            disabled={loading}
          />
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  containerContent: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    // padding: 20,
    gap: 20,
    marginVertical: 20,
    width: "90%",
    alignSelf: "center",
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    // backgroundColor: "yellow",
    width: "100%",
    gap: 20,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  buttonPassword: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  titleContainer: {
    flexDirection: "row",
    gap: 8,
  },
  imgIsLoadingPage: {
    height: 75,
    width: 130,
    alignSelf: "center",
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
  input: {
    backgroundColor: "white",
    borderColor: "none",
    height: 40,
    padding: 10,
    borderRadius: 4,
  },
  label: {
    fontSize: 14,
    color: "#262627",
    marginVertical: 1,
    fontWeight: "bold",
  },
  fileName: {
    fontSize: 14,
    color: "#262627",
    marginVertical: 5,
    padding: 10,
    backgroundColor: "#f5f5f5",
    borderRadius: 4,
  },
});
