/*
app/screens/notifications/FlashQSEDetails.tsx

Page de détails d'un flash QSE.

Informations pertinentes :

- Les informations du Flash QSE sont récupérés depuis la navigation du composant `FlashQSEList.tsx`
*/

import {
  Image,
  Keyboard,
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
  ScrollView,
  Alert,
  Platform,
} from "react-native";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AntDesign } from "@expo/vector-icons";
import { useRoute, RouteProp } from "@react-navigation/native";
import FontAwesome from "@expo/vector-icons/FontAwesome";
import HeaderV2 from "@/components/common/HeaderV2.native";
import React, { useContext, useState, useEffect } from "react";
import Header from "@/components/common/Header";
import { supabase } from "../../../lib/supabase.native";
import { AppContext } from "@/state/AppContext";


type FlashQSEDetailsRouteParams = {
  flashQSE: {
    id: number;
    name: string;
    context: string;
    cover: string;
    causes: string;
    actions: string;
    uid_company: number;
  };
};

export default function FlashQSEDetails() {
  const route = useRoute<RouteProp<{ params: FlashQSEDetailsRouteParams }, "params">>();
  const [flashQSE, setFlashQSE] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const { user } = useContext(AppContext);

  // Récupérer les données depuis la base de données
  useEffect(() => {
    const fetchFlashQSE = async () => {
      try {
        setLoading(true);
        let flashQSEId;

        // Essayer de récupérer l'ID depuis les paramètres de navigation
        if (route.params?.flashQSE?.id) {
          flashQSEId = route.params.flashQSE.id;
        } else if (Platform.OS === 'web') {
          // Sur le web, essayer de récupérer l'ID depuis l'URL
          const urlParams = new URLSearchParams(window.location.search);
          flashQSEId = urlParams.get('id');
        }

        if (!flashQSEId) {
          throw new Error("ID du Flash QSE non trouvé");
        }

        const { data, error } = await supabase
          .from("flash_qse")
          .select("*")
          .eq("id", flashQSEId)
          .single();

        if (error) throw error;
        if (data) {
          setFlashQSE(data);
          // Mettre à jour l'URL avec l'ID si ce n'est pas déjà fait
          if (Platform.OS === 'web' && !window.location.search.includes('id=')) {
            window.history.replaceState({}, '', `?id=${flashQSEId}`);
          }
        } else {
          throw new Error("Flash QSE non trouvé");
        }
      } catch (error) {
        console.error("Erreur lors de la récupération du Flash QSE:", error);
        if (Platform.OS === "web") {
          window.alert("Erreur lors de la récupération des données");
        } else {
          Alert.alert("Erreur", "Erreur lors de la récupération des données");
        }
        navigation.goBack();
      } finally {
        setLoading(false);
      }
    };

    fetchFlashQSE();
  }, [route.params?.flashQSE?.id, Platform.OS === 'web' ? window.location.search : null]);

  // 🔥 Fonction pour supprimer un Flash QSE avec confirmation
  const handleDeleteFlash = async () => {
    const confirmDelete =
      Platform.OS === "web"
        ? window.confirm("Êtes-vous sûr de vouloir supprimer ce Flash QSE ?")
        : await new Promise((resolve) =>
            Alert.alert(
              "Confirmation",
              "Êtes-vous sûr de vouloir supprimer ce Flash QSE ?",
              [
                { text: "Annuler", style: "cancel", onPress: () => resolve(false) },
                { text: "Oui, supprimer", onPress: () => resolve(true) },
              ]
            )
          );

    if (!confirmDelete) return;

    try {
      const { error } = await supabase
        .from("flash_qse")
        .delete()
        .eq("id", flashQSE.id);

      if (error) throw error;

      alert("Le Flash QSE a été supprimé avec succès.");
      navigation.goBack();
    } catch (error) {
      console.error("❌ Erreur lors de la suppression :", error);
      alert("Une erreur est survenue lors de la suppression.");
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Chargement...</Text>
      </View>
    );
  }

  return (
    <>
      <Header
        onPressIcon={() =>
          navigation.navigate("screens/notifications/UpdateFlashQSE", {
            flashQSE,
          })
        }
      />
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <ScrollView 
          alwaysBounceVertical
          contentContainerStyle={{ flexGrow: 1 }}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.card}>
            {flashQSE ? (
              <>
                <Text style={styles.detailTitle}> Détails du Flash QSE : </Text>
                <Text style={styles.detailName}>{flashQSE.name}</Text>
                <Text style={styles.listDescription2}>{flashQSE.context}</Text>
                <View style={styles.imageContainer}>
                  <Image
                    source={{ uri: flashQSE.cover }}
                    style={styles.detailImage}
                    resizeMode="contain"
                  />
                </View>
                <Text style={styles.detailTitle}>Les causes possibles</Text>
                <Text style={styles.listDescription2}>{flashQSE.causes}</Text>
                <Text style={styles.detailTitle}>
                  Les actions engagées à ce jour
                </Text>
                <Text style={styles.listDescription2}>{flashQSE.actions}</Text>
                {/* 🔥 Bouton de suppression visible uniquement pour les Admins */}
                {user?.status === "Admin" && (
                  <View style={styles.deleteButtonContainer}>
                    <TouchableOpacity style={styles.deleteButton} onPress={handleDeleteFlash}>
                      <Text style={styles.deleteButtonText}>Supprimer ce Flash QSE</Text>
                    </TouchableOpacity>
                  </View>
                )}
              </>
            ) : (
              <Text>Les détails du Flash QSE ne sont pas disponibles.</Text>
            )}
          </View>
        </ScrollView>
      </TouchableWithoutFeedback>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 0,
    ...Platform.select({
      web: {
        maxWidth: 800,
        marginHorizontal: "auto",
      },
    }),
  },
  headSection: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  backButton: {
    width: 24,
    textAlign: "left",
  },
  titleWrapper: {
    display: "flex",
    flexDirection: "row",
    marginVertical: 15,
  },
  title: {
    color: "#262627",
    fontSize: 17,
    textAlign: "left",
  },
  card: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "flex-start",
    alignItems: "flex-start",
    alignContent: "flex-start",
    padding: 5,
    borderRadius: 8,
    marginBottom: 20,
    shadowColor: "#CCC",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.9,
    shadowRadius: 2,
    elevation: 1,
    rowGap: 5,
    paddingLeft: 30,
    paddingTop: 10,
    paddingHorizontal: 30,
    paddingBottom: 20,
    marginHorizontal: 5,
    ...Platform.select({
      web: {
        maxWidth: 800,
        marginHorizontal: "auto",
      },
    }),
  },
  insideCard: {
    width: "100%",
    height: "auto",
    display: "flex",
    flexDirection: "column",
    rowGap: 5,
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  imageContainer: {
    width: "100%",
    backgroundColor: "#f5f5f5",
    borderRadius: 10,
    overflow: "hidden",
    marginVertical: 20,
    borderWidth: 1,
    borderColor: "#E5E7EB",
    padding: 10,
  },
  detailImage: {
    width: "100%",
    height: 300,
    resizeMode: "contain",
  },
  detailTitle: {
    fontSize: 17,
    fontWeight: "bold",
    color: "#1C3144",
    marginBottom: 10,
  },
  detailName: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#1C3144",
    marginBottom: 10,
  },
  detailDescription: {
    fontSize: 16,
    color: "#525252",
    marginBottom: 5,
  },
  listDescription2: {
    // width: "70%",
    fontSize: 14,
    color: "#1c3144",
    fontStyle: "normal",
    flexWrap: "wrap",
  },
  deleteButtonContainer: {
    width: "100%",
    display: "flex",
    justifyContent: "center",
    textAlign: "center",
    alignItems: "center",
    // position: "absolute",
    marginTop: 30,
  },
  deleteButton: {
    width: 200,
    backgroundColor: "#ff4f42",
    padding: 10,
    borderRadius: 8,
    alignItems: "center",
  },
  deleteButtonText: {
    color: "#FFFFFF",
    fontWeight: "normal",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});


