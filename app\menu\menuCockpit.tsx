import AntDesign from "@expo/vector-icons/AntDesign";
import EvilIcons from "@expo/vector-icons/EvilIcons";
import Ionicons from "@expo/vector-icons/Ionicons";
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";
import { Feather } from "@expo/vector-icons";

import React, { useEffect, useContext } from "react";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import Info from "./itemsMenuCockpit/info";
import Objectif from "./itemsMenuCockpit/objectif";
import Risk from "./itemsMenuCockpit/risk";
import Carto from "./itemsMenuCockpit/carto";
import PIP from "./itemsMenuCockpit/pip";
import { View } from "react-native";
import Header from "@/components/common/Header";
import { router } from "expo-router";
import Toast from "react-native-toast-message";
import { AppContext } from "@/state/AppContext";

const Tab = createBottomTabNavigator();

const tabNames = {
  Accueil: "Carto",
  Informations: "Vision",
};

export default function MenuCockpit() {
  const { setInCockpit } = useContext(AppContext);

  useEffect(() => {
    // Marquer que l'utilisateur est dans le cockpit
    setInCockpit(true);

    // Nettoyer lors du démontage
    return () => {
      setInCockpit(false);
    };
  }, []);

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ color }) => {
          if (route.name === "Accueil") {
            return <Ionicons name="settings-outline" size={24} color={color} />;
          } else if (route.name === "Informations") {
            return (
              <Ionicons name="newspaper-outline" size={24} color={color} />
            );
          } else if (route.name === "Objectifs") {
            return <Feather name="target" size={24} color={color} />;
          } else if (route.name === "Risques") {
            return (
              <MaterialCommunityIcons
                name="alarm-light-outline"
                size={24}
                color={color}
              />
            );
          } else if (route.name === "PIP") {
            return <Ionicons name="body-outline" size={24} color={color} />;
          }
        },
        tabBarActiveTintColor: "#F99527",
        tabBarInactiveTintColor: "#FFFFFF",
        headerShown: false,
        tabBarStyle: {
          backgroundColor: "#1C3144",
          borderTopWidth: 0,
          elevation: 0,
        },
      })}
    >
      <Tab.Screen
        name="Accueil"
        component={Carto}
        options={{ title: tabNames["Accueil"] || "Accueil" }}
      />
      <Tab.Screen
        name="Informations"
        component={Info}
        options={{
          title: tabNames["Informations"] || "Informations",
          headerShown: true,
          header: () => (
            <View>
              <Header />
              <Toast />
            </View>
          ),
        }}
      />
      <Tab.Screen
        name="PIP"
        component={PIP}
        options={{
          headerShown: true,
          header: () => (
            <View>
              <Header />
              <Toast />
            </View>
          ),
        }}
      />
      <Tab.Screen name="Objectifs" component={Objectif} />
      <Tab.Screen
        name="Risques"
        component={Risk}
        options={{
          headerShown: true,
          header: () => (
            <View>
              <Header />
              <Toast />
            </View>
          ),
        }}
      />
    </Tab.Navigator>
  );
}
