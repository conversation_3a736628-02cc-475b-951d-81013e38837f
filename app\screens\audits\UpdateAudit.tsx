/*
app/screens/audits/UpdateAudit.tsx

Formulaire pour modifier un audit existant.

Informations pertinentes :

- Récupération des informations de l'audit via la navigation depuis `AuditDetails.tsx`.
- Quand le formulaire est validé les données sont envoyées dans :
  -> la table `audits` (modifie l'audit existant)
  -> la table de jointure `audit_observations` (modifie les relations existantes)

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern` et `Extern` n'ont pas accès à cette page.
*/

import React, { useContext, useEffect, useState } from "react";
import {
  Alert,
  StyleSheet,
  View,
  TextInput,
  Text,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { supabase } from "@/lib/supabase";
import { Controller, useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { AntDesign } from "@expo/vector-icons";
import {
  useRoute,
  RouteProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native";
import { Dropdown } from "react-native-element-dropdown";
import { useRouter } from "expo-router";
import DateTimePicker from "@react-native-community/datetimepicker";
import { DateTimePickerAndroid } from "@react-native-community/datetimepicker";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import CustomizedSelect from "@/components/ui/customizedSelect/customizedSelect";
import ContainedButton from "@/components/ui/buttons/ContainedButton";
import { WebDateTimePicker } from "@/components/ui/datepicker/WebDateTimePicker";

// Données de navigation
type AuditDetailsRouteParams = {
  audit: {
    id: number;
    name: string;
    type: string;
    field: string;
    date: Date;
    observation?: {
      id: number;
      conclusion: string;
    };
    companyId: number;
  };
};

// Choix du dropdown (audits)
const auditTypes = [
  { label: "Finalisé", value: "validated" },
  { label: "En cours", value: "in_progress" },
  { label: "En relecture", value: "invalid" },
];

export default function UpdateAudit() {
  const router = useRouter();
  const [selectedDate, setSelectedDate] = useState(new Date());
  const { user } = useContext(AppContext);
  const route =
    useRoute<RouteProp<{ params: AuditDetailsRouteParams }, "params">>();
  const { audit } = route.params;
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const {
    handleSubmit,
    formState: { errors },
    control,
    reset,
    register,
    setError,
  } = useForm({
    defaultValues: {
      name: audit.name || "",
      date: audit.date || "",
      field: audit?.field || "",
      type: audit?.type || "",
    },
    mode: "onBlur",
    criteriaMode: "all",
  });

  const [loading, setLoading] = useState(false);

  // Récupérer les informations de l'utilisateur connecté
  useEffect(() => {
    const fetchData = async () => {
      try {
        const { data: companySelected, error: companyError } = await supabase
          .from("companies")
          .select("id")
          .eq("uid_user", user?.uid)
          .single();

        if (companyError) {
          console.error(
            "Erreur lors de la récupération de l'entreprise :",
            companyError.message
          );
          return;
        }

        setSelectedDate(new Date(audit.date));
      } catch (err) {
        console.error("Erreur inattendue lors de la récupération :", err);
      }
    };

    fetchData();
  }, [user]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  // Date (iOS)
  const handleDateChange = (event: any, selectedDate?: Date) => {
    if (selectedDate) {
      setSelectedDate(selectedDate);
    }
  };

  // Date (Android)
  const showDatePicker = () => {
    DateTimePickerAndroid.open({
      value: selectedDate,
      mode: "date",
      onChange: handleDateChange,
      display: "default",
    });
  };

  // Valider le formulaire
  const handleSubmitUpdateAudit = async (data: any) => {
    try {
      // Construire les données mises à jour en excluant les valeurs vides
      const updatedData: Partial<typeof audit> = {
        name: data.name || audit.name,
        date: selectedDate || audit.date,
        field: data.field || audit.field,
        type: data.type || audit.type,
      };

      // Effectuer la mise à jour sur Supabase
      const { data: updatedAction, error } = await supabase
        .from("audits")
        .update(updatedData)
        .eq("id", audit.id)
        .select();

      const auditId = updatedAction?.[0]?.id;
      if (!auditId) {
        Alert.alert("Erreur", "La mise à jour de l'audit a échoué.");
        return;
      }

      Alert.alert("Action modifiée !", `${data.name} modifiée avec succès.`);

      setTimeout(() => {
        reset();
        router.navigate("/menu/itemsMenuDefault/audit");
      }, 1000);
    } catch (err) {
      Alert.alert(
        "Erreur",
        `${audit.name} : Une erreur est survenue lors de la modification.`
      );
    }
  };

  return (
    <View style={styles.mainContainer}>
      <View style={styles.container}>
        <View style={styles.content}>
          <View style={styles.formContainer}>
            <View style={styles.inputs}>
              <InputCustomized
                label="Nom de l'audit"
                placeholder="Nom de l'audit"
                register={register}
                name="name"
                control={control}
                errors={errors}
                setError={setError}
              />

              <View style={styles.dateTimePickerContainer}>
                <Text style={styles.label}>Date</Text>
                {Platform.OS === "web" ? (
                  <WebDateTimePicker
                    value={selectedDate}
                    onChange={handleDateChange}
                    style={styles.datePicker}
                  />
                ) : (
                  <DateTimePicker
                    value={selectedDate}
                    mode="date"
                    display="default"
                    onChange={handleDateChange}
                    style={styles.datePicker}
                  />
                )}
              </View>

              <InputCustomized
                label="Champ"
                placeholder="Champ de l'audit"
                register={register}
                name="field"
                control={control}
                errors={errors}
                setError={setError}
              />

              <CustomizedSelect
                name="type"
                label="Type"
                register={register}
                control={control}
                errors={errors}
                setError={setError}
                data={auditTypes}
              />
            </View>

            <View style={styles.buttons}>
              <ContainedButton
                label="Modifier"
                backgroundColor="#F99527"
                onPress={handleSubmit(handleSubmitUpdateAudit)}
                disabled={loading}
              />
            </View>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  content: {
    gap: 20,
    padding: 20,
  },
  formContainer: {
    width: "100%",
    gap: 20,
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
  dateTimePickerContainer: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    textAlign: "left",
    gap: 10,
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
  },
  datePicker: {
    width: "100%",
    marginTop: 10,
    marginBottom: 10,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
});
