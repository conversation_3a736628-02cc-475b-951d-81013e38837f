import ContainedButton from "@/components/ui/buttons/ContainedButton";
import OutlinedButton from "@/components/ui/buttons/OutlinedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useRouter } from "expo-router";
import {
  StyleSheet,
  Image,
  View,
  Text,
  Linking,
  Alert,
  TextInput,
  ScrollView,
  Platform,
} from "react-native";

import * as FileSystem from "expo-file-system";

import { useContext, useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { MaterialIcons } from "@expo/vector-icons";
import InputFileCustomized from "@/components/ui/inputs/InputFileCustomized";
import { AppContext } from "@/state/AppContext";
import TypeUser from "@/types/user";
import * as ImagePicker from "expo-image-picker";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import uploadPhoto from "@/lib/uploadPictureStorage";
import { AuthChangeEvent, Session } from '@supabase/supabase-js';
import { KeyboardAvoidingView } from "react-native";


// Type étendu pour le formulaire
type TypeUserForm = TypeUser & {
  company_name?: string;
};

const inputs = [
  { label: "Nom", name: "last_name", placeholder: "Ecrire ton nom" },
  { label: "Prénom", name: "first_name", placeholder: "Ecrire ton prénom" },
  {
    label: "Email",
    name: "email",
    placeholder: "Ecrire ton email",
    isDisabled: true,
  },
];

export default function Profil() {
  const [loading, setLoading] = useState(false);

  const [imageUri, setImageUri] = useState<string | null>(null);

  const { user, setUser, companySelected, setCompanySelected } = useContext(AppContext);
  const {
    register,
    handleSubmit,
    setError,
    formState: { errors, touchedFields },
    control,
    getValues,
    watch,
    setValue,
  } = useForm<TypeUserForm>({
    defaultValues: {
      first_name: user?.first_name || "",
      last_name: user?.last_name || "",
      email: user?.email || "",
      company_name: companySelected?.company_name || "",
    },
  });

  // Initialiser la valeur de company_name quand companySelected change
  useEffect(() => {
    if (companySelected?.company_name) {
      setValue('company_name', companySelected.company_name, { shouldValidate: true });
    }
  }, [companySelected]);

  useEffect(() => {
    // Écouter les changements d'état d'authentification
    const { data: authListener } = supabase.auth.onAuthStateChange(async (event: AuthChangeEvent, session: Session | null) => {
      if (event === "PASSWORD_RECOVERY") {
        // L'utilisateur a cliqué sur le lien de réinitialisation
        Alert.prompt(
          "Nouveau mot de passe",
          "Veuillez entrer votre nouveau mot de passe",
          [
            {
              text: "Annuler",
              style: "cancel",
            },
            {
              text: "Valider",
              onPress: async (password) => {
                if (password) {
                  try {
                    const { error } = await supabase.auth.updateUser({
                      password: password
                    });

                    if (error) throw error;

                    Toast.show({
                      type: "success",
                      text1: "Mot de passe mis à jour",
                      text1Style: { color: "#1C3144" },
                    });
                  } catch (error) {
                    console.error("Erreur lors de la mise à jour du mot de passe:", error);
                    Toast.show({
                      type: "error",
                      text1: "Erreur lors de la mise à jour du mot de passe",
                      text1Style: { color: "#1C3144" },
                    });
                  }
                }
              },
            },
          ],
          "secure-text"
        );
      }
    });

    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, []);

  const display = () => {
    Toast.show({
      type: "success",
      text1: "Enregistré",
      text1Style: { color: "#1C3144" },
      // text2: "This is some something 👋",
    });
  };

  const updateProfil = async (data: TypeUserForm, newImageUri?: string) => {
    if (!data.first_name || !data.last_name) {
      Toast.show({
        type: "error",
        text1: "Le nom et le prénom sont obligatoires",
        text1Style: { color: "#1C3144" },
      });
      return;
    }
  
    setLoading(true);
    try {
      let newImageUrl = user?.profil_picture;
  
      if (newImageUri) {
        try {
          const response = await uploadPhoto(
            newImageUri,
            user?.uid,
            "images",
            "profil_pictures",
            "image/jpeg"
          );
          
          if (!response?.url) {
            throw new Error("Échec de l'upload de l'image");
          }
          
          newImageUrl = response.url;
        } catch (uploadError) {
          console.error("Erreur upload image:", uploadError);
          Toast.show({
            type: "error",
            text1: "Erreur lors de l'upload de l'image",
            text1Style: { color: "#1C3144" },
          });
          setLoading(false);
          return;
        }
      }
  
      // Mise à jour de l'utilisateur dans Supabase
      const { error } = await supabase
        .from("users")
        .update({
          first_name: data.first_name,
          last_name: data.last_name,
          profil_picture: newImageUrl,
        })
        .eq("uid", user?.uid);
  
      if (error) throw error;

      // Si l'utilisateur est admin et a modifié le nom de l'entreprise
      if (user?.status === "Admin" && data.company_name && data.company_name !== companySelected?.company_name) {
        const { error: companyError } = await supabase
          .from("companies")
          .update({
            company_name: data.company_name,
          })
          .eq("id", companySelected?.id);

        if (companyError) throw companyError;

        // Mettre à jour le contexte avec le nouveau nom d'entreprise
        setCompanySelected({
          ...companySelected!,
          company_name: data.company_name,
        });
      }
  
      // Récupérer les nouvelles données utilisateur
      const { data: updatedUser, error: fetchError } = await supabase
        .from("users")
        .select("*, companies(*)")
        .eq("uid", user?.uid)
        .single();
  
      if (fetchError) throw fetchError;
  
      setUser({
        ...updatedUser,
        companies: updatedUser.companies || [],
      });      
      
      setImageUri(null);
  
      Toast.show({
        type: "success",
        text1: "Votre profil a bien été mis à jour",
        text1Style: { color: "#1C3144" },
      });
    } catch (err) {
      console.error("Erreur mise à jour profil:", err);
      Toast.show({
        type: "error",
        text1: "Une erreur est survenue, veuillez réessayer",
        text1Style: { color: "#1C3144" },
      });
    } finally {
      setLoading(false);
    }
  };

  const pickImage = async () => {
    const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (!permissionResult.granted) {
      Toast.show({
        type: "error",
        text1: "Permissions refusées",
        text1Style: { color: "#1C3144" },
        text2: "Vous devez autoriser l'accès à vos photos.",
      });
      return;
    }
  
    const pickerResult = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [500, 500],
      quality: 1,
    });
  
    if (!pickerResult.canceled && pickerResult.assets.length > 0) {
      const selectedImageUri = pickerResult.assets[0].uri;
      setImageUri(selectedImageUri);
      
      // Récupérer les valeurs actuelles du formulaire
      const formData = getValues();
      
      // Appeler updateProfil avec les données actuelles et la nouvelle image
      await updateProfil(formData, selectedImageUri);
    }
  };

  const handleResetPassword = async () => {
    console.log("Email de l'utilisateur:", user?.email);
    console.log("UID de l'utilisateur:", user?.uid);
    console.log("Session actuelle:", await supabase.auth.getSession());
    
    if (!user?.email) {
      Toast.show({
        type: "error",
        text1: "Email non disponible",
        text1Style: { color: "#1C3144" },
      });
      return;
    }

    setLoading(true);
    try {
      // Demander d'abord le mot de passe actuel
      Alert.prompt(
        "Vérification",
        "Veuillez entrer votre mot de passe actuel",
        [
          {
            text: "Annuler",
            style: "cancel",
            onPress: () => setLoading(false)
          },
          {
            text: "Valider",
            onPress: async (currentPassword) => {
              if (currentPassword) {
                try {
                  // Vérifier le mot de passe actuel
                  const { error: signInError } = await supabase.auth.signInWithPassword({
                    email: user.email!,
                    password: currentPassword
                  });

                  if (signInError) {
                    Toast.show({
                      type: "error",
                      text1: "Mot de passe incorrect",
                      text1Style: { color: "#1C3144" },
                      text2: "Veuillez réessayer",
                    });
                    setLoading(false);
                    return;
                  }

                  // Si le mot de passe est correct, demander le nouveau mot de passe
                  Alert.prompt(
                    "Nouveau mot de passe",
                    "Veuillez entrer votre nouveau mot de passe",
                    [
                      {
                        text: "Annuler",
                        style: "cancel",
                        onPress: () => setLoading(false)
                      },
                      {
                        text: "Valider",
                        onPress: async (newPassword) => {
                          if (newPassword) {
                            try {
                              const { error } = await supabase.auth.updateUser({
                                password: newPassword
                              });

                              if (error) throw error;

                              Toast.show({
                                type: "success",
                                text1: "Mot de passe mis à jour",
                                text1Style: { color: "#1C3144" },
                              });
                            } catch (error: any) {
                              console.error("Erreur lors de la mise à jour du mot de passe:", error);
                              Toast.show({
                                type: "error",
                                text1: "Erreur lors de la mise à jour du mot de passe",
                                text1Style: { color: "#1C3144" },
                                text2: error?.message || "Veuillez réessayer plus tard",
                              });
                            } finally {
                              setLoading(false);
                            }
                          }
                        },
                      },
                    ],
                    "secure-text"
                  );
                } catch (error: any) {
                  console.error("Erreur de vérification:", error);
                  Toast.show({
                    type: "error",
                    text1: "Erreur",
                    text1Style: { color: "#1C3144" },
                    text2: error?.message || "Veuillez réessayer plus tard",
                  });
                  setLoading(false);
                }
              }
            },
          },
        ],
        "secure-text"
      );
    } catch (error: any) {
      console.error("Erreur:", error);
      Toast.show({
        type: "error",
        text1: "Erreur",
        text1Style: { color: "#1C3144" },
        text2: error?.message || "Veuillez réessayer plus tard",
      });
      setLoading(false);
    }
  };

  const router = useRouter();

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 0}
    >
      <ScrollView
        contentContainerStyle={{ paddingBottom: 80 }}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.containerContent}>
          {imageUri ? (
            <Image
              source={{ uri: imageUri }}
              style={styles.profileImage}
            />
          ) : user?.profil_picture ? (
            <Image
              source={{ uri: user?.profil_picture }}
              style={styles.profileImage}
            />
          ) : (
            <MaterialIcons name="account-circle" size={200} color="black" />
          )}

          <View style={styles.registerInfo}>
            <View style={styles.inputs}>
              <Text style={styles.infoText}>
                <Text style={styles.infoLabel}>Entreprise : </Text>
                {companySelected?.company_name}
              </Text>

              <Text style={styles.infoText}>
                <Text style={styles.infoLabel}>Profil : </Text>
                {user?.profil}
              </Text>

              {user?.status === "Admin" && (
                <View>
                  <InputCustomized
                    label="Nom de l'entreprise"
                    placeholder="Modifier le nom de l'entreprise"
                    register={register}
                    name="company_name"
                    control={control}
                    errors={errors}
                    setError={setError}
                    defaultValue={companySelected?.company_name}
                  />
                </View>
              )}
              {inputs.map((input, index) => {
                return (
                  <View key={index}>
                    <InputCustomized
                      label={input.label}
                      placeholder={input.placeholder}
                      register={register}
                      name={input.name}
                      control={control}
                      errors={errors}
                      setError={setError}
                      isDisabled={input.isDisabled}
                    />
                  </View>
                );
              })}
            </View>
          </View>

          <InputFileCustomized
            label={"Photo de profil"}
            placeholder={"Choisir la photo"}
            onPress={pickImage}
          />

          <View style={styles.buttonPassword}>
            <ContainedButton
              label="Changer le mot de passe"
              backgroundColor="#F99527"
              onPress={handleResetPassword}
              disabled={loading}
            />
          </View>

          <View style={styles.buttons}>
            <ContainedButton
              label="Modifier mon profil"
              backgroundColor="#F99527"
              onPress={() => {
                const formData = getValues();
                updateProfil(formData);
              }}
              disabled={loading}
            />
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  containerContent: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    marginVertical: 20,
    width: "100%",
    paddingHorizontal: 20,
    ...Platform.select({
      web: {
        maxWidth: 800,
        alignSelf: "center",
        paddingHorizontal: 0,
      },
    }),
  },
  profileImage: {
    width: 150,
    height: 150,
    marginVertical: 20,
    borderRadius: 150,
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    width: "100%",
    gap: 20,
  },
  infoText: {
    fontSize: 14,
    color: "#525252",
  },
  infoLabel: {
    fontWeight: "bold",
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  buttonPassword: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
});
