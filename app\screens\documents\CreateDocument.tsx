/*
app/screens/documents/CreateDocument.tsx

Formulaire pour créer un document.

Informations pertinentes :
- Un document est relié à l'entreprise de l'utilisateur connecté qui le crée.


+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern`, `Supplier` et `Extern` n'ont pas accès à cette page.
*/

import React, { useContext, useEffect, useState } from "react";
import {
  Alert,
  StyleSheet,
  View,
  TextInput,
  Image,
  Text,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import { supabase } from "../../../lib/supabase.native";
import { Controller, useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { AntDesign } from "@expo/vector-icons";
import Octicons from "@expo/vector-icons/Octicons";
import * as DocumentPicker from "expo-document-picker";
import uploadDocumentfileStorage from "@/lib/uploadDocumentCoverStorage";
import { Dropdown } from "react-native-element-dropdown";
import { useRouter } from "expo-router";
import uploadPDF from "@/lib/uploadDocumentCoverStorage";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import Toast from "react-native-toast-message";

export default function CreateDocument() {
  const router = useRouter();
  const [companyId, setCompanyId] = useState("");
  const [file, setFile] = useState<string | null>(null);
  const [activities, setActivities] = useState([]);
  const [loadingActivities, setLoadingActivities] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [selectedActivityName, setSelectedActivityName] = useState<
    string | null
  >(null);
    const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const { user } = useContext(AppContext); // Récupérer les informations utilisateur
  const {
    handleSubmit,
    formState: { errors, touchedFields },
    control,
    reset,
  } = useForm({
    defaultValues: {
      name: "",
      description: "",
      file: "",
      activity: "",
      company_id: "",
    },
  });

  // Récupérer les informations de l'utilisateur connecté
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Étape 1 : Récupérer l'entreprise de l'utilisateur connecté
        if (!user?.uid) {
          console.log(
            "Aucun utilisateur connecté. Annulation de la récupération."
          );
          return;
        }

        const { data: companyData, error: companyError } = await supabase
          .from("companies")
          .select("id")
          .eq("uid_user", user.uid)
          .single();

        if (companyError) {
          console.error(
            "Erreur lors de la récupération de l'entreprise :",
            companyError.message
          );
          return;
        }

        const companyId = companyData?.id;

        if (!companyId) {
          console.log("Aucune entreprise trouvée pour cet utilisateur.");
          return;
        }

        setCompanyId(companyId);

        // Étape 2 : Récupérer les activités de l'entreprise
        const { data: activityData, error: activityError } = await supabase
          .from("activities")
          .select("id, activity_name")
          .eq("company_id", companyId);

        if (activityError) {
          console.error(
            "Erreur lors de la récupération des activités :",
            activityError.message
          );
          return;
        }

        const formattedActivities: any = activityData.map((activity) => ({
          label: activity.activity_name,
          value: activity.id.toString(),
        }));

        setActivities(formattedActivities);
      } catch (err) {
        console.error("Erreur inattendue lors de la récupération :", err);
      } finally {
        console.log("Fin du processus de récupération.");
        setLoadingActivities(false);
      }
    };

    fetchData();
  }, [user]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern", "Supplier"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  // Enregistrer le fichier
  const handleFilePicker = async () => {
    try {  
      const result = await DocumentPicker.getDocumentAsync({
        type: "application/pdf",
      });
  
      if (result.canceled) return;
  
      const selectedFile = result.assets?.[0];
      if (selectedFile && selectedFile.mimeType === "application/pdf") {
        setFile(selectedFile.uri);
      } else {
        alert("Seuls les fichiers PDF sont autorisés.");
      }
    } catch (error) {
      console.error("Erreur lors de la sélection du fichier :", error);
      alert("Impossible de sélectionner le fichier.");
    }
  };

  // Valider le formulaire
  const handleSubmitCreateDocument = async (data: any) => {
    try {
      if (!companyId) {
        Alert.alert("Erreur", "Aucune entreprise associée trouvée.");
        return;
      }
      setIsSubmitting(true);

      // Vérifier et uploader le fichier PDF sur Supabase Storage
      let publicUrl = "";
      if (file) {
        const uploadResult = await uploadPDF(file, user?.uid || "");

        if (!uploadResult || !uploadResult.url) {
          Alert.alert("Erreur", "Impossible de télécharger le fichier.");
          return;
        }

        publicUrl = uploadResult.url;
      }

      const documentData = {
        ...data,
        file: publicUrl,
        activity: data.activity,
        company_id: companyId,
        created_at: new Date(),
      };

      console.log('Données du document à créer:', documentData);

      const { error } = await supabase.from("documents").insert(documentData);

      if (error) {
        Toast.show({
          type: "error",
          text1: "Échec de la création",
          text2: error.message || "Une erreur est survenue."
        });
        return;
      }

      Toast.show({
        type: "success",
        text1: "Succès",
        text2: "Le document a été créé avec succès",
        visibilityTime: 2000,
        onHide: () => {
          reset();
          router.back();
          // router.back();
        }
      });

    } catch (err) {
      Toast.show({
        type: "error",
        text1: "Erreur inattendue",
        text2: "Veuillez réessayer plus tard."
      });
      console.error("Erreur inattendue :", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      keyboardVerticalOffset={Platform.select({ ios: 0, android: 100 })}
    >
      <ScrollView style={styles.form}>
        {/* Champ Nom */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Nom</Text>
          <Controller
            control={control}
            name="name"
            rules={{ required: "Le nom est requis." }}
            render={({ field: { onChange, value } }) => (
              <TextInput
                maxLength={20}
                placeholderTextColor={"#6B7280"}
                style={styles.input}
                placeholder="Nom du document"
                onChangeText={onChange}
                value={value}
              />
            )}
          />
          {errors.name && (
            <Text style={styles.errorText}>{errors.name.message}</Text>
          )}
        </View>

        {/* Champ Description */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Description</Text>
          <Controller
            control={control}
            name="description"
            rules={{ required: "Le descriptione est requis." }}
            render={({ field: { onChange, value } }) => (
              <TextInput
                maxLength={200}
                placeholderTextColor={"#6B7280"}
                style={styles.input}
                placeholder="Description"
                onChangeText={onChange}
                value={value}
                multiline
              />
            )}
          />
          {errors.description && (
            <Text style={styles.errorText}>{errors.description.message}</Text>
          )}
        </View>

        {/* Champ Fichier */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Fichier</Text>
          <TouchableOpacity
            style={styles.imagePicker}
            onPress={handleFilePicker}
          >
            {!file && (
              <View
                style={{
                  width: "100%",
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Octicons
                  style={{
                    borderWidth: 1,
                    borderColor: "#f99527",
                    borderRadius: 7,
                    padding: 5,
                    textAlign: "center",
                  }}
                  name="upload"
                  size={24}
                  color="black"
                />
                <Text style={{ textAlign: "center", fontWeight: "normal" }}>
                  Choisir le fichier
                </Text>
              </View>
            )}
          </TouchableOpacity>
          {file && <Image source={{ uri: file }} style={styles.imagePreview} />}
        </View>

        {/* Champ Activité */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Activité</Text>
          <Controller
            control={control}
            name="activity"
            rules={{ required: "Les activités sont requises." }}
            render={({ field: { onChange, value } }) => (
              <Dropdown
                style={dropdownStyles.dropdown}
                placeholderStyle={dropdownStyles.placeholderStyle}
                selectedTextStyle={dropdownStyles.selectedTextStyle}
                inputSearchStyle={dropdownStyles.inputSearchStyle}
                iconStyle={dropdownStyles.iconStyle}
                data={activities}
                search
                maxHeight={300}
                labelField="label"
                valueField="value"
                placeholder={
                  loadingActivities
                    ? "Chargement des activités..."
                    : "Sélectionner une activité"
                }
                searchPlaceholder="Rechercher une activité"
                value={value}
                onChange={(item) => {
                  onChange(item.value); // Met à jour la valeur dans le formulaire
                  setSelectedActivityName(item.label); // Met à jour le nom de l'activité sélectionnée
                }}
                // onChange={(item) => onChange(item.value)}
                renderLeftIcon={() => (
                  <AntDesign
                    style={dropdownStyles.icon}
                    color="black"
                    name="Safety"
                    size={20}
                  />
                )}
              />
            )}
          />
        </View>

        {/* Bouton de soumission */}
        <TouchableOpacity
  style={[styles.confirmButton, isSubmitting && styles.disabledButton]}
  onPress={() => {
    if (!isSubmitting) {
      handleSubmit(handleSubmitCreateDocument)();
    }
  }}
  disabled={isSubmitting} // ✅ Désactive le bouton pendant le chargement
>
  {isSubmitting ? (
    <ActivityIndicator size="small" color="#FFFFFF" /> // ✅ Ajout du loader
  ) : (
    <Text style={styles.confirmButtonText}>Créer</Text>
  )}
</TouchableOpacity>

      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    height: "100%",
    width: "100%",
    alignItems: "center",
    alignContent: "center",
  },
  disabledButton: {
    backgroundColor: "#ccc", // ✅ Change la couleur pour montrer que c'est désactivé
  },
  
  headSection: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 20,
    marginBottom: 30,
  },
  backButton: {
    width: 24,
    textAlign: "left",
  },
  titleWrapper: {
    display: "flex",
    flexDirection: "row",
    marginVertical: 15,
  },
  title: {
    color: "#262627",
    fontSize: 17,
    textAlign: "left",
  },
  form: {
    width: "90%",
    alignSelf: "center",
    marginTop: 20,
  },
  input: {
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "auto",
    padding: 12,
    marginTop: 10,
    color: "#000000",
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginBottom: 5,
  },
  infoText: {
    fontSize: 14,
    marginVertical: 10,
    color: "#555",
  },
  inputContainer: {
    position: "relative",
    marginBottom: 20,
    width: "auto",
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
  },
  confirmButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    marginBottom: 16,
    backgroundColor: "#f99527",
    marginTop: 10,
  },
  confirmButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  imagePicker: {
    padding: 10,
    backgroundColor: "transparent",
    borderColor: "#E5E7EB",
    borderRadius: 8,
    borderWidth: 1,
    alignItems: "center",
    marginBottom: 10,
  },
  imagePickerText: {
    color: "#1F2937",
    fontWeight: "bold",
  },
  imagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  image: {
    width: 150,
    height: 150,
    borderRadius: 75,
    marginVertical: 20,
  },
});

// Style du dropdown
const dropdownStyles = StyleSheet.create({
  dropdown: {
    marginVertical: 16,
    height: 50,
    backgroundColor: "white",
    borderRadius: 12,
    padding: 12,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,

    elevation: 2,
  },
  icon: {
    marginRight: 5,
  },
  item: {
    padding: 17,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  textItem: {
    flex: 1,
    fontSize: 16,
  },
  placeholderStyle: {
    fontSize: 16,
  },
  selectedTextStyle: {
    fontSize: 16,
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 16,
  },
});
