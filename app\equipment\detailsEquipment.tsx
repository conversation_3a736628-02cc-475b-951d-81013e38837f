/*
app/equipment/detailsEquipment.tsx

Page de détails d'un équipement.

Informations pertinentes :

*/

import React, { useContext, useEffect, useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  Platform,
  Image,
  Linking,
} from "react-native";
import {
  ParamListBase,
  useNavigation,
  useRoute,
  useFocusEffect,
} from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import { useLocalSearchParams } from "expo-router";
import { useRouter } from "expo-router";
import Header from "@/components/common/Header";
import moment from "moment";

export default function detailsEquipment() {
  const params = useLocalSearchParams();
  const router = useRouter();
  const [equipment, setEquipment] = useState<any>(null);
  const { user } = useContext(AppContext);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  // Récupérer les informations de l'équipement
  const fetchEquipmentDetails = async () => {
    try {
      if (!params.uuid) {
        console.error("Aucun UUID fourni");
        return;
      }

      // Récupérer les informations de la table `equipments`
      const { data: equipmentData, error: equipmentError } = await supabase
        .from("equipments")
        .select("*, users(first_name, last_name)")
        .eq("uuid", params.uuid)
        .single();

      if (equipmentError) {
        console.error(
          "Erreur lors de la récupération des informations de l'équipement :",
          equipmentError,
        );
        return;
      }

      setEquipment(equipmentData);
    } catch (err) {
      console.error("Erreur inattendue :", err);
    }
  };

  // Récupérer les informations de l'équipement au chargement et après chaque focus
  useFocusEffect(
    React.useCallback(() => {
      fetchEquipmentDetails();
    }, []),
  );

  // Restrictions selon le statut
  useEffect(() => {
    if (!user) return;

    if (["User", "Extern", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page.",
      );
      navigation.goBack();
    }
  }, [user]);

  const handlUpdateIcon = () => {
    router.push({
      pathname: "/equipment/updateEquipment",
      params: {
        id: params.id,
        uuid: params.uuid,
        name: params.name,
        reference: params.reference,
        type: params.type,
        status: params.status,
        price: params.price,
        document: params.document,
        image: params.image,
        purchase_date: params.purchase_date,
        verification_date: params.verification_date,
      },
    });
  };

  // Supprimer l'équipement
  const handleDeleteEquipment = async () => {
    const confirmDelete =
      Platform.OS === "web"
        ? window.confirm("Êtes-vous sûr de vouloir supprimer cet équipment ?")
        : await new Promise((resolve) =>
            Alert.alert(
              "Confirmation",
              "Êtes-vous sûr de vouloir supprimer cet équipement ?",
              [
                {
                  text: "Annuler",
                  style: "cancel",
                  onPress: () => resolve(false),
                },
                { text: "Oui, supprimer", onPress: () => resolve(true) },
              ],
            ),
          );

    if (!confirmDelete) return;

    try {
      if (!params.uuid) {
        console.error("UUID manquant pour la suppression");
        return;
      }

      const { error: deleteEquipmentError } = await supabase
        .from("equipments")
        .delete()
        .eq("uuid", params.uuid);

      if (deleteEquipmentError) throw deleteEquipmentError;

      alert("Équipement supprimé avec succès.");
      navigation.goBack();
    } catch (error) {
      console.error("❌ Erreur lors de la suppression :", error);
      alert("Une erreur est survenue lors de la suppression.");
    }
  };

  // Gestion des types
  const getEquipmentsTypes = (type: string) => {
    switch (type?.toLowerCase()) {
      case "cyclic":
        return { title: "Cyclique" };
      case "custom":
        return { title: "Personnalisé" };
      default:
        return { title: "Cyclique" };
    }
  };

  // Gestion des statuts
  const getEquipmentsStatusStyle = (status: string | undefined, verificationDate: string | undefined) => {
    if (!status) return { text: "Conforme", backgroundColor: "#2FC12B" };

    // Vérifier si la date de vérification est dépassée
    if (verificationDate) {
      const today = new Date();
      const verificationDateObj = new Date(verificationDate);
      if (verificationDateObj < today && status === "compliant") {
        return { text: "À vérifier", backgroundColor: "#F99527" };
      }
    }
    
    switch (status) {
      case "compliant":
        return { text: "Conforme", backgroundColor: "#2FC12B" };
      case "to_be_checked":
        return { text: "À vérifier", backgroundColor: "#F99527" };
      case "under_verification":
        return { text: "En cours de vérification", backgroundColor: "#277FF9" };
      case "out_of_service":
        return { text: "Hors service", backgroundColor: "#737373" };
      default:
        return { text: "Conforme", backgroundColor: "#2FC12B" };
    }
  };

  return (
    <>
      <Header onPressIcon={() => handlUpdateIcon()} />

      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {/* Image */}
        {equipment?.image && (
          <View style={styles.imageContainer}>
            <Image
              source={{ uri: equipment.image }}
              style={styles.detailImage}
              resizeMode="cover"
            />
          </View>
        )}

        {/* Informations */}
        <View style={styles.equipmentCard}>
          <Text style={styles.cardName}>{equipment?.name}</Text>

          <Text style={styles.text}>
            Date d'achat :{" "}
            {moment(equipment?.purchase_date).format("DD/MM/YYYY")}
          </Text>

          {equipment?.type && (
            <Text style={styles.text}>
              Type d'EPI : {getEquipmentsTypes(equipment.type).title}
            </Text>
          )}

          <Text style={styles.text}>Coût : {equipment?.price}</Text>

          {equipment?.reference && (
            <Text style={styles.text}>Référence : {equipment.reference}</Text>
          )}

          {equipment?.users && (
            <Text style={styles.text}>
              Détenteur : {equipment.users.first_name}{" "}
              {equipment.users.last_name}
            </Text>
          )}
          {equipment?.delivery_date && equipment?.users && (
            <Text style={styles.text}>
              Date de remise :{" "}
              {moment(equipment?.delivery_date).format("DD/MM/YYYY")}
            </Text>
          )}

          {equipment?.verification_date && (
            <Text style={styles.text}>
              Date de vérification :{" "}
              {moment(equipment?.verification_date).format("DD/MM/YYYY")}
            </Text>
          )}

          {/* Statut de l'équipement */}
          {equipment?.status && (
            <View style={styles.statusContainer}>
              <Text style={[styles.statusText, { backgroundColor: getEquipmentsStatusStyle(equipment.status, equipment.verification_date).backgroundColor }]}>
                {getEquipmentsStatusStyle(equipment.status, equipment.verification_date).text}
              </Text>
            </View>
          )}

        </View>

        {/* Assigner l'EPI */}
        <View style={styles.deleteButtonContainer}>
          <TouchableOpacity
            style={styles.openDocumentButton}
            onPress={() =>
              router.push({
                pathname: "/equipment/assignEquipment",
                params: {
                  id: params.id,
                  uuid: params.uuid,
                },
              })
            }
          >
            <Text style={styles.deleteButtonText}>Modifier l'assignation</Text>
          </TouchableOpacity>
        </View>

        {/* Document */}
        {equipment?.document && (
          <View style={styles.deleteButtonContainer}>
            <TouchableOpacity
              style={styles.openDocumentButton}
              onPress={() => {
                if (equipment.document) {
                  Linking.openURL(equipment.document);
                }
              }}
            >
              <Text style={styles.deleteButtonText}>Voir le document</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Supprimer l'équipement */}
        <View style={styles.deleteButtonContainer}>
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={handleDeleteEquipment}
          >
            <Text style={styles.deleteButtonText}>Supprimer</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
      width: '100%',
    } : {}),
  },
  imageContainer: {
    width: "100%",
    height: 200,
    marginBottom: 20,
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  detailImage: {
    width: "100%",
    height: "100%",
  },
  scrollContainer: {
    flex: 1,
    padding: 20,
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
      width: '100%',
    } : {}),
  },
  headSection: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  confirmButton: {
    borderRadius: 7,
    width: "100%",
    padding: 12,
    backgroundColor: "#f99527",
    marginTop: 20,
    marginBottom: 40,
    alignSelf: "center",
  },
  confirmButtonText: {
    textAlign: "center",
    color: "#FFFFFF",
    fontSize: 14,
  },
  disabledButton: {
    backgroundColor: "#ccc",
  },
  backButton: {
    width: 24,
    textAlign: "left",
  },
  titleWrapper: {
    display: "flex",
    flexDirection: "row",
    marginVertical: 15,
  },
  title: {
    color: "#262627",
    fontSize: 17,
    textAlign: "left",
  },
  sectionButton: {},
  sectionButtonText: {
    fontSize: 14,
    textDecorationLine: "underline",
    color: "#F99527",
    marginVertical: 10,
  },
  listContainer: {
    width: "100%",
    paddingVertical: 10,
  },
  listItem: {
    width: "100%",
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    display: "flex",
    justifyContent: "space-between",
    flexDirection: "column",
    gap: 10,
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  listTitle: {
    fontSize: 16,
    color: "#1C3144",
    fontWeight: "bold",
  },
  equipmentCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    padding: 20,
    marginBottom: 20,
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
      width: '100%',
    } : {}),
  },
  cardName: {
    fontSize: 16,
    color: "#262627",
    fontWeight: "bold",
  },
  leftElementCard: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  elementsTop: {
    display: "flex",
    flexDirection: "column",
    rowGap: 5,
  },
  elementsBottom: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  listName: {
    width: "100%",
    fontSize: 14,
    color: "#262627",
    fontStyle: "normal",
    flexWrap: "wrap",
    fontWeight: "semibold",
  },
  listDescription: {
    width: "70%",
    fontSize: 14,
    color: "#8c8c8c",
    fontStyle: "normal",
    flexWrap: "wrap",
  },
  topElementsCard: {
    backgroundColor: "#FFFFFF",
  },
  bottomElementsCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 7,
    justifyContent: "space-between",
    flexDirection: "column",
    rowGap: 5,
  },
  createEquipmentButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    marginBottom: 16,
    backgroundColor: "#f99527",
    marginTop: 10,
  },
  createEquipmentButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  sectionContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#1C3144",
    marginVertical: 10,
  },
  text: {
    fontSize: 14,
    color: "#000000",
    fontStyle: "normal",
    flexWrap: "wrap",
    fontWeight: "normal",
  },
  texts: {
    fontSize: 10,
    color: "#FFFFFF",
    padding: 8,
    borderRadius: 50,
    fontStyle: "normal",
    flexWrap: "wrap",
    fontWeight: "normal",
    textAlign: "center",
    minWidth: 100,
  },

  deleteButtonContainer: {
    width: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 30,
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  openDocumentButton: {
    width: "100%",
    backgroundColor: "#F99527",
    padding: 12,
    borderRadius: 7,
    alignItems: "center",
    marginTop: 20,
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  deleteButton: {
    width: 200,
    backgroundColor: "#ff4f42",
    padding: 10,
    borderRadius: 8,
    alignItems: "center",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  deleteButtonText: {
    color: "#FFFFFF",
    fontWeight: "normal",
  },
  statusContainer: {
    width: '100%',
    alignItems: 'center',
    marginTop: 10,
  },
  statusText: {
    color: '#FFFFFF',
    padding: 8,
    borderRadius: 50,
    fontSize: 14,
    fontWeight: 'bold',
    minWidth: 120,
    textAlign: 'center',
  },
});
