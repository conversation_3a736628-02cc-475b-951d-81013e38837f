/*
app/event/createActionEvent.tsx

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern`, `Supplier` et `Extern` n'ont pas accès à cette page.
*/

import ContainedButton from "@/components/ui/buttons/ContainedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useFocusEffect, useLocalSearchParams, useRouter } from "expo-router";
import {
  StyleSheet,
  View,
  Text,
  Alert,
  ScrollView,
  TouchableOpacity,
  Platform,
} from "react-native";
import DateTimePicker from "@react-native-community/datetimepicker";
import { DateTimePickerAndroid } from "@react-native-community/datetimepicker";
import * as ImagePicker from "expo-image-picker";
import { useCallback, useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import CustomizedSelect from "@/components/ui/customizedSelect/customizedSelect";
import TypeProcess from "@/types/typeProcess";
import TypeActionEvent from "@/types/typeActionEvent";
import typeUserByAdmin from "@/types/typeUserByAdmin";
import Paper from "@/components/common/paper";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { WebDateTimePicker } from "@/components/ui/datepicker/WebDateTimePicker";
import Header from "@/components/common/Header";

/*
const typesActions = [
  { value: 0, label: "Actions immédiates et/ou de confinement" },
  { value: 1, label: "Actions traitant les causes du problème" },
];
*/

// Choix du dropdown (actions)
const typesActions = [
  { label: "Immédiate", value: "immediate" },
  { label: "Correctif", value: "corrective" },
];

// Choix du dropdown (actions)
const actionStatus = [
  { label: "Réalisée", value: "achieved" },
  { label: "Non réalisée", value: "not_achieved" },
];

export default function CreateActionEvent() {
  const [loading, setLoading] = useState(false);
  const { user, companySelected } = useContext(AppContext);
  const { idEvent, eventName, description } = useLocalSearchParams();
  const [process, setProcess] = useState<TypeProcess[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [usersAdmin, setUsersAdmin] = useState<typeUserByAdmin[] | null>([]);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [eventProcess, setEventProcess] = useState<{ id: number; process_name: string } | null>(null);
  const router = useRouter();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  const fetchData = async () => {
    setIsLoading(true);
    
    // Récupérer le processus de l'événement
    if (idEvent) {
      const { data: eventData, error: eventError } = await supabase
        .from("events")
        .select("process, process:process(*)")
        .eq("id", idEvent)
        .single();
        
      if (!eventError && eventData) {
        setEventProcess(eventData.process);
        
        // Préremplir le formulaire avec le processus de l'événement
        if (eventData.process && typeof eventData.process === 'object') {
          setValue('process_id', eventData.process.id);
        } else if (eventData.process) {
          // Dans le cas où process est juste l'ID
          setValue('process_id', eventData.process);
          
          // Récupérer les détails du processus
          const { data: processDetails, error: processError } = await supabase
            .from("process")
            .select("id, process_name")
            .eq("id", eventData.process)
            .single();
            
          if (!processError && processDetails) {
            setEventProcess(processDetails);
          }
        }
      }
    }

    const { data: dataGetted, error } = await supabase
      .from("process")
      .select()
      .eq("uid_admin", user?.uid)
      .eq("company_id", companySelected?.id);

    if (!error) {
      setProcess(
        (dataGetted?.map((item: any) => {
          return { label: item.process_name, value: item.id };
        }) as any) || null
      );
    }

    const { data, error: error_users } = await supabase
      .from("users")
      .select()
      .eq("uid_admin", user?.uid)
      .eq("status", "User");

    if (!error_users) {
      setUsersAdmin(
        (data?.map((item: any) => {
          return { label: item.first_name, value: item.uid };
        }) as any) || null
      );
    }
    
    setIsLoading(false);
  };

  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [])
  );

  useEffect(() => {
    const fetchUsersAdmin = async () => {};
    fetchUsersAdmin();
  }, []);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern", "Supplier"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors, touchedFields },
    control,
    reset,
  } = useForm<TypeActionEvent>({
    defaultValues: {
      action_id: 0,
      event_id: 0,
      created_at: "",
      process_id: 0,
      uid_user: "",
      company_id: 0,
      wording_action: "",
      date: selectedDate.toISOString(),
      administrator: 0,
      type: 0,
    },
  });

  const inputs = [
    {
      label: "Libellé de l'action",
      name: "wording_action",
      placeholder: "Libellé de l'action",
      type: "Text",
    },
    {
      label: "Date prévu",
      name: "date",
      placeholder: "Date",
      type: "Date",
    },
    {
      label: "Responsable de l'action",
      name: "administrator",
      placeholder: "",
      type: "Select",
      data: usersAdmin,
    },
    {
      label: "Processus porteur",
      name: "process_id",
      placeholder: "",
      type: "DisplayOnly",
      value: eventProcess?.process_name || "Chargement...",
    },
    {
      label: "Types d'actions",
      name: "type",
      placeholder: "",
      type: "Select",
      data: typesActions,
    },
    {
      label: "Statut",
      name: "status",
      placeholder: "",
      type: "Select",
      data: actionStatus,
    },
  ];

  // Date (iOS)
  const handleDateChange = (event: any, date: any) => {
    if (date) {
      setSelectedDate(date); // Mettre à jour avec la date sélectionnée
    }
  };

  // Date (Android)
  const showDatePicker = () => {
    DateTimePickerAndroid.open({
      value: selectedDate,
      mode: "date",
      display: "default",
      onChange: handleDateChange,
    });
  };

  const handleSubmitCreateEvent = async (data: TypeActionEvent) => {
    setLoading(true);
    const created_at = new Date();

    try {
      if (!companySelected?.id || !idEvent) {
        Alert.alert(
          "Erreur",
          "Informations de l'entreprise ou de l'événement manquantes."
        );
        return;
      }

      // Utiliser le processus de l'événement
      if (!eventProcess) {
        Alert.alert("Erreur", "Le processus de l'événement est introuvable.");
        return;
      }

      // Étape 1 : Insertion dans la table actions
      const { data: createdAction, error: actionError } = await supabase
        .from("actions")
        .insert({
          name: data.wording_action,
          date: selectedDate,
          type: data.type,
          status: "not_achieved", // Par défaut
          company_id: companySelected?.id,
          created_at,
        })
        .select("id")
        .single();

      if (actionError) {
        Alert.alert("Échec", "Erreur lors de la création de l'action.");
        console.error("Erreur action :", actionError.message);
        return;
      }

      const actionId = createdAction.id;

      // Étape 2 : Insertion dans action_administrators
      const { error: adminError } = await supabase
        .from("action_administrators")
        .insert({
          action_id: actionId,
          user_id: data.administrator,
        });

      if (adminError) {
        Alert.alert("Erreur", "Impossible d'ajouter l'administrateur.");
        console.error("Erreur admin :", adminError.message);
        return;
      }

      // Étape 3 : Insertion dans action_processes avec le processus de l'événement
      const { error: processError } = await supabase
        .from("action_processes")
        .insert({
          action_id: actionId,
          process_id: eventProcess.id,
        });

      if (processError) {
        Alert.alert("Erreur", "Impossible d'ajouter le processus.");
        console.error("Erreur process :", processError.message);
        return;
      }

      // Étape 4 : Insertion dans action_events
      const { error: eventError } = await supabase
        .from("action_events")
        .insert({
          action_id: actionId,
          event_id: idEvent,
        });

      if (eventError) {
        Alert.alert("Erreur", "Impossible d'ajouter l'événement.");
        console.error("Erreur process :", eventError.message);
        return;
      }

      // Étape 5 : Insertion dans action_events
      const { error: actionForEventError } = await supabase
        .from("event_actions")
        .insert({
          action_id: actionId,
          event_id: idEvent,
          process_id: eventProcess.id,
          uid_user: data.administrator,
          company_id: companySelected?.id,
          wording_action: data.wording_action,
          date: selectedDate,
          type: data.type,
          status: data.status
        });

      if (actionForEventError) {
        Alert.alert("Erreur", "Impossible d'ajouter l'événement.");
        console.error("Erreur event :", actionForEventError.message);
        return;
      }

      // 🔹 Succès : Reset du formulaire et navigation
      Toast.show({
        type: "success",
        text1: "Action créée avec succès !",
        text1Style: { color: "#1C3144" },
      });

      reset();
      setLoading(false);
      router.back()
    } catch (err) {
      console.error("Erreur inattendue :", err);
      setLoading(false);
      Toast.show({
        type: "error",
        text1: "Une erreur est survenue",
        text1Style: { color: "#1C3144" },
      });
    }
  };

  return (
    <View style={styles.mainContainer}>
      <View style={styles.container}>
        <View style={styles.content}>
          <View style={styles.registerInfo}>
            <Paper
              data={[{ label: "Évènement : " + eventName, 
                value: (description ?? "Pas de description").slice(0, 100) + "..."
              }]}
            />
            <View style={styles.inputs}>
              {inputs.map((input, index) => {
                if (input?.type === "Select") {
                  return (
                    <View key={index}>
                      <CustomizedSelect
                        name={input.name}
                        label={input.label}
                        register={register}
                        control={control}
                        errors={errors}
                        setError={setError}
                        data={input.data as any}
                      />
                    </View>
                  );
                } else if (input?.type === "DisplayOnly") {
                  return (
                    <View key={index} style={styles.displayOnlyContainer}>
                      <Text style={styles.label}>{input.label}</Text>
                      <View style={styles.displayOnlyValue}>
                        <Text>{input.value}</Text>
                      </View>
                    </View>
                  );
                } else {
                  if (input?.type === "Date") {
                    return (
                      <View key={index}>
                        {Platform.OS === "android" && (
                          <TouchableOpacity
                            onPress={showDatePicker}
                            style={styles.dateTimePickerContainer}
                          >
                            <Text style={styles.label}>{"Date"}</Text>
                            <View style={styles.datePickerContainer}>
                              <Text>{selectedDate.toLocaleDateString()}</Text>
                            </View>
                          </TouchableOpacity>
                        )}

                        {Platform.OS === "ios" && (
                          <View style={styles.dateTimePickerContainer}>
                            <Text style={styles.label}>Date</Text>
                            <DateTimePicker
                              value={selectedDate}
                              mode="date"
                              display="default"
                              onChange={handleDateChange}
                              style={styles.datePicker}
                            />
                          </View>
                        )}

                        {Platform.OS === "web" && (
                          <View style={styles.dateTimePickerContainer}>
                            <Text style={styles.label}>Date</Text>
                            <WebDateTimePicker
                              value={selectedDate}
                              onChange={(date) => {
                                if (date) {
                                  setSelectedDate(date);
                                }
                              }}
                              style={styles.datePicker}
                            />
                          </View>
                        )}
                      </View>
                    );
                  } else {
                    return (
                      <View key={index}>
                        <InputCustomized
                          label={input.label}
                          placeholder={input.placeholder}
                          register={register}
                          name={input.name}
                          control={control}
                          errors={errors}
                          setError={setError}
                        />
                      </View>
                    );
                  }
                }
              })}
            </View>
          </View>

          <View style={styles.buttons}>
            <ContainedButton
              label="Créer"
              backgroundColor="#F99527"
              onPress={handleSubmit(handleSubmitCreateEvent)}
              disabled={loading}
            />
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  content: {
    gap: 20,
    padding: 20,
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    width: "100%",
    gap: 20,
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
  dateTimePickerContainer: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    textAlign: "left",
    gap: 10,
  },
  datePickerContainer: {
    width: "100%",
    borderRadius: 4,
    borderStyle: "solid",
    borderWidth: 1,
    borderColor: "#d3d3d3",
    padding: 10,
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
    textAlign: "left",
  },
  datePicker: {
    width: "100%",
    marginTop: 10,
    marginBottom: 10,
  },
  displayOnlyContainer: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    textAlign: "left",
    gap: 10,
  },
  displayOnlyValue: {
    width: "100%",
    borderRadius: 4,
    borderStyle: "solid",
    borderWidth: 1,
    borderColor: "#d3d3d3",
    padding: 10,
    backgroundColor: "#f5f5f5",
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
});
