/*
app/screens/notifications/UpdateFlashQSE.tsx

Formulaire pour modifier un Flash QSE existant.

Informations pertinentes :

- Les informations du Flash QSE sont récupérés depuis la navigation depuis le composant `FlashQSEDetails.tsx`

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern` et `Extern` n'ont pas accès à cette page.
*/

import React, { useContext, useEffect, useState } from "react";
import {
  Alert,
  StyleSheet,
  View,
  AppState,
  TextInput,
  Button,
  Image,
  Text,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { supabase } from "../../../lib/supabase.native";
import { Controller, useForm } from "react-hook-form";
import TypeFlashQSE from "@/types/flashQSE";
import { AppContext } from "@/state/AppContext";
import Toast from "react-native-toast-message";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AntDesign } from "@expo/vector-icons";
import Octicons from "@expo/vector-icons/Octicons";
import * as ImagePicker from "expo-image-picker";
import uploadFlashSQECoverStorage from "@/lib/uploadFlashSQECoverStorage";
import { useRoute, RouteProp } from "@react-navigation/native";

type FlashQSEDetailsRouteParams = {
  flashQSE: {
    id: number;
    name: string;
    context: string;
    cover: string;
    causes: string;
    actions: string;
    uid_company: number;
  };
};

export default function UpdateFlashQSE() {
  const [cover, setCover] = useState<string | null>(null);
  const { user } = useContext(AppContext);
  const route =
    useRoute<RouteProp<{ params: FlashQSEDetailsRouteParams }, "params">>();
  const { flashQSE } = route.params;
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const {
    handleSubmit,
    formState: { errors },
    control,
    reset,
  } = useForm({
    defaultValues: {
      name: flashQSE?.name || "",
      context: flashQSE?.context || "",
      cover: flashQSE?.cover || "",
      causes: flashQSE?.causes || "",
      actions: flashQSE?.actions || "",
      uid_company: flashQSE?.uid_company || "",
    },
  });

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  // Enregistrer l'image
  const handleImagePicker = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled) {
      setCover(result.assets[0].uri);
    }
  };

  // Valider le formulaire
  const handleUpdateFlashQSE = async (data: any) => {
    try {
      if (!user) {
        console.log("L'utilisateur n'es pas connecté ?");
        return;
      }

      // Vérifier et uploader l'image sur Supabase Storage
      let publicUrl = flashQSE?.cover || ""; // Utiliser l'image existante par défaut
      if (cover) {
        const uploadResult = await uploadFlashSQECoverStorage(cover, user?.uid);

        if (!uploadResult || !uploadResult.url) {
          Alert.alert(
            "Erreur",
            `${flashQSE.name} : Impossible de télécharger l'image.`
          );
          return;
        }

        publicUrl = uploadResult.url; // URL publique de l'image
      }

      // Construire les données mises à jour en excluant les valeurs vides
      const updatedData: Partial<typeof flashQSE> = {
        name: data.name || flashQSE.name,
        context: data.context || flashQSE.context,
        causes: data.causes || flashQSE.causes,
        actions: data.actions || flashQSE.actions,
        cover: publicUrl,
      };

      // Effectuer la mise à jour sur Supabase
      const { error } = await supabase
        .from("flash_qse")
        .update(updatedData)
        .eq("id", flashQSE.id);

      if (error) {
        Alert.alert(
          "Erreur",
          `${flashQSE.name} : Une erreur est survenue lors de la modification.`
        );
        return;
      }

      // Afficher l'alerte de succès
      Alert.alert("Flash modifié !", `${flashQSE.name} modifié avec succès.`);

      setTimeout(() => {
        reset();
        navigation.navigate("screens/notifications/FlashQSEList", {
          refresh: true,
        });
      }, 1000);
    } catch (err) {
      Alert.alert(
        "Erreur",
        `${flashQSE.name} : Une erreur est survenue lors de la modification.`
      );
    }
  };

  return (
    <View style={styles.mainContainer}>
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.select({ ios: 0, android: 100 })}
      >
        <ScrollView style={styles.form} contentContainerStyle={styles.formContent}>
          {/* Champ Nom */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Nom</Text>
            <Controller
              control={control}
              name="name"
              rules={{ required: "Le nom est requis." }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  maxLength={20}
                  placeholderTextColor={"#6B7280"}
                  style={styles.input}
                  placeholder={flashQSE?.name || "Nom du Flash QSE"}
                  onChangeText={onChange}
                  value={value}
                />
              )}
            />
            {errors.name && (
              <Text style={styles.errorText}>{errors.name.message}</Text>
            )}
          </View>

          {/* Champ Contexte */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Ce qu'il s'est passé</Text>
            <Controller
              control={control}
              name="context"
              rules={{ required: "Le contexte est requis." }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  maxLength={300}
                  placeholderTextColor={"#6B7280"}
                  style={styles.input}
                  placeholder={flashQSE?.context || "Ce qu'il s'est passé"}
                  onChangeText={onChange}
                  value={value}
                  multiline
                />
              )}
            />
            {errors.context && (
              <Text style={styles.errorText}>{errors.context.message}</Text>
            )}
          </View>

          {/* Champ Image */}
          <Text style={styles.label}>Photo</Text>
          <View style={[styles.inputContainer, styles.imageContainer]}>
            <TouchableOpacity 
              style={styles.imageWrapper}
              onPress={handleImagePicker}
            >
              <Image
                source={{ uri: cover || flashQSE?.cover }}
                style={styles.fullImagePreview}
                resizeMode="contain"
              />
            </TouchableOpacity>
          </View>

          {/* Champ Causes */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Les causes</Text>
            <Controller
              control={control}
              name="causes"
              rules={{ required: "Les causes sont requises." }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  maxLength={150}
                  placeholderTextColor={"#6B7280"}
                  style={styles.input}
                  placeholder={flashQSE?.causes || "Les causes"}
                  onChangeText={onChange}
                  value={value}
                  multiline
                />
              )}
            />
            {errors.causes && (
              <Text style={styles.errorText}>{errors.causes.message}</Text>
            )}
          </View>

          {/* Champ Actions */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Les actions</Text>
            <Controller
              control={control}
              name="actions"
              rules={{ required: "Les actions sont requises." }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  maxLength={150}
                  placeholderTextColor={"#6B7280"}
                  style={styles.input}
                  placeholder={flashQSE?.actions || "Les actions"}
                  onChangeText={onChange}
                  value={value}
                  multiline
                />
              )}
            />
            {errors.actions && (
              <Text style={styles.errorText}>{errors.actions.message}</Text>
            )}
          </View>

          {/* Bouton de soumission */}
          <TouchableOpacity
            style={styles.confirmButton}
            onPress={() => {
              handleSubmit(handleUpdateFlashQSE)();
            }}
          >
            <Text style={styles.confirmButtonText}>Modifier</Text>
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  form: {
    flex: 1,
    paddingHorizontal: 20,
  },
  formContent: {
    paddingBottom: 100,
  },
  headSection: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 20,
    marginBottom: 30,
  },
  backButton: {
    width: 24,
    textAlign: "left",
  },
  titleWrapper: {
    display: "flex",
    flexDirection: "row",
    marginVertical: 15,
  },
  title: {
    color: "#262627",
    fontSize: 17,
    textAlign: "left",
  },
  input: {
    borderWidth: 2,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "auto",
    padding: 12,
    marginTop: 10,
    color: "#000000",
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginBottom: 5,
  },
  infoText: {
    fontSize: 14,
    marginVertical: 10,
    color: "#555",
  },
  inputContainer: {
    position: "relative",
    marginBottom: 20,
    width: "auto",
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
  },
  confirmButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    marginBottom: 200,
    backgroundColor: "#f99527",
    marginTop: 10,
  },
  confirmButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  imagePicker: {
    padding: 10,
    backgroundColor: "#FFFFFF",
    borderColor: "#E5E7EB",
    borderRadius: 8,
    borderWidth: 1,
    alignItems: "center",
    marginBottom: 10,
  },
  imagePickerText: {
    color: "#1F2937",
    fontWeight: "bold",
  },
  imagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  imageContainer: {
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 20,
  },
  imageWrapper: {
    width: "100%",
    aspectRatio: 4/3,
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    overflow: "hidden",
    borderWidth: 1,
    borderColor: "#E5E7EB",
  },
  uploadPlaceholder: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  uploadIcon: {
    borderWidth: 1,
    borderColor: "#f99527",
    borderRadius: 7,
    padding: 5,
  },
  uploadText: {
    textAlign: "center",
    fontWeight: "normal",
  },
  fullImagePreview: {
    width: "100%",
    height: "100%",
  },
});
