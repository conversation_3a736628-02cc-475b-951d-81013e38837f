/*
app/menu/itemsMenuCockpit/objectif.tsx

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `Supplier` n'ont pas accès à cette page.
*/

import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  Platform,
} from "react-native";
import { useRouter} from "expo-router";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { useContext, useEffect, useState, useCallback } from "react";
import { Spinner } from "@/components/ui/spinner";
import { supabase } from "@/lib/supabase";
import { AppContext } from "@/state/AppContext";
import TypeObjectif from "@/types/typeObjectif";
import PaperInfo from "@/components/common/paperInfo";
import { Octicons } from "@expo/vector-icons";
import FilterPage from "@/components/common/filterPage";
import Header from "@/components/common/Header";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { useFocusEffect } from "@react-navigation/native";

export default function Objectif() {
  const router = useRouter();
  const [data, setData] = useState<TypeObjectif[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { user, companySelected } = useContext(AppContext);
  const [showFilter, setShowFilter] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  // État pour gérer les filtres
  const [filters, setFilters] = useState<{
    objectifAtteint: boolean;
    objectifPartiel: boolean;
    objectifNonAtteint: boolean;
  }>({
    objectifAtteint: false,
    objectifPartiel: false,
    objectifNonAtteint: false,
  });

  const filterLabels = {
    objectifAtteint: "Objectif atteint",
    objectifPartiel: "Objectif partiellement atteint",
    objectifNonAtteint: "Objectif non atteint",
  };

  useFocusEffect(
    useCallback(() => {
      // Écoute les changements en temps réel pour les objectifs
      const objectifsChannel = supabase
        .channel("objectifs-changes")
        .on(
          "postgres_changes",
          { 
            event: "*", 
            schema: "public", 
            table: "objectifs",
            filter: `company_id=eq.${companySelected?.id}`
          },
          () => {
            console.log("Changement d'objectif détecté");
            setRefreshKey((prev) => prev + 1);
          }
        )
        .subscribe();

      // Écoute les changements en temps réel pour les valeurs
      const valuesChannel = supabase
        .channel("values-changes")
        .on(
          "postgres_changes",
          { 
            event: "*", 
            schema: "public", 
            table: "values",
            filter: `company_id=eq.${companySelected?.id}`
          },
          () => {
            console.log("Changement de valeur détecté");
            setRefreshKey((prev) => prev + 1);
          }
        )
        .subscribe();

      // Écoute les changements en temps réel pour les valeurs spécifiques à chaque objectif
      const objectifValuesChannel = supabase
        .channel("objectif-values-changes")
        .on(
          "postgres_changes",
          { 
            event: "*", 
            schema: "public", 
            table: "values"
          },
          () => {
            console.log("Changement de valeur d'objectif détecté");
            setRefreshKey((prev) => prev + 1);
          }
        )
        .subscribe();
  
      return () => {
        supabase.removeChannel(objectifsChannel);
        supabase.removeChannel(valuesChannel);
        supabase.removeChannel(objectifValuesChannel);
      };
    }, [companySelected?.id])
  );

  useEffect(() => {
    if (!companySelected?.id) {
      console.error("Aucun `company_id` défini, requête annulée !");
      return;
    }
    
    setIsLoading(true);
    const fetchData = async () => {
      const { data: dataGetted, error } = await supabase
        .from("objectifs")
        .select(`
          *,
          process(*),
          values(*),
          companies(*)
        `)
        .eq("company_id", companySelected.id)
        .order('created_at', { ascending: false });
  
      if (!error && dataGetted) {
        // Trier les valeurs par date_value pour chaque objectif
        const sortedData = dataGetted.map((obj: any) => {
          // Filtrer les valeurs sans date_value
          const valuesWithDate = obj.values?.filter((v: any) => v.date_value) || [];
          const valuesWithoutDate = obj.values?.filter((v: any) => !v.date_value) || [];
          
          // Trier les valeurs avec date_value par date décroissante
          const sortedValuesWithDate = valuesWithDate.sort((a: any, b: any) => 
            new Date(b.date_value).getTime() - new Date(a.date_value).getTime()
          );
          
          // Trier les valeurs sans date_value par created_at décroissant
          const sortedValuesWithoutDate = valuesWithoutDate.sort((a: any, b: any) => 
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
          );
          
          // Combiner les deux listes triées (d'abord celles avec date_value, puis celles sans)
          const allSortedValues = [...sortedValuesWithDate, ...sortedValuesWithoutDate];
          
          return {
            ...obj,
            values: allSortedValues,
            // Mettre à jour le level avec celui de la dernière valeur
            level: allSortedValues[0]?.level || obj.level
          };
        });
        
        console.log("Données triées :", sortedData);
        setData(sortedData);
      }
      setIsLoading(false);
    };
  
    fetchData();
  }, [companySelected, refreshKey]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["User", "Supplier"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const filteredData =
    filters.objectifAtteint ||
    filters.objectifPartiel ||
    filters.objectifNonAtteint
      ? data.filter((item) => {
          if (filters.objectifAtteint && item.level === 2) return true;
          if (filters.objectifPartiel && item.level === 1) return true;
          if (filters.objectifNonAtteint && item.level === 0) return true;
          return false;
        })
      : data;

  return (
    <View style={styles.mainContainer}>
      <Header
        title={showFilter ? "Filtrer les objectifs" : "Objectifs"}
        onPressIcon={() => setShowFilter(!showFilter)}
        onPressFlesh={() => {
          if (showFilter) {
            setShowFilter(false);
          } else {
            navigation.goBack();
          }
        }}
      />
      <ScrollView style={styles.container}>
        {showFilter ? (
          <FilterPage
            title={"Processus"}
            filters={filters}
            setFilters={setFilters}
            filterLabels={filterLabels} />
        ) : (
          <View style={styles.content}>
            <View style={styles.header}>
              <Text style={{ fontWeight: "bold" }}>
                Créer un nouvel objectif :
              </Text>
              <TouchableOpacity
                onPress={() => router.navigate("/objectif/createObjectif")}
              >
                <Octicons name="diff-added" size={25} color="#F99527" />
              </TouchableOpacity>
            </View>

            <View style={styles.users}>
              {isLoading ? (
                <Spinner size="small" color="#1C3144" />
              ) : filteredData.length > 0 ? (
                filteredData.map((item, index) => (
                  <PaperInfo
                    key={index}
                    title={item.designation}
                    text1={`Cible : ${item.target}`}
                    text2={`Dernière valeur : ${item?.values?.[0]?.value_name || ""}`}
                    text4={item.process?.process_name}
                    level={item?.values?.[0]?.level || item.level}
                    onPress={() => {
                      router.push({
                        pathname: "/objectif/detailsObjectif",
                        params: {
                          uid_user: user?.uid,
                          target: item.target,
                          designation: item.designation,
                          measuring_indicator: item.measuring_indicator,
                          process_id: item.process_id,
                          level: item?.values?.[0]?.level || item.level,
                          created_at: item.created_at,
                          objectif_id: item.id,
                          company_id: item.company_id,
                        },
                      });
                    }} />
                ))
              ) : (
                <Text>Aucun objectif</Text>
              )}
            </View>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  content: {
    gap: 10,
    marginBottom: 10,
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 14,
  },
  users: {
    paddingHorizontal: 10,
    gap: 10,
  },
});
