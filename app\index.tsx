/*
app/index.tsx

Page d'accueil de l'application.
*/

import {
  FlatList,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { AppContext } from "@/state/AppContext";
import React, { useContext, useEffect, useState } from "react";
import { SafeAreaProvider } from "react-native-safe-area-context";
import MenuDefault from "./menu/menuDefault";
import MenuCockpit from "./menu/menuCockpit";
import Header from "@/components/common/Header";
import { usePathname } from "expo-router";

export default function HomeScreen() {
  const { platformUser } = useContext(AppContext);

  const pathName = usePathname();

  return (
    <View style={styles.container}>
      <SafeAreaProvider>
        {pathName !== "/Objectifs" &&
          pathName !== "/Evenements" &&
          pathName !== "/Documents" &&
          pathName !== "/Audit" &&
          pathName !== "/Action" &&
          pathName !== "/Informations" &&
          pathName !== "/PIP" &&
          pathName !== "/Equipements" &&
          pathName !== "/Risques" && <Header />}
        {platformUser ? <MenuDefault /> : <MenuCockpit />}
      </SafeAreaProvider>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    height: "100%",
    flexDirection: "column",
    flex: 1,
  },
  content: {
    backgroundColor: "green",
    height: "93%",
  },
  menu: {
    height: "7%",
    backgroundColor: "yellow",
  },
});
