/*
app/screens/actions/ActionDetails.tsx

Page de détails d'une action.

Informations pertinentes :

- Les informations d'une action sont récupérése depuis la navigation du composant `action.tsx`
*/

import {
  Alert,
  Keyboard,
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
  ScrollView,
  Platform
} from "react-native";
import { ParamListBase, useNavigation, useFocusEffect } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { useRoute, RouteProp } from "@react-navigation/native";
import React, { useState, useCallback, useEffect } from "react";
import moment from "moment";
import { supabase } from "@/lib/supabase";
import Header from "@/components/common/Header";
import { router } from "expo-router";

// Données de navigation
type ActionDetailsRouteParams = {
  action?: any; // Peut être un objet ou une chaîne JSON
  action_id?: number;
  forceRefresh?: number; // Un timestamp pour forcer le rafraîchissement
};

export default function ActionDetails() {
  const [showModal, setShowModal] = useState(false);
  const [showValideModal, setValideShowModal] = useState(false);
  const [action, setAction] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [documents, setDocuments] = useState<any[]>([]);
  const [loadingDocuments, setLoadingDocuments] = useState(false);

  const route = useRoute<RouteProp<Record<string, ActionDetailsRouteParams>, string>>();
  const { action: actionParam, action_id, forceRefresh } = route.params;
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  
  // Fonction pour charger les données de l'action
  const loadActionDetails = useCallback(async () => {
    try {
      setLoading(true);
      console.log("Chargement des détails avec forceRefresh =", forceRefresh);
      
      // Si nous avons une action directement
      if (actionParam && typeof actionParam === 'object') {
        setAction(actionParam);
        setLoading(false);
        return;
      }
      
      // Si nous avons une action en JSON string
      if (actionParam && typeof actionParam === 'string') {
        try {
          const decodedAction = JSON.parse(actionParam);
          setAction(decodedAction);
          setLoading(false);
          return;
        } catch (e) {
          console.error("Erreur lors du parsing de l'action JSON:", e);
        }
      }
      
      // Sinon, charger les données depuis l'ID
      if (action_id) {
        console.log("Chargement des détails de l'action:", action_id);
        
        const { data, error } = await supabase
          .from("actions")
          .select(`
            *,
            action_administrators!inner(users:users(*)),
            action_processes!inner(process:process(*)),
            action_events(event_id, events:events(*))
          `)
          .eq("id", action_id)
          .single();
        
        if (error) {
          console.error("Erreur lors du chargement des détails de l'action:", error.message);
          return;
        }
        
        if (data) {
          console.log("Données de l'action chargées:", data);
          
          const formattedAction = {
            ...data,
            administrator: data.action_administrators[0].users,
            process: data.action_processes[0].process,
            event: data.action_events?.[0]?.events
          };
          
          setAction(formattedAction);
        }
      }
    } catch (err) {
      console.error("Erreur inattendue lors du chargement des détails:", err);
    } finally {
      setLoading(false);
    }
  }, [actionParam, action_id, forceRefresh]);

  // Recharger les données quand l'écran est affiché ou quand forceRefresh change
  useFocusEffect(
    useCallback(() => {
      console.log("ActionDetails est de retour au premier plan - rechargement des données");
      // Recharger les données à chaque fois que l'écran revient au focus ou que forceRefresh change
      loadActionDetails();
      
      // Nettoyer le focus effect lors du démontage du composant
      return () => {
        console.log("ActionDetails quitte le premier plan");
      };
    }, [loadActionDetails, forceRefresh]) // Ajouter forceRefresh comme dépendance
  );

  // Fonction pour charger les documents de l'action
  const loadActionDocuments = async () => {
    try {
      setLoadingDocuments(true);
      const { data, error } = await supabase
        .from("actions_documents")
        .select("*")
        .eq("action_id", action.id);

      if (error) {
        console.error("Erreur lors du chargement des documents:", error.message);
        return;
      }

      setDocuments(data || []);
    } catch (err) {
      console.error("Erreur inattendue lors du chargement des documents:", err);
    } finally {
      setLoadingDocuments(false);
    }
  };

  // Recharger les documents quand l'écran est affiché
  useFocusEffect(
    useCallback(() => {
      if (action) {
        loadActionDocuments();
      }
    }, [action])
  );

  // Si les données sont en cours de chargement ou non disponibles
  if (loading || !action) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text>Chargement...</Text>
      </View>
    );
  }

  // Supprimer l'action
  const handleDeleteAction = async () => {
    try {
      const { error: deleteActionError } = await supabase
        .from("actions")
        .delete()
        .eq("id", action.id);

      if (deleteActionError) {
        Alert.alert("Erreur", "Échec de la suppression de l'action.");
        return;
      }

      // Supprimer les entrées des tables de jointures
      await supabase
        .from("action_administrators")
        .delete()
        .eq("action_id", action.id);
      await supabase.from("action_events").delete().eq("action_id", action.id);
      await supabase
        .from("action_processes")
        .delete()
        .eq("action_id", action.id);

      Alert.alert("Succès", "L'action a été supprimée.");
      setShowModal(false);
      navigation.goBack();
    } catch (error) {
      Alert.alert("Erreur", "Une erreur est survenue lors de la suppression.");
    }
  };


  const handleUpdateAction = async (resultAction : string ) => {
    try {
      const { error } = await supabase
        .from("actions")
        .update({ status: "achieved", resultAction }) // ✅ Met à jour le statut
        .eq("id", action.id);
  
      if (error) {
        Alert.alert("Erreur", "Échec de la mise à jour de l'action."+error.message);
        return;
      }
  
      Alert.alert("Succès", "L'action a été validée.");
      setValideShowModal(false);
      navigation.goBack();
    } catch (error) {
      Alert.alert("Erreur", "Une erreur est survenue lors de la mise à jour.");
    }
  };
  
  // Supprimer un document
  const handleDeleteDocument = async (documentId: number) => {
    try {
      const { error } = await supabase
        .from("actions_documents")
        .delete()
        .eq("id", documentId);

      if (error) {
        Alert.alert("Erreur", "Échec de la suppression du document.");
        return;
      }

      Alert.alert("Succès", "Le document a été supprimé.");
      loadActionDocuments(); // Recharger la liste des documents
    } catch (error) {
      Alert.alert("Erreur", "Une erreur est survenue lors de la suppression.");
    }
  };

  return (
    <>
      <Header
        onPressIcon={() => {
          // Naviguer vers UpdateAction avec les données de l'action
          navigation.navigate("screens/actions/UpdateAction", { 
            action: JSON.stringify({
              id: action.id,
              name: action.name,
              type: action.type,
              status: action.status,
              date: action.date,
              administrator: action.administrator?.uid,
              process: action.process?.id
            })
          });
          
          // Pas besoin d'ajouter un listener car useFocusEffect s'en charge déjà
          console.log("Navigation vers UpdateAction. Le rechargement se fera automatiquement grâce à useFocusEffect");
        }}
      />
      <View style={[
        Platform.OS === 'web' ? { 
          maxWidth: 800, 
          width: '100%', 
          alignSelf: 'center',
          flex: 1,
          maxHeight: '100%'
        } : { flex: 1 }
      ]}>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <ScrollView 
            style={{ 
              flex: 1, 
              paddingHorizontal: 30, 
              width: "100%", 
              marginBottom:50 
            }}
            contentContainerStyle={Platform.OS === 'web' ? { minHeight: '100%' } : undefined}
          >
            {loading ? (
              <View style={styles.loadingContainer}>
                <Text>Chargement des données...</Text>
              </View>
            ) : (
              <>
                {/* Informations action */}
                <View style={styles.card}>
                  {action ? (
                    <View style={styles.insideCard}>
                      {/* 1ère ligne d'éléments */}
                      <View style={styles.elementsTop}>
                        <Text style={styles.detailName}>
                          {action.name || "Aucune données"}
                        </Text>
                      </View>
                      <View style={styles.elementsTop}>
                        <Text style={styles.date}>
                          {moment(action.date).format("DD/MM/YYYY")}
                        </Text>
                      </View>

                      {/* 2ème ligne d'éléments */}
                      <View style={styles.infoContainer}>
                        <View style={styles.row}>
                          <Text style={styles.textLabel}>Responsable :</Text>
                          <Text style={styles.textValue}>
                            {action.administrator?.first_name || "Aucune données"}{" "}
                            {action.administrator?.last_name || "Aucune données"}
                          </Text>
                        </View>

                        <View style={styles.row}>
                          <Text style={styles.textLabel}>Processus :</Text>
                          <Text style={styles.textValue}>
                            {action?.process?.process_name || "Aucune données"}
                          </Text>
                        </View>

                        {action.type !== "preventive" && (
                          <View style={styles.row}>
                            <Text style={styles.textLabel}>Évènements :</Text>
                            <Text style={styles.textValue}>
                              {action?.event?.wording ? action.event.wording : "Aucun événement"}
                            </Text>
                          </View>
                        )}

                        <View style={styles.row}>
                          <Text style={styles.textLabel}>Type :</Text>
                          <Text style={styles.textValue}>
                            {action.type === "immediate" ? "Action immédiate" : 
                             action.type === "preventive" ? "Action préventive" : 
                             "Action corrective"}
                          </Text>
                        </View>

                        <View style={styles.row}>
                          <Text style={styles.textLabel}>Status :</Text>
                          <Text style={[styles.textValue, action.status === "achieved" ? styles.statusAchieved : styles.statusPending]}>
                            {action.status == "achieved" ? "Action réalisée" : "Action non réalisée"}
                          </Text>
                        </View>
                        {action.resultAction === "efficace" || action.resultAction === "inefficace" ? 
                          <View style={styles.row}>
                            <Text style={styles.textLabel}>Résultat :</Text>
                            <Text style={[styles.textValue, action.resultAction === "efficace" ? styles.statusAchieved : styles.statusPending]}>
                              {action.resultAction == "efficace" ? "Action efficace" : "Action inefficace"}
                            </Text>
                          </View>
                          : ""
                        }
                      </View>
                    </View>
                  ) : (
                    <Text>Les détails de l'action ne sont pas disponibles.</Text>
                  )}
                </View>

                {/* Section Documents */}
                <View style={styles.card}>
                  <View style={styles.insideCard}>
                    <Text style={styles.sectionTitle}>Documents annexes</Text>
                    
                    {/* Bouton Ajouter un document */}
                    <TouchableOpacity
                      style={styles.addDocumentButton}
                      onPress={() => {
                        router.push({
                          pathname: "/screens/actions/CreateActionDocument",
                          params: { action_id: action.id }
                        });
                      }}
                    >
                      <Text style={styles.addDocumentButtonText}>Ajouter un document</Text>
                    </TouchableOpacity>

                    {/* Liste des documents */}
                    {loadingDocuments ? (
                      <View style={styles.loadingContainer}>
                        <Text>Chargement des documents...</Text>
                      </View>
                    ) : documents.length > 0 ? (
                      documents.map((doc) => (
                        <View key={doc.id} style={styles.documentItem}>
                          <View style={styles.documentInfo}>
                            <Text style={styles.documentName}>{doc.name}</Text>
                            <Text style={styles.documentDescription}>{doc.description}</Text>
                            <Text style={styles.documentDate}>
                              Ajouté le {moment(doc.created_at).format("DD/MM/YYYY")}
                            </Text>
                          </View>
                          <View style={styles.documentActions}>
                            <TouchableOpacity
                              style={styles.documentButton}
                              onPress={() => {
                                navigation.navigate("screens/actions/UpdateActionDocument", {
                                  document: doc
                                });
                              }}
                            >
                              <Text style={styles.documentButtonText}>Modifier</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                              style={[styles.documentButton, styles.deleteDocButton]}
                              onPress={() => handleDeleteDocument(doc.id)}
                            >
                              <Text style={styles.documentButtonText}>Supprimer</Text>
                            </TouchableOpacity>
                          </View>
                        </View>
                      ))
                    ) : (
                      <Text style={styles.noDocumentsText}>Aucun document annexe</Text>
                    )}
                  </View>
                </View>

                {/* Bouton de validation visible seulement si l'action n'est pas achevée */}
                {action.status !== "achieved" && (
                  <View style={styles.deleteButtonContainer}>
                    <TouchableOpacity
                      style={styles.validateButton}
                      onPress={() => setValideShowModal(true)}
                    >
                      <Text style={styles.deleteButtonText}>Valider l'action</Text>
                    </TouchableOpacity>
                  </View>
                )}

                {/* Bouton Supprimer */}
                <View style={styles.deleteButtonContainer}>
                  <TouchableOpacity
                    style={styles.deleteButton}
                    onPress={() => setShowModal(true)}
                  >
                    <Text style={styles.deleteButtonText}>Supprimer l'action</Text>
                  </TouchableOpacity>
                </View>
              </>
            )}
          </ScrollView>
        </TouchableWithoutFeedback>
      </View>

      {/* Modale de confirmation */}
      {showModal && (
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>
              Supprimer l'action "{action.name}" ?
            </Text>
            <Text style={styles.modalText}>
              La suppression est définitive.
            </Text>
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setShowModal(false)}
              >
                <Text style={styles.cancelButtonText}>Annuler</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.confirmButton}
                onPress={handleDeleteAction}
              >
                <Text style={styles.confirmButtonText}>Confirmer</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}

      {showValideModal && (
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>
              Valider l'action "{action.name}" ?
            </Text>
            <Text style={styles.modalText}>
              La validation est définitive.
            </Text>
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.confirmValideButton}
                onPress={() => handleUpdateAction("efficace")}
              >
                <Text style={styles.confirmButtonText}>Action efficace</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.confirmEnefficaceValideButton}
                onPress={() => handleUpdateAction("inefficace")}
              >
                <Text style={styles.confirmButtonText}>Action inefficace</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setValideShowModal(false)}
              >
                <Text style={styles.cancelButtonText}>Annuler</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 10,
    marginTop: 14,
  },
  headSection: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  backButton: {
    width: 24,
    textAlign: "left",
  },
  titleWrapper: {
    display: "flex",
    flexDirection: "row",
    marginVertical: 15,
  },
  title: {
    color: "#262627",
    fontSize: 17,
    textAlign: "left",
  },
  card: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    alignContent: "center",
    padding: 5,
    borderRadius: 8,
    marginVertical: 20,
    shadowColor: "#CCC",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.9,
    shadowRadius: 2,
    elevation: 1,
    rowGap: 5,
    backgroundColor: "#FFFFFF",
  },
  insideCard: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    rowGap: 10,
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  detailImage: {
    width: "100%",
    height: 230,
    borderRadius: 10,
    marginBottom: 20,
  },
  detailTitle: {
    fontSize: 17,
    fontWeight: "bold",
    color: "#1C3144",
    marginBottom: 10,
  },
  detailName: {
    fontSize: 15,
    color: "#262627",
    fontStyle: "normal",
    fontWeight: "600", // 🔹 Remplace "semibold" par une valeur correcte
    flexWrap: "wrap", // 🔹 Permet le retour à la ligne
    maxWidth: "70%", // 🔹 Empêche le texte de trop s'étirer
  },
  date: {
    fontSize: 14,
    color: "#525252",
    fontStyle: "normal",
    fontWeight: "normal",
    flexShrink: 0, // 🔹 Empêche la date d'être réduite
    textAlign: "right", // 🔹 Garde la date alignée à droite
    maxWidth: "100%", // 🔹 Évite que la date soit poussée hors de l'écran
  },
  texts: {
    fontSize: 14,
    color: "#262627",
    fontStyle: "normal",
    flexWrap: "wrap",
    fontWeight: "normal",
  },
  detailDescription: {
    fontSize: 16,
    color: "#525252",
    marginBottom: 5,
  },
  listDescription2: {
    // width: "70%",
    fontSize: 14,
    color: "#1c3144",
    fontStyle: "normal",
    flexWrap: "wrap",
  },
  elementsTop: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  separatorContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  leftElements: {
    rowGap: 10,
  },
  rightElements: {
    rowGap: 10,
    
  },
  textLeft: {
    fontSize: 14,
    color: "#262627",
    fontStyle: "italic",
    flexWrap: "wrap",
    fontWeight: "normal",
  },
  textRight: {
    fontSize: 14,
    color: "#262627",
    fontStyle: "normal",
    flexWrap: "wrap",
    fontWeight: "bold",
  },
  deleteButtonContainer: {
    width: "100%",
    display: "flex",
    justifyContent: "center",
    textAlign: "center",
    alignItems: "center",
    // position: "absolute",
    marginTop: 30,
  },
  validateButton: {
    width: 200,
    backgroundColor: "#2FC12B",
    padding: 10,
    borderRadius: 8,
    alignItems: "center",
  },
  deleteDocButton: {
    width: 150,
    backgroundColor: "#ff4f42",
    padding: 10,
    borderRadius: 8,
    alignItems: "center",
  },
  deleteButton: {
    width: 200,
    backgroundColor: "#ff4f42",
    padding: 10,
    borderRadius: 8,
    alignItems: "center",
  },
  deleteButtonText: {
    color: "#FFFFFF",
    fontWeight: "normal",
  },
  modalContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    width: 300,
    backgroundColor: "#fff",
    padding: 20,
    borderRadius: 10,
    alignItems: "center",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 10,
  },
  modalText: {
    fontSize: 14,
    marginBottom: 20,
    textAlign: "center",
  },
  modalButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
  },
  cancelButton: {
    flex: 1,
    padding: 10,
    backgroundColor: "#ccc",
    borderRadius: 5,
    alignItems: "center",
    marginRight: 10,
  },
  cancelButtonText: {
    fontWeight: "bold",
  },
  confirmButton: {
    flex: 1,
    padding: 10,
    backgroundColor: "red",
    borderRadius: 5,
    alignItems: "center",
  },
  confirmValideButton: {
    flex: 1,
    padding: 10,
    backgroundColor: "green",
    borderRadius: 5,
    marginLeft: 3,
    marginRight: 3,
    marginBottom: 5,
    alignItems: "center",
  },
  confirmEnefficaceValideButton: {
    flex: 1,
    padding: 10,
    backgroundColor: "red",
    borderRadius: 5,
    marginLeft: 3,
    marginRight: 3,
    marginBottom: 5,
    alignItems: "center",
  },
  confirmButtonText: {
    color: "#FFFFFF",
    fontWeight: "bold",
  },
  infoContainer: {
    width: "100%",
    backgroundColor: "#f5f5f5", // Fond léger pour différencier la section
    padding: 15,
    borderRadius: 8,
    marginVertical: 10,
    elevation: 3, // Ombre légère pour démarquer la section
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  row: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderBottomWidth: 1, // Séparateur entre chaque ligne
    borderBottomColor: "#ddd",
    paddingVertical: 10,
  },
  textLabel: {
    fontSize: 14,
    color: "#262627",
    fontWeight: "bold",
    minWidth: 120, // Assure un bon alignement des colonnes
  },
  textValue: {
    fontSize: 14,
    color: "#525252",
    flexShrink: 1, // Permet le retour à la ligne si besoin
    textAlign: "right", // Alignement propre des valeurs
  },
  statusAchieved: {
    color: "#2FC12B", // Vert pour succès
    fontWeight: "bold",
  },
  statusPending: {
    color: "#ff4f42", // Rouge pour en attente
    fontWeight: "bold",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  containerContent: {
    padding: 20,
    flex: 1,
  },
  detailsContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
    gap: 10,
  },
  buttonContainer: {
    gap: 10,
  },
  button: {
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#262627",
    marginBottom: 15,
  },
  addDocumentButton: {
    backgroundColor: "#f99527",
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
    marginBottom: 20,
  },
  addDocumentButtonText: {
    color: "#FFFFFF",
    fontWeight: "bold",
  },
  documentItem: {
    backgroundColor: "#f5f5f5",
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
  },
  documentInfo: {
    marginBottom: 10,
  },
  documentName: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#262627",
    marginBottom: 5,
  },
  documentDescription: {
    fontSize: 14,
    color: "#525252",
    marginBottom: 5,
  },
  documentDate: {
    fontSize: 12,
    color: "#888",
  },
  documentActions: {
    flexDirection: "row",
    justifyContent: "flex-end",
    gap: 10,
  },
  documentButton: {
    backgroundColor: "#f99527",
    padding: 8,
    borderRadius: 5,
    minWidth: 80,
    alignItems: "center",
  },
  documentButtonText: {
    color: "#FFFFFF",
    fontSize: 12,
  },
  noDocumentsText: {
    textAlign: "center",
    color: "#888",
    fontStyle: "italic",
  },
});
