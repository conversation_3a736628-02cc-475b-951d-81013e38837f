import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { Input, InputField } from "../input/index.native";
import { Controller } from "react-hook-form";
import { useEffect, useState } from "react";
import { Select, SelectTrigger, SelectContent, SelectItem } from "../select";
import { Ionicons } from "@expo/vector-icons";

type TypesProps = {
  label?: string;
  placeholder?: string;
  typeInput?: string;
  defaultValue?: any;
  onChangeText?: any;
  register?: any;
  name: string;
  control?: any;
  errors?: any;
  setError?: any;
  isDisabled?: boolean;
  isReadOnly?: boolean;
  height?: number;
  isRequired?: boolean;
  multiline?:boolean;
  secureTextEntry?: boolean;
};

const InputCustomized = ({
  label,
  placeholder,
  defaultValue,
  onChangeText,
  register,
  name,
  control,
  errors,
  setError,
  isDisabled = false,
  isReadOnly = false,
  height,
  isRequired = true,
  multiline = false,
  secureTextEntry = false,
}: TypesProps) => {
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [isConfirmPasswordVisible, setConfirmIsPasswordVisible] = useState(false);
  const [isTouched, setIsTouched] = useState(false);

  return (
    <View style={styles.containerInput}>
      {label && <Text style={styles.label}>{label}</Text>}

      <Controller
        control={control}
        render={({
          field: { onChange, onBlur, value },
          fieldState: { error },
        }) => (
          <Input
            variant="outline"
            size="md"
            isDisabled={isDisabled}
            isInvalid={false}
            isReadOnly={isReadOnly}
            style={{
              height: multiline ? 80 : 40,
              width: "100%",
              outline: "none",
              borderRadius: 4,
              borderWidth: 1,
              borderColor: '#d3d3d3',
              backgroundColor: '#fff',
            }}
          >
            <InputField
              placeholder={placeholder}
              onBlur={onBlur}
              onChangeText={(value) => onChange(value)}
              value={value}
              secureTextEntry={name === "password" && !isPasswordVisible || name === "password_confirmation" && !isConfirmPasswordVisible || secureTextEntry}
              autoCapitalize={"none"}
              multiline={multiline}
              numberOfLines={multiline ? 4 : 1}
              style={{
                height: multiline ? 80 : 50,
                textAlignVertical: multiline ? "top" : "center",
                outline: "none",
                paddingVertical: 12,
                fontSize: 16,
              }}
            />
          </Input>
        )}
        name={name}
        rules={{ required: isRequired }}
      />
      {errors?.[name] && (
        <Text style={styles.errorText}>{`Ce champ est requis`}</Text>
      )}

            {/* ✅ Bouton pour afficher/masquer le mot de passe */}
            {name === "password" && (
              <TouchableOpacity
                style={styles.eyeButton}
                onPress={() => setIsPasswordVisible(!isPasswordVisible)}
              >
                <Ionicons
                  name={isPasswordVisible ? "eye-off" : "eye"}
                  size={22}
                  color="#666"
                />
              </TouchableOpacity>
            )}

            {name === "password_confirmation" && (
              <TouchableOpacity
                style={styles.eyeButton}
                onPress={() => setConfirmIsPasswordVisible(!isConfirmPasswordVisible)}
              >
                <Ionicons
                  name={isConfirmPasswordVisible ? "eye-off" : "eye"}
                  size={22}
                  color="#666"
                />
              </TouchableOpacity>
            )}
    </View>
  );
};

export default InputCustomized;

const styles = StyleSheet.create({
  containerInput: {
    gap: 10,
    fontSize: 14,
  },
  eyeButton: {
    position: "absolute",
    right: 10,
  },
  label: {
    fontSize: 14,
    color: "#262627",
    marginVertical: 1,
    fontWeight: "bold",
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginTop: 2,
  },
});
