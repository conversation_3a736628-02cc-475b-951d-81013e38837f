import ContainedButton from "@/components/ui/buttons/ContainedButton";
import { supabase } from "@/lib/supabase";
import { AppContext } from "@/state/AppContext";
import TypeELearning from "@/types/typeELearning";
import { AntDesign, Ionicons } from "@expo/vector-icons";
import {
  router,
  useFocusEffect,
  useLocalSearchParams,
  usePathname,
} from "expo-router";
import { useCallback, useContext, useEffect, useState } from "react";
import { Dimensions, ScrollView, Platform, Linking } from "react-native";
import {
  TouchableOpacity,
  View,
  FlatList,
  StyleSheet,
  Text,
  Image,
  Alert,
} from "react-native";
import { useEvent } from "expo";
import { useVideoPlayer, VideoView } from "expo-video";
import Header from "@/components/common/Header";
import WebView from "react-native-webview";

export default function OneELearning() {
  const [eLearningVideos, setELearningVideos] = useState<TypeELearning>();
  const { width } = Dimensions.get("window");
  const { user, companySelected } = useContext(AppContext);
  const [loading, setLoading] = useState(false);
  const path_name = usePathname();
  const { idELearning } = useLocalSearchParams();

  const updateELearningIsShown = async () => {
    const { data, error } = await supabase
      .from("elearnings")
      .update({
        isShown: true,
      })
      .eq("id", idELearning)
      .eq("uid_user", user?.uid);
  };

  const fetchDataELearnings = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("elearnings")
        .select()
        .eq("uid_user", user?.uid)
        .eq("id", idELearning)
        .single();

      if (error) {
        console.error('Erreur lors de la récupération des données:', error);
        return;
      }

      if (data) {
        console.log('Données eLearning récupérées:', data);
        setELearningVideos(data as TypeELearning);
      }
    } catch (error) {
      console.error('Erreur inattendue:', error);
    } finally {
      setLoading(false);
    }
  };

  const player = useVideoPlayer(eLearningVideos?.url_media as any, (player) => {
    player.loop = true;
    player.pause();
  });

  const { isPlaying } = useEvent(player, "playingChange", {
    isPlaying: player.playing,
  });

  const recordView = async () => {
    // Vérifier si l'enregistrement existe déjà
    const { data: existingRecord } = await supabase
      .from("elearnings_students")
      .select()
      .eq("elearning_id", idELearning)
      .eq("user_id", user?.uid)
      .single();

    // Si l'enregistrement n'existe pas, le créer avec upsert
    if (!existingRecord) {
      const { error } = await supabase.from("elearnings_students").upsert({
        elearning_id: idELearning,
        user_id: user?.uid,
        view_at: new Date().toISOString(),
        company_id: companySelected?.id
      }, {
        onConflict: 'elearning_id,user_id'
      });

      if (error) {
        console.error('Erreur lors de l\'enregistrement de la vue:', error);
      }
    }
  };

  const recordClick = async () => {
    await supabase.from("elearnings_students").upsert({
      elearning_id: idELearning,
      user_id: user?.uid,
      click_at: new Date().toISOString(),
    }, {
      onConflict: 'elearning_id,user_id'
    });
  };

  // Récupérer les données au chargement initial
  useEffect(() => {
    if (idELearning && user?.uid) {
      fetchDataELearnings();
    }
  }, [idELearning, user?.uid]);

  // Récupérer les données à chaque focus de l'écran
  useFocusEffect(
    useCallback(() => {
      if (idELearning && user?.uid) {
        fetchDataELearnings();
        updateELearningIsShown();
        recordView();
      }
    }, [idELearning, user?.uid])
  );

  const handleVoirPlus = async () => {
    if (eLearningVideos?.url_video) {
      router.push({
        pathname: "/content/elearning/video",
        params: {
          url: eLearningVideos.url_video,
          title: eLearningVideos.title
        }
      });
    } else {
      Alert.alert("Erreur", "Aucun lien vidéo disponible");
    }
  };

  const getYouTubeVideoId = (url: string) => {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : null;
  };

  const isYouTubeUrl = (url: string) => {
    return url?.includes('youtube.com') || url?.includes('youtu.be');
  };

  const handleYouTubePress = async (url: string) => {
    // Vérifier si l'enregistrement existe et si click_at est vide
    const { data: existingRecord } = await supabase
      .from("elearnings_students")
      .select()
      .eq("elearning_id", idELearning)
      .eq("user_id", user?.uid)
      .single();

    if (existingRecord && !existingRecord.click_at) {
      // Mettre à jour uniquement click_at si c'est vide
      const { error } = await supabase
        .from("elearnings_students")
        .update({ click_at: new Date().toISOString() })
        .eq("elearning_id", idELearning)
        .eq("user_id", user?.uid);

      if (error) {
        console.error('Erreur lors de l\'enregistrement du clic YouTube:', error);
      }
    }

    const videoId = getYouTubeVideoId(url);
    if (videoId) {
      const youtubeUrl = `vnd.youtube://${videoId}`;
      const canOpen = await Linking.canOpenURL(youtubeUrl);
      
      if (canOpen) {
        await Linking.openURL(youtubeUrl);
      } else {
        await Linking.openURL(`https://www.youtube.com/watch?v=${videoId}`);
      }
    }
  };

  const renderYouTubeVideo = () => {
    const videoId = getYouTubeVideoId(eLearningVideos?.url_video || '');
    if (!videoId) return null;

    if (Platform.OS === 'web') {
      // Sur web, on garde l'iframe
      return (
        <View style={{ maxWidth: 1200, width: '100%', alignSelf: 'center' }}>
          <iframe
            src={`https://www.youtube.com/embed/${videoId}`}
            style={{
              width: '100%',
              aspectRatio: '16/9',
              border: 'none',
              borderRadius: 10,
              minHeight: 600,
            }}
            allowFullScreen
          />
        </View>
      );
    } else {
      // Sur mobile, on affiche la miniature avec un bouton de lecture
      return (
        <TouchableOpacity
          style={styles.thumbnailContainer}
          onPress={() => handleYouTubePress(eLearningVideos?.url_video || '')}
        >
          <Image
            source={{ uri: `https://img.youtube.com/vi/${videoId}/hqdefault.jpg` }}
            style={styles.thumbnail}
          />
          <View style={styles.playButton}>
            <AntDesign name="playcircleo" size={50} color="#F99527" />
          </View>
        </TouchableOpacity>
      );
    }
  };

  useEffect(() => {
    if (isPlaying) {
      // Vérifier si l'enregistrement existe et si click_at est vide
      const recordPlayClick = async () => {
        const { data: existingRecord } = await supabase
          .from("elearnings_students")
          .select()
          .eq("elearning_id", idELearning)
          .eq("user_id", user?.uid)
          .single();

        if (existingRecord && !existingRecord.click_at) {
          // Mettre à jour uniquement click_at si c'est vide
          const { error } = await supabase
            .from("elearnings_students")
            .update({ click_at: new Date().toISOString() })
            .eq("elearning_id", idELearning)
            .eq("user_id", user?.uid);

          if (error) {
            console.error('Erreur lors de l\'enregistrement du clic lecture:', error);
          }
        }
      };

      recordPlayClick();
    }
  }, [isPlaying]);

  return (
    <View style={styles.mainContainer}>
      <Header
        onPressIcon={() => {
          player.pause();
          router.push({
            pathname: "/content/updateELearning",
            params: {
              uid_user: user?.uid,
              title: eLearningVideos?.title,
              description: eLearningVideos?.description,
              videoSource: eLearningVideos?.url_media,
              idELearning: idELearning,
              file_type: eLearningVideos?.file_type,
              url_mignature: eLearningVideos?.url_mignature,
              url_media: eLearningVideos?.url_media,
              type_profil: JSON.stringify(eLearningVideos?.type_profil || []),
              url_video: eLearningVideos?.url_video,
            },
          });
        }}
      />
      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <View style={styles.content}>
          <View style={styles.card}>
            <View style={styles.videoContainer}>
              {eLearningVideos?.url_media ? (
                isYouTubeUrl(eLearningVideos.url_media) ? (
                  renderYouTubeVideo()
                ) : (
                  <VideoView style={[styles.video, { aspectRatio: '16/9', minHeight: Platform.OS === 'web' ? 600 : undefined }]} player={player} />
                )
              ) : (
                <View style={[styles.video, { backgroundColor: '#f0f0f0', justifyContent: 'center', alignItems: 'center', aspectRatio: '16/9', minHeight: Platform.OS === 'web' ? 600 : undefined }]}>
                  <Text style={styles.emptyText}>Aucune vidéo disponible</Text>
                </View>
              )}
            </View>

            <View style={styles.overlay}>
              <Text style={styles.title}>{eLearningVideos?.title}</Text>
              <Text style={styles.description}>{eLearningVideos?.description}</Text>
            </View>
          </View>

          {eLearningVideos?.url_video && eLearningVideos?.file_type === 'pdf' && (
            <View style={styles.buttons}>
              <ContainedButton
                label="Voir plus"
                backgroundColor="#F99527"
                onPress={handleVoirPlus}
                disabled={loading}
              />
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  contentContainer: {
    flexGrow: 1,
  },
  content: {
    gap: 20,
    padding: 20,
    paddingBottom: Platform.OS === 'web' ? 40 : 20,
  },
  card: {
    borderRadius: 10,
    overflow: "hidden",
    backgroundColor: "#fff",
    elevation: 3,
    padding: 10,
    margin: 10,
    paddingBottom: 0,
  },
  videoContainer: {
    width: '100%',
    overflow: 'hidden',
    borderRadius: 10,
  },
  video: {
    width: '100%',
    aspectRatio: '16/9',
    borderRadius: 10,
    minHeight: Platform.OS === 'web' ? 600 : undefined,
  },
  overlay: {
    padding: Platform.OS === 'web' ? 20 : 10,
    marginTop: Platform.OS === 'web' ? 20 : 0,
  },
  title: {
    fontSize: 16,
    fontWeight: "bold",
    alignSelf: "center",
  },
  description: {
    fontSize: 14,
    color: "#666",
    alignSelf: "center",
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "80%",
    alignSelf: "center",
    marginBottom: 20,
  },
  thumbnailContainer: {
    width: '100%',
    aspectRatio: '16/9',
    borderRadius: 10,
    overflow: 'hidden',
    position: 'relative',
  },
  thumbnail: {
    width: '100%',
    height: '100%',
    borderRadius: 10,
  },
  playButton: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -25 }, { translateY: -25 }],
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 50,
    padding: 10,
  },
  emptyText: {
    fontSize: 14,
    color: '#525252',
    textAlign: 'center',
  },
});
