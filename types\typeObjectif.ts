import TypeCompany from "./company";
import TypeProcess from "./typeProcess";
import TypeValue from "./typeValue";

type TypeObjectif = {
  id: number;
  uid_user: string;
  designation: string;
  target: string;
  measuring_indicator: string;
  process_id: number;
  level: number;
  process: TypeProcess;
  created_at: any;
  company_id: number;
  values: TypeValue[];
  companies: TypeCompany;
};

export default TypeObjectif;
