import ActionList from './menu/itemsMenuDefault/action';
import { useContext } from 'react';
import { AppContext } from '@/state/AppContext';
import { View, ActivityIndicator, StyleSheet } from 'react-native';

export default function Action() {
  const { companySelected } = useContext(AppContext);

  if (!companySelected) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#F99527" />
      </View>
    );
  }

  return (
    <View style={{ flex: 1 }}>
      <ActionList />
    </View>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
}); 