const path = require("path");
const webpack = require('webpack'); // ✅ Utilisé pour ajouter des plugins
const createExpoWebpackConfigAsync = require("@expo/webpack-config");

module.exports = async function (env, argv) {
    require("dotenv").config();
    console.log("🚀 EXPO_ROUTER_APP_ROOT:", process.env.EXPO_ROUTER_APP_ROOT); // Debug 🔍

    const config = await createExpoWebpackConfigAsync(env, argv);

    config.resolve.alias = {
        ...config.resolve.alias,
        "react-native$": "react-native-web",
        "expo-router/app": path.resolve(__dirname, "app") // ✅ Corrige l'alias
    };

    config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false
    };
    


    return config;
};
