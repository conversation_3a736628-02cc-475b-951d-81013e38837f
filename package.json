{"name": "management-qse-mobile", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "DARK_MODE=media expo start --ios", "web": "EXPO_ROUTER_IMPORT_MODE=import expo start --web", "web-build": "expo export:web", "export": "expo export --output dist", "website": "expo export --platform web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/html-elements": "^0.4.2", "@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@gluestack-ui/actionsheet": "^0.2.46", "@gluestack-ui/button": "^1.0.8", "@gluestack-ui/checkbox": "^0.1.33", "@gluestack-ui/divider": "^0.1.10", "@gluestack-ui/icon": "^0.1.25", "@gluestack-ui/input": "^0.1.32", "@gluestack-ui/menu": "^0.2.37", "@gluestack-ui/modal": "^0.1.35", "@gluestack-ui/nativewind-utils": "^1.0.26", "@gluestack-ui/overlay": "^0.1.16", "@gluestack-ui/select": "^0.1.30", "@gluestack-ui/spinner": "^0.1.15", "@gluestack-ui/switch": "^0.1.23", "@gluestack-ui/toast": "^1.0.8", "@legendapp/motion": "^2.4.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.3.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@react-navigation/stack": "^7.1.1", "@rneui/themed": "^4.0.0-rc.8", "@supabase/storage-js": "^2.7.1", "@supabase/supabase-js": "^2.47.10", "babel-plugin-module-resolver": "^5.0.2", "dotenv": "^16.4.7", "expo": "^53.0.0", "expo-blur": "~14.1.5", "expo-constants": "~17.1.6", "expo-dev-client": "~5.2.0", "expo-document-picker": "~13.1.5", "expo-file-system": "~18.1.10", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image-manipulator": "^13.1.7", "expo-image-picker": "~16.1.4", "expo-linking": "~7.1.5", "expo-router": "~5.0.7", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.8", "expo-video": "~2.2.0", "expo-web-browser": "~14.1.6", "framer-motion": "^12.6.3", "metro-config": "^0.81.3", "metro-core": "^0.81.3", "moment": "^2.30.1", "nativewind": "^4.1.23", "openai": "^4.85.1", "pdf-lib": "^1.17.1", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.54.2", "react-native": "0.79.3", "react-native-blob-util": "^0.22.0", "react-native-css-interop": "^0.1.22", "react-native-dropdown-select-list": "^2.0.5", "react-native-element-dropdown": "^2.12.4", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-paper": "^5.13.1", "react-native-pdf": "^6.7.7", "react-native-pdf-page-image": "^0.2.1", "react-native-progress": "^5.0.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-snap-carousel": "^3.9.1", "react-native-svg": "15.11.2", "react-native-toast-message": "^2.2.1", "react-native-vector-icons": "^10.2.0", "react-native-view-shot": "^4.0.3", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "react-native-youtube-iframe": "^2.3.0", "sharp": "^0.34.2", "sonner": "^1.7.1", "tailwindcss": "^3.4.17", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/webpack-config": "^19.0.1", "@react-native-community/cli": "latest", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-test-renderer": "^18.3.0", "jest": "^29.2.1", "jest-expo": "~53.0.6", "jscodeshift": "^0.15.2", "metro": "^0.82.0", "metro-react-native-babel-preset": "^0.77.0", "react-test-renderer": "19.0.0", "typescript": "~5.8.3", "webpack": "^5.98.0", "webpack-cli": "^6.0.1"}, "overrides": {"react": "19.0.0", "react-dom": "19.0.0"}, "private": true}