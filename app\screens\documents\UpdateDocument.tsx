/*
app/screens/notifications/UpdateDocument.tsx

Formulaire pour modifier un document existant.

Informations pertinentes :

- Les informations du document sont récupérés depuis la navigation depuis le composant `DocumentDetails.tsx`.

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern`, `Supplier` et `Extern` n'ont pas accès à cette page.
*/

import React, { useContext, useEffect, useState } from "react";
import {
  Alert,
  StyleSheet,
  View,
  TextInput,
  Image,
  Text,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { supabase } from "../../../lib/supabase.native";
import { Controller, useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AntDesign } from "@expo/vector-icons";
import uploadDocumentCoverStorage from "@/lib/uploadDocumentCoverStorage";
import { useRoute, RouteProp } from "@react-navigation/native";
import { Dropdown } from "react-native-element-dropdown";
import * as DocumentPicker from "expo-document-picker";
import { useRouter } from "expo-router";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import CustomizedSelect from "@/components/ui/customizedSelect/customizedSelect";
import ContainedButton from "@/components/ui/buttons/ContainedButton";
import Toast from "react-native-toast-message";

// Données de navigation
type DocumentDetailsRouteParams = {
  document: {
    id: number;
    name: string;
    description: string;
    file: string;
    activity: string;
    company_id: number;
  };
};

type TypeDoc = {
  name: string;
  description: string;
  file: string | null;
  activity: string;
  company_id: string;
};

export default function UpdateDocument() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [file, setFile] = useState<string | null>(null);
  const [currentFile, setCurrentFile] = useState<string | null>(null);
  const [activities, setActivities] = useState([]);
  const [loadingActivities, setLoadingActivities] = useState(false);
  const [selectedActivityName, setSelectedActivityName] = useState<string | null>(null);
  const { user, companySelected } = useContext(AppContext);
  const route =
    useRoute<RouteProp<{ params: DocumentDetailsRouteParams }, "params">>();
  const { document: documentParam } = route.params;
  const [document, setDocument] = useState(
    Platform.OS === 'web' 
      ? (typeof documentParam === 'string' ? JSON.parse(documentParam) : documentParam)
      : documentParam
  );
  console.log('Document param reçu:', documentParam);
  console.log('Type du paramètre:', typeof documentParam);
  
  console.log('Document parsé:', document);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
    control,
    reset,
  } = useForm<TypeDoc>({
    defaultValues: {
      name: document?.name || "",
      description: document?.description || "",
      file: document?.file || "",
      activity: document?.activity || "",
      company_id: document?.company_id?.toString() || "",
    },
  });

  // Initialiser currentFile avec le fichier du document
  useEffect(() => {
    if (document?.file) {
      setCurrentFile(document.file);
    }
  }, []);

  // Récupère les activités de l'entreprise
  useEffect(() => {
    const fetchData = async () => {
      console.log("Début du processus de récupération...");

      try {
        const companyId = companySelected?.id;

        // Récupérer les activités associées à l'entreprise
        console.log(
          "Récupération des activités pour l'entreprise :",
          companyId
        );

        const { data: activityData, error: activityError } = await supabase
          .from("activities")
          .select("id, activity_name")
          .eq("company_id", companyId);

        if (activityError) {
          console.error(
            "Erreur lors de la récupération des activités :",
            activityError.message
          );
          return;
        }

        const formattedActivities: any = activityData.map((activity) => ({
          label: activity.activity_name,
          value: activity.id.toString(),
        }));

        setActivities(formattedActivities);
      } catch (err) {
        console.error("Erreur inattendue lors de la récupération :", err);
      } finally {
        setLoadingActivities(false);
        console.log("Fin du processus de récupération.");
      }
    };

    fetchData();
  }, [user]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern", "Supplier"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  // Enregistrer le fichier
  const handleFileSelect = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ["application/pdf"],
        copyToCacheDirectory: true,
      });

      if (result.canceled) {
        return;
      }

      const fileUri = result.assets[0].uri;
      setFile(fileUri);
      setCurrentFile(null); // Réinitialiser le fichier actuel
    } catch (error: any) {
      Toast.show({
        type: "error",
        text1: "Erreur lors de la sélection du fichier",
        text2: error.message,
      });
    }
  };

  // Supprimer le fichier actuel
  const handleRemoveCurrentFile = async () => {
    try {
      console.log('Suppression du fichier actuel');
      console.log('État avant suppression:', { currentFile, file });
      
      setCurrentFile(null);
      setFile(null);
      
      // Mettre à jour le document local
      const updatedDocument = {
        ...document,
        file: null
      };
      setDocument(updatedDocument);
      
      // Mettre à jour le formulaire
      const updatedValues = {
        ...control._formValues,
        file: null
      };
      console.log('Nouvelles valeurs du formulaire:', updatedValues);
      reset(updatedValues);

      // Mettre à jour directement dans la base de données
      const { error } = await supabase
        .from("documents")
        .update({ file: null })
        .eq("id", document.id);

      if (error) {
        console.error('Erreur lors de la suppression du fichier:', error);
        throw error;
      }

      Toast.show({
        type: "success",
        text1: "Fichier supprimé avec succès",
      });
    } catch (error: any) {
      console.error('Erreur lors de la suppression du fichier:', error);
      Toast.show({
        type: "error",
        text1: "Erreur lors de la suppression du fichier",
        text2: error.message,
      });
    }
  };

  // Valider le formulaire
  const onSubmit = async (data: TypeDoc) => {
    try {
      setLoading(true);
      console.log('Début de la soumission avec les données:', data);
      console.log('État des fichiers:', { currentFile, file });

      let fileUrl = null;
      if (file) {
        // Si un nouveau fichier a été sélectionné
        console.log('Nouveau fichier sélectionné, upload en cours...');
        const uploadResult = await uploadDocumentCoverStorage(file, user?.uid);
        if (!uploadResult) throw new Error("Erreur lors de l'upload du fichier");
        fileUrl = uploadResult.url;
        console.log('Nouveau fichier uploadé:', fileUrl);
      } else if (currentFile) {
        // Si on garde le fichier actuel
        console.log('Conservation du fichier actuel:', currentFile);
        fileUrl = currentFile;
      } else {
        console.log('Aucun fichier sélectionné ou conservé');
      }

      const updateData = {
        name: data.name,
        description: data.description,
        file: fileUrl,
        activity: data.activity,
        company_id: data.company_id,
      };

      console.log('Données de mise à jour:', updateData);

      const { error } = await supabase
        .from("documents")
        .update(updateData)
        .eq("id", document.id);

      if (error) {
        console.error('Erreur Supabase:', error);
        throw error;
      }

      console.log('Mise à jour réussie');
      Toast.show({
        type: "success",
        text1: "Document modifié avec succès",
      });

      if (Platform.OS === 'web') {
        router.back();
      } else {
        navigation.goBack();
      }
    } catch (error: any) {
      console.error('Erreur lors de la mise à jour:', error);
      Toast.show({
        type: "error",
        text1: "Erreur lors de la modification",
        text2: error.message,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    try {
      setLoading(true);
      const { error } = await supabase
        .from("documents")
        .delete()
        .eq("id", document.id);

      if (error) throw error;

      Toast.show({
        type: "success",
        text1: "Document supprimé avec succès",
      });

      if (Platform.OS === 'web') {
        router.back();
        router.back();
      } else {
        navigation.goBack();
        navigation.goBack();
      }
    } catch (error: any) {
      Toast.show({
        type: "error",
        text1: "Erreur lors de la suppression",
        text2: error.message,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.mainContainer}>
      <View style={styles.container}>
        <View style={styles.content}>
          <View style={styles.formContainer}>
            <View style={styles.inputs}>
              <InputCustomized
                label="Nom du document"
                placeholder="Nom du document"
                register={register}
                name="name"
                control={control}
                errors={errors}
                setError={setError}
              />

              <InputCustomized
                label="Description"
                placeholder="Description du document"
                register={register}
                name="description"
                control={control}
                errors={errors}
                setError={setError}
                multiline
              />

              <View style={styles.fileContainer}>
                <Text style={styles.label}>Fichier actuel</Text>
                {currentFile ? (
                  <View style={styles.currentFile}>
                    <Text style={styles.fileName}>{document?.name}</Text>
                    <Text style={styles.fileInfo}>
                      {currentFile?.endsWith(".pdf") ? "PDF" : "Image"}
                    </Text>
                    <TouchableOpacity
                      style={styles.removeFileButton}
                      onPress={handleRemoveCurrentFile}
                    >
                      <Text style={styles.removeFileButtonText}>Supprimer le fichier</Text>
                    </TouchableOpacity>
                  </View>
                ) : (
                  <Text style={styles.noFileText}>Aucun fichier</Text>
                )}
              </View>

              <View style={styles.fileContainer}>
                <Text style={styles.label}>Nouveau fichier (optionnel)</Text>
                <View style={styles.fileInput}>
                  <TextInput
                    style={styles.fileInputText}
                    value={file ? "Fichier sélectionné" : "Aucun fichier sélectionné"}
                    editable={false}
                  />
                  <TouchableOpacity
                    style={styles.fileButton}
                    onPress={handleFileSelect}
                  >
                    <Text style={styles.fileButtonText}>Sélectionner</Text>
                  </TouchableOpacity>
                </View>
              </View>

              <CustomizedSelect
                name="activity"
                label="Activité"
                register={register}
                control={control}
                errors={errors}
                setError={setError}
                data={activities}
              />
            </View>

            <View style={styles.buttons}>
              <ContainedButton
                label="Modifier"
                backgroundColor="#F99527"
                onPress={handleSubmit(onSubmit)}
                disabled={false}
              />
              <TouchableOpacity 
                style={styles.deleteButton}
                onPress={() => {
                  if (Platform.OS === 'web') {
                    if (window.confirm("Êtes-vous sûr de vouloir supprimer ce document ?")) {
                      handleDelete();
                    }
                  } else {
                    Alert.alert(
                      "Supprimer le document",
                      "Êtes-vous sûr de vouloir supprimer ce document ?",
                      [
                        {
                          text: "Annuler",
                          style: "cancel"
                        },
                        {
                          text: "Supprimer",
                          onPress: handleDelete,
                          style: "destructive"
                        }
                      ]
                    );
                  }
                }}
              >
                <Text style={styles.deleteButtonText}>Supprimer le document</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  content: {
    gap: 20,
    padding: 20,
  },
  formContainer: {
    width: "100%",
    gap: 20,
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
  fileContainer: {
    width: "100%",
    gap: 10,
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
  },
  currentFile: {
    padding: 15,
    backgroundColor: "#f5f5f5",
    borderRadius: 4,
    borderWidth: 1,
    borderColor: "#d3d3d3",
  },
  fileName: {
    fontSize: 16,
    color: "#262627",
    marginBottom: 5,
  },
  fileInfo: {
    fontSize: 14,
    color: "#666",
  },
  fileInput: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  fileInputText: {
    flex: 1,
    padding: 10,
    borderWidth: 1,
    borderColor: "#d3d3d3",
    borderRadius: 4,
    backgroundColor: "#f5f5f5",
  },
  fileButton: {
    backgroundColor: "#F99527",
    padding: 10,
    borderRadius: 4,
  },
  fileButtonText: {
    color: "#fff",
    fontWeight: "bold",
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  deleteButton: {
    backgroundColor: '#FF3B30',
    padding: 12,
    borderRadius: 7,
    alignItems: 'center',
    marginTop: 10,
  },
  deleteButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  dropdown: {
    marginVertical: 16,
    height: 50,
    backgroundColor: "white",
    borderRadius: 12,
    padding: 12,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
    ...(Platform.OS === 'web' ? {} : {
      width: '100%',
      minHeight: 50,
    }),
  },
  icon: {
    marginRight: 5,
  },
  item: {
    padding: 17,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  textItem: {
    flex: 1,
    fontSize: 16,
  },
  placeholderStyle: {
    fontSize: 16,
  },
  selectedTextStyle: {
    fontSize: 16,
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 16,
  },
  removeFileButton: {
    backgroundColor: '#FF3B30',
    padding: 8,
    borderRadius: 4,
    marginTop: 10,
    alignSelf: 'flex-start',
  },
  removeFileButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  noFileText: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
});
