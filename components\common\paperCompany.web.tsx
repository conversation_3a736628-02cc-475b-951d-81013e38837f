import React from "react";
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Platform } from "react-native";
import { Badge, BadgeText } from "../ui/badge";
import FontAwesome5 from "@expo/vector-icons/FontAwesome5";
import { motion } from "framer-motion";

type typePropPaper = {
  title?: string;
  text1?: string;
  text2?: string;
  text3?: string;
  text4?: string;
  onPress?: any;
  scrollable?: boolean;
};

const PaperCompany = ({
  title,
  text1,
  text2,
  text3,
  text4,
  onPress,
  scrollable = false,
}: typePropPaper) => {
  const renderText1 = () => {
    if (scrollable) {
      return (
        <ScrollView 
          style={styles.scrollableText}
          showsVerticalScrollIndicator={false}
        >
          <Text style={styles.text}>{text1}</Text>
        </ScrollView>
      );
    }
    return <Text style={styles.text}>{text1}</Text>;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      style={styles.container}
    >
      <TouchableOpacity style={styles.content} onPress={onPress}>
        <View style={styles.header}>
          <Text style={styles.title}>{title}</Text>
        </View>

        <View style={styles.textContainer}>
          {text1 && (
            <View style={styles.textWrapper}>
              <Text style={styles.label}>Description</Text>
              {renderText1()}
            </View>
          )}
          {text2 && (
            <View style={styles.textWrapper}>
              <Text style={styles.label}>Organisation</Text>
              <Text style={styles.text}>{text2}</Text>
            </View>
          )}
          {text3 && (
            <View style={styles.textWrapper}>
              <Text style={styles.label}>Site Web</Text>
              <Text style={styles.text}>{text3}</Text>
            </View>
          )}
          {text4 && (
            <View style={styles.textWrapper}>
              <Text style={styles.label}>Historique</Text>
              <Text style={styles.text}>{text4}</Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    </motion.div>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
    borderRadius: 12,
    boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
    margin: 10,
    overflow: "hidden",
    width: "100%",
    maxWidth: 800,
    minHeight: 200,
  },
  content: {
    padding: 20,
    width: "100%",
  },
  header: {
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#E6E6E6",
    paddingBottom: 12,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1C3144",
  },
  textContainer: {
    width: "100%",
  },
  textWrapper: {
    marginBottom: 16,
    width: "100%",
  },
  label: {
    fontSize: 14,
    fontWeight: "600",
    color: "#666",
    marginBottom: 8,
  },
  text: {
    fontSize: 16,
    color: "#333",
    lineHeight: 24,
    width: "100%",
  },
  scrollableText: {
    maxHeight: 300,
    ...(Platform.OS === 'web' ? {
      overflowY: 'auto',
      paddingRight: 8,
      '&::-webkit-scrollbar': {
        width: '6px',
      },
      '&::-webkit-scrollbar-track': {
        background: '#f1f1f1',
        borderRadius: '4px',
      },
      '&::-webkit-scrollbar-thumb': {
        background: '#888',
        borderRadius: '4px',
        '&:hover': {
          background: '#555',
        },
      },
    } : {}),
  },
});

export default PaperCompany; 