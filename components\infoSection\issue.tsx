/*
components/infoSection/issue.tsx

Composant pour afficher l'enjeu de l'entreprise.
*/

import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Platform,
} from "react-native";
import { useRouter, usePathname } from "expo-router";
import { Spinner } from "../ui/spinner";
import TypeContext from "@/types/typeContext";
import PaperCompany from "../common/paperCompany";

type typeContextsProps = {
  icon?: any;
  data: TypeContext[];
  isLanding?: boolean;
};

export default function IssuesInfoSection({
  icon,
  data,
  isLanding,
}: typeContextsProps) {
  const router = useRouter();
  const path = usePathname();

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Enjeux</Text>
        <TouchableOpacity onPress={() => router.navigate("/info/issue")}>
          {icon ? (
            icon
          ) : (
            <Text style={styles.seeMore}>Voir plus</Text>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView 
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {isLanding ? (
          <View style={styles.spinnerContainer}>
            <Spinner size="small" color="#1C3144" />
          </View>
        ) : data?.length > 0 ? (
          (path === "/Informations" || path === "/menu/itemsMenuCockpit/info"
            ? data?.slice(-2)
            : data
          ).map((item, index) => (
            <PaperCompany
              key={index}
              title="Enjeux de l'entreprise"
              text1={item.context}
              scrollable={true}
            />
          ))
        ) : (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>Aucun enjeu d'entreprise.</Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: "100%",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#E6E6E6",
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1C3144",
  },
  seeMore: {
    color: "#F99527",
    textDecorationLine: "underline",
    fontFamily: "Poppins",
    fontWeight: "bold",
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingVertical: 16,
    ...(Platform.OS === 'web' && {
      maxWidth: 1200,
      marginHorizontal: 'auto',
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
      gap: 16,
    }),
  },
  spinnerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});
