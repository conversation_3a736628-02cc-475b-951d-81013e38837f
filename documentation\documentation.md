# Documentation

Mis à jour le : **20/03/2025**

Par : **<PERSON>**

## Organisation du GitLab

### Branches GitLab

L'organisation des branches dans **GitLab** est structurée pour faciliter le développement, les tests, et le déploiement en production. Voici les principales branches utilisées :

- **`dev`** :  
  La branche principale de développement continu. Elle sert d'environnement bac à sable pour les développeurs où toutes les nouvelles fonctionnalités et corrections de bugs sont intégrées après validation initiale.

- **`pre-prod`** :  
  Cette branche est destinée à présenter les fonctionnalités au client. Une fois que les développements de la branche `dev` sont stabilisés et validés, ils sont fusionnés dans cette branche pour des démonstrations.

- **`prod`** :  
  La branche finale de production qui contient la version stable et prête à être déployée de l'application. Elle est mise à jour uniquement lorsque les changements dans `pre-prod` sont entièrement validés.

- **`feat/nom_de_fonctionnalité`** :  
  Des branches éphémères créées pour le développement d'une fonctionnalité spécifique. Ces branches sont fusionnées dans la branche `dev` une fois les développements terminés et validés.

### Bonnes pratiques

- Toujours nommer les branches fonctionnelles avec le préfixe `feat/` suivi d'une description claire de la fonctionnalité (par exemple, `feat/authentication`).
- Tester les fonctionnalités dans la branche `dev` avant de les intégrer dans `pre-prod`.
- Ne jamais effectuer de développement direct sur les branches `pre-prod` et `prod`.
- Maintenir la branche `prod` uniquement avec du code stable et prêt pour les utilisateurs finaux.

## Technologies

- ![TypeScript](https://img.shields.io/badge/typescript-%23007ACC.svg?style=for-the-badge&logo=typescript&logoColor=white)
- ![React Native](https://img.shields.io/badge/react_native-%2320232a.svg?style=for-the-badge&logo=react&logoColor=%2361DAFB)
- ![NPM](https://img.shields.io/badge/NPM-%23CB3837.svg?style=for-the-badge&logo=npm&logoColor=white)
- ![Supabase](https://img.shields.io/badge/Supabase-3ECF8E?style=for-the-badge&logo=supabase&logoColor=white)
- ![GitLab](https://img.shields.io/badge/gitlab-%23181717.svg?style=for-the-badge&logo=gitlab&logoColor=white)

## Supabase (base de données)

### Tables

#### Table `users`

Table des utilisateurs de l'application.

```sql
id: INT8 Primary key Identity Unique Non-Nullable
created_at: TIMESTAMP Non-Nullable
profile_picture: VARCHAR Nullable
status: VARCHAR Non-Nullable
email: VARCHAR Unique Non-Nullable
first_name: VARCHAR Non-Nullable
last_name: VARCHAR Non-Nullable
uid: UUID Unique Non-Nullable
profil: VARCHAR Non-Nullable
company_id: INT8
uid: UUID
uid_admin : UUID
is_active: BOOLEAN
phone_number: TEXT
```

Explications :

- `id` : ID de la table.
- `created_at` : Date de création de la colonne.
- `profile_picture` : Image de profil de l'utilisateur.
- `status` : Statut de l'utilisateur :
   -> `Admin` = Utilisateur admin/responsable
   -> `User` = Utilisateur lambda
   -> `Extern` = Auditeur externe
   -> `Intern` = Auditeur interne
   -> `Supplier` = Fournisseur.
- `email` : Adresse email.
- `first_name` : Prénom.
- `last_name` : Nom de famille.
- `uid` : ID de l'utilisateur.
- `uid_admin` : ID de l'utilisateur `Admin` qui a créé l'utilisateur.
- `profil` : ???.
- `is_active` : Compte actif ou non.
- `phone_number` : Numéro de téléphone.
- `company_id` : Entreprise à laquelle appartient l'utilisateur. Clé étrangère qui va relier cette clé étrangère à la colonne `id` (INT8) de la table `companies` de l'entreprise de l'utilisateur connecté.

Quand un utilisateur est créé il est également ajouté à la table de jointure `company_users`.

#### Table `companies`

```shell
id: INT8 Primary key Identity Unique Non-Nullable
uid_user: UUID Non-Nullable
company_name: VARCHAR Non-Nullable
users: ARRAY
created_at: TIMESTAMP Non-Nullable
```

Explications :

- `id` : ID de la table.
- `created_at` : Date de création de la colonne.
- `uid_user` : ID de l'utilisateur qui a créé l'entreprise. Ce champ fait la liaison avec la table `users` -> Tableau de UUID.
- `company_name` : Nom de l'entreprise.

#### Table `admin_users`

```shell
id: INT8 Primary key Identity Non-Nullable
created_at: TIMESTAMP Non-Nullable
uid: UUID Unique Non-Nullable
uid_admin: UUID Non-Nullable
first_name: VARCHAR Non-Nullable
last_name: VARCHAR Non-Nullable
is_active: VARCHAR Non-Nullable
profile_picture: VARCHAR Nullable
email: VARCHAR Non-Nullable
```

Explications :

- `id` : ID de la table.
- `created_at` : Date de création de la colonne.
- `uid_admin` : ???.
- `first_name` : Prénom.
- `last_name` : Nom de famille.
- `is_active` : ???.
- `profile_picture` : Image de profil de l'utilisateur.
- `email` : Adresse email.

#### Table `activities`

```sql
id: INT8 Primary key Identity Non-Nullable
company_id: INT8 Non-Nullable
process_id: INT8 Non-Nullable
uid_admin: INT8 Non-Nullable
activity_name: VARCHAR Non-Nullable
description: VARCHAR Non-Nullable
inputs: VARCHAR Non-Nullable
outputs: VARCHAR Nullable
created_at: TIMESTAMP Non-Nullable
```

Explications :

- `id` : ID de la table.
- `company_id` : `id` de l'entreprise à laquelle appartient l'activité.
- `process_id` : `id` du processus associé à l'activité.
- `uid_admin` (clé-étrangère) : `uid` de l'utilisateur `Admin` qui a créé l'activité.
- `activity_name` : Nom de l'activité.
- `description` : Description.
- `inputs` : Entrants.
- `outputs` : Sortants.
- `created_at` : Date de création de la colonne.

#### Table `objectifs`

```shell
id: INT8 Primary key Identity Unique Non-Nullable
uid_user: UUID Non-Nullable
designation: VARCHAR Non-Nullable
target: VARCHAR Non-Nullable
measuring_indicator: VARCHAR Nullable
process_indicator: VARCHAR Nullable
created_at: TIMESTAMP Non-Nullable
```

Explications :

- `id` : ID de la table.
- `uid_user` : ???.
- `designation` : ???.
- `target` : ???.
- `measuring_indicator` : ???.
- `process_indicator` : ???.
- `created_at` : Date de création de la colonne.

#### Table `process`

```sql
id: INT8 Primary key Identity Unique Non-Nullable
uid_admin: UUID Non-Nullable
process_pilot: UUID
company_id: INT8 Non-Nullable
activity_id: INT8 Non-Nullable
process_name: VARCHAR Non-Nullable
profil_picture: VARCHAR Non-Nullable
created_at: TIMESTAMP Non-Nullable
```

Explications :

- `id` : ID de la table.
- `uid_admin` (clé-étrangère) : `uid` de l'utilisateur `Admin` qui a créé le processus.
- `process_pilot` (clé-étrangère) : `uid` de l'utilisateur `Admin` qui a créé le processus.
- `company_id` : `id` de l'entreprise à laquelle appartient l'activité.
- `activity_id` : `id` du l'activité associé au processus.
- `process_name` : Nom du processus.
- `profil_picture` : Image
- `created_at` : Date de création de la colonne.

#### Table `flash_qse`

```shell
created_at : TIMESTAMP
name : VARCHAR
context : VARCHAR
cover : VARCHAR
causes : VARCHAR
actions : VARCHAR
uid_company : INT8
```

Explications :

- `date` : Date de création.
- `name` : Nom du Flash QSE.
- `context` : Ce qu'il c'est passé.
- `cover` : Image du Flash QSE.
- `causes` : Les causes.
- `actions` : Les actions.
- `uid_company` : Entreprise à laquelle appartient le Flash QSE. Clé étrangère qui va relier cette clé étrangère au champ `id` (INT8) de la table `companies` de l'entreprise de l'utilisateur connecté.

#### Table `documents`

```shell
created_at : TIMESTAMP
name : VARCHAR
description : VARCHAR
file : VARCHAR
activity : VARCHAR
company_id : INT8
```

Explications :

- `date` : Date de création.
- `name` : Nom du Flash QSE.
- `description` : Description.
- `file` : Image du Flash QSE.
- `activity` : Les activités de de l'entreprise. Sur la base de données Supabase il y a une table `activities`. La table `activities` a une colonne `company_id` qui correspond à l'id de l'entreprise. On récupère toutes les activités pour les afficher dans le dropdown.
- `company_id` : Entreprise à laquelle appartient le document. Clé étrangère qui va relier cette clé étrangère au champ `id` (INT8) de la table `companies` de l'entreprise de l'utilisateur connecté.

#### Table `actions`

```shell
created_at : TIMESTAMP
name : VARCHAR
date : TIMESTAMP
type : VARCHAR
status : VARCHAR
company_id : INT8
```

Explications :

- `created_at` -> Date de création (automatique).
- `name` -> Nom du Flash QSE.
- `date` -> Date d'échéance attribuée à laction. Ne pas confondre avec la date de création.
- `administrator` -> Responsable de l'action.
- `process` -> Processus de l'action. On récupère les processus de l'entreprise dans la table `process`. Les processus ont une colonne `company_id` avec l'`id` de l'enteprise de l'utilisateur connecté.
- `event` -> Évènement de l'action. On récupère les événements de l'entreprise dans la table `events`. Les événements ont une colonne `company_id` avec l'`id` de l'enteprise de l'utilisateur connecté.
- `type` ->  Type d'action :
   -> "immediate" -> Action immédiate
   -> "corrective" -> Action correctif.
- `status` ->  Type de statut :
   -> "achieved" -> Statut réalisé.
   -> "not_achieved" -> Statut non réalisé.
- `company_id` : Entreprise à laquelle appartient le document. Clé étrangère qui va relier cette clé étrangère à la colonne `id` (INT8) de la table `companies` de l'entreprise de l'utilisateur connecté.

**Affichage des actions**
Le formulaire de création d'une action permet d'ajouter une date. En fonction de cette date (`date`) et du statut de l'action (`status`) nous aurons les conditions suivantes :

- Actions (validées) :
   -> `status` : `achieved`
- Actions en cours :
   -> `status` : `not_achieved`
   -> `date` : `date` < date du jour
- Actions en retard :
   -> `status` : `not_achieved`
   -> `date` : `date` > date du jour

#### Table `events`

```sql
CREATE TABLE IF NOT EXISTS events (
    id BIGSERIAL PRIMARY KEY,  -- Ajout de BIGSERIAL pour auto-incrémentation
    administrator UUID NOT NULL REFERENCES users(uid) ON DELETE CASCADE,
    process INT8 NOT NULL REFERENCES process(id) ON DELETE CASCADE,
    wording VARCHAR NOT NULL,
    uid_admin UUID NOT NULL REFERENCES users(uid) ON DELETE CASCADE,
    company_id INT8 NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    file TEXT[] NOT NULL,  -- Stocke plusieurs images sous forme de tableau
    type TEXT NOT NULL,
    causes VARCHAR NOT NULL,
    date TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT now() NOT NULL
);
```

Explications :

- `administrator` : UUID faisant référence à l'utilisateur responsable de l'événement.
- `process` : Identifiant (INT8) du processus associé à l'événement, provenant de la table process.
- `uid_admin` : UUID de l'administrateur ayant créé l'événement.
- `company_id` : Identifiant (INT8) de l'entreprise associée à l'événement.
- `wording` : Libellé ou nom de l'événement (VARCHAR).
- `file` : Tableau de chaînes de caractères stockant les URL des images associées à l'événement.
- `type` : Type de l'événement (TEXT).
- `causes` : Analyse des causes de l'événement (VARCHAR).
- `date` : Date attribuée à l'événement (TIMESTAMP).
- `created_at` : Date de création de l'enregistrement (TIMESTAMP), généralement définie automatiquement lors de l'insertion.

#### Table `audits`

```sql
created_at : TIMESTAMP
name : VARCHAR
date : TIMESTAMP
field : VARCHAR
type : VARCHAR
company_id : INT8 FOREIGN KEY
```

Explications :

- `created_at` -> Date de création (automatique).
- `name` -> Nom.
- `date` -> Date attribuée. Ne pas confondre avec la date de création.
- `field` -> Champ.
- `type` ->  Types :
   -> `validated` -> Validé.
   -> `in_progress` -> En cours.
   -> `invalid` -> En relecture.
- `company_id` : Entreprise à laquelle appartient le document. Clé étrangère qui va relier cette clé étrangère à la colonne `id` (INT8) de la table `companies` de l'entreprise de l'utilisateur connecté.

#### Table `observations`

```sql
created_at : TIMESTAMP
name : VARCHAR
requirements : VARCHAR
type : VARCHAR
risk : INT8 FOREIGN KEY
process : INT8 FOREIGN KEY
company_id : INT8 FOREIGN KEY
```

Explications :

- `created_at` -> Date de création (automatique).
- `name` -> Nom.
- `requirements` -> Exigences.
- `conclusion` -> Conclusion.
- `risk` -> Risque.
- `type` ->  Types :
   -> `non_compliant` -> Constat de non-conformité.
   -> `sensitive_point` -> Point sensible.
   -> `progress` -> Piste de progrès.
   -> `note` -> Note.
- `process` -> Processus. On récupère les processus de l'entreprise dans la table `process`. Les processus ont une colonne `company_id` avec l'`id` de l'enteprise de l'utilisateur connecté.
- `company_id` : Entreprise à laquelle appartient le document. Clé étrangère qui va relier cette clé étrangère à la colonne `id` (INT8) de la table `companies` de l'entreprise de l'utilisateur connecté.

#### Table `contexts`

Contexte de l'entreprise.

```sql
history : VARCHAR
context : VARCHAR
organisation : VARCHAR
website : VARCHAR
uid_admin : UUID
company_id : INT8 FOREIGN KEY
created_at : TIMESTAMP
```

SQL :

```sql
CREATE TABLE contexts (
    id SERIAL PRIMARY KEY,
    history TEXT NOT NULL,
    context TEXT NOT NULL,
    organisation TEXT NOT NULL,
    website VARCHAR(255),
    company_id INT8 NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT now()
);
```

Explications :

- `history` -> Histoire de l'entreprise.
- `context` -> Contexte.
- `organisation` -> Organisation.
- `website` -> Site internet.
- `uid_admin` -> Clé étrangère qui va relier la colonne `uid` de la table `users`.
- `company_id` : Entreprise à laquelle appartient le document. Clé étrangère qui va relier cette clé étrangère à la colonne `id` (INT8) de la table `companies` de l'entreprise de l'utilisateur connecté.
- `created_at` -> Date de création (automatique).

#### Table `issues`

Enjeux de l'entreprise.

```sql
context : VARCHAR
uid_admin : UUID (AUTH)
company_id : INT8 FOREIGN KEY
created_at : TIMESTAMP
```

Explications :

- `context` -> Enjeu.
- `uid_admin` -> Clé étrangère qui va relier la colonne `uid` de la table `users`.
- `company_id` : Entreprise à laquelle appartient le document. Clé étrangère qui va relier cette clé étrangère à la colonne `id` (INT8) de la table `companies` de l'entreprise de l'utilisateur connecté.
- `created_at` -> Date de création (automatique).

#### Table `policy`

Politique de l'entreprise.

```sql
context : VARCHAR
uid_admin : UUID (AUTH)
company_id : INT8 FOREIGN KEY
created_at : TIMESTAMP
```

Explications :

- `context` -> Politique.
- `uid_admin` -> Clé étrangère qui va relier la colonne `uid` de la table `users`.
- `company_id` : Entreprise à laquelle appartient le document. Clé étrangère qui va relier cette clé étrangère à la colonne `id` (INT8) de la table `companies` de l'entreprise de l'utilisateur connecté.
- `created_at` -> Date de création (automatique).

#### Table `suppliers`

Fournisseurs de l'entreprise.
La table `suppliers` a une table de jointure `company_suppliers` qui regroupe la liste des fournisseurs de l'entreprise.

```sql
CREATE TABLE suppliers (
  id BIGSERIAL PRIMARY KEY,  -- Identifiant unique auto-incrémenté
  uid UUID NOT NULL DEFAULT gen_random_uuid(),  -- Identifiant unique du fournisseur (UUID)
  company_id BIGINT NOT NULL REFERENCES companies (id) ON DELETE CASCADE,
  company_name VARCHAR NOT NULL,
  first_name VARCHAR NOT NULL,
  last_name VARCHAR NOT NULL,
  phone_number VARCHAR NOT NULL,
  status VARCHAR NOT NULL CHECK (status IN ('good', 'regular', 'bad')), -- Contrainte sur les statuts
  created_at TIMESTAMP DEFAULT now(),
  UNIQUE (uid) -- Ajout de la contrainte UNIQUE sur `uid`
);
```

Explications :

- `id` : Clé primaire auto-incrémentée (BIGSERIAL).
- `uid` : Identifiant unique du fournisseur (UUID, généré automatiquement).
- `company_name` -> Nom de l'entreprise du fournisseur.
- `last_name` -> Nom du fournisseur.
- `first_name` -> Prénom du fournisseur.
- `phone_number` -> Numéro de téléphone du fournisseur.
- `status` -> Status du fournisseur :
  - `good` : Bon
  - `regular` : Moyen
  - `bad` : Mauvais
- `company_id` : Entreprise à laquelle appartient le document. Clé étrangère qui va relier cette clé étrangère à la colonne `id` (INT8) de la table `companies` de l'entreprise de l'utilisateur connecté.
- `created_at` -> Date de création (automatique).

#### Table `administrators`

Cette table (intermédiaire) stocke les administrateurs qui peuvent être soit des utilisateurs (`users`), soit des fournisseurs (`suppliers`). Elle permet de centraliser les administrateurs pour gérer les références à un seul endroit.

```sql
CREATE TABLE administrators (
    id BIGSERIAL PRIMARY KEY,
    administrator_uuid UUID UNIQUE NOT NULL,
    administrator_type VARCHAR(10) NOT NULL CHECK (administrator_type IN ('user', 'supplier'))
);
```

Explications :

- `id` : Clé primaire auto-incrémentée.
- `administrator_uuid` : Identifiant unique de l'administrateur (UUID). Peut provenir de la table users ou suppliers.
- `administrator_type` : Indique si l'administrateur est un user ou un supplier.

Cette table est mise à jour automatiquement via des triggers lors de l'ajout d'un utilisateur (`user`) ou d'un fournisseur (`supplier`).

**Triggers existants :**

Trigger pour `users`

```sql
CREATE OR REPLACE FUNCTION insert_user_into_administrators()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO administrators (administrator_uuid, administrator_type)
  VALUES (NEW.uid, 'user')
  ON CONFLICT (administrator_uuid) DO NOTHING;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_insert_user
AFTER INSERT ON users
FOR EACH ROW EXECUTE FUNCTION insert_user_into_administrators();
```

Trigger pour `suppliers`

```sql
CREATE OR REPLACE FUNCTION insert_supplier_into_administrators()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO administrators (administrator_uuid, administrator_type)
  VALUES (NEW.uid, 'supplier')
  ON CONFLICT (administrator_uuid) DO NOTHING;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_insert_supplier
AFTER INSERT ON suppliers
FOR EACH ROW EXECUTE FUNCTION insert_supplier_into_administrators();
```

### Tables de jointure

Les tables de jointures fonctionnent comme des "liste de".
Exemple : La table `company_users` correspond à la "liste de" des utilisateurs de l'entreprise. Nous utilisons des table de jointure pour limiter les appels en base de données.

#### Table `action_administrators`

Relie une action à un ou plusieurs responsables/administrateurs.
Cette table établit une relation **many-to-many** entre `actions` et `users` (responsables/administrateurs).

```sql
create table action_administrators (
  id bigint primary key generated always as identity,
  action_id bigint not null references actions (id) on delete cascade,
  user_id uuid not null references users (uid) on delete cascade,
  created_at timestamp not null default now()
);
```

- `action_id` : Référence une action dans la table `actions`.
- `user_id` : Référence un utilisateur (administrateur) dans la table `users`.

#### Table `action_processes`

Relie une action à un ou plusieurs processus.
Cette table établit une relation **many-to-many** entre `actions` et `process`.

```sql
create table action_processes (
  id bigint primary key generated always as identity,
  action_id bigint not null references actions (id) on delete cascade,
  process_id bigint not null references process (id) on delete cascade,
  created_at timestamp not null default now()
);
```

- `action_id` : Référence une action dans la table `actions`.
- `process_id` : Référence un processus dans la table `process`.

#### Table `action_events`

Relie une action à un ou plusieurs événements.
Cette table établit une relation **many-to-many** entre `actions` et `events`.

```sql
create table action_events (
  id bigint primary key generated always as identity,
  action_id bigint not null references actions (id) on delete cascade,
  event_id bigint not null references events (id) on delete cascade,
  created_at timestamp not null default now()
);
```

- `action_id` : Référence une action dans la table `actions`.
- `event_id` : Référence un événement dans la table `events`.

#### Table `company_users`

Gérer la relation entre les entreprises et les utilisateurs.

```sql
create table company_users (
  id bigint primary key generated always as identity,
  company_id bigint not null references companies (id) on delete cascade,
  user_id uuid not null references users (uid) on delete cascade,
  created_at timestamp not null default now(),
  unique (company_id, user_id) -- Empêche les doublons
);
```

- `company_id` : Référence une entreprise dans la table companies.
- `user_id` : Référence un utilisateur dans la table users.
- `created_at` : Date d'ajout de l'utilisateur à l'entreprise.

#### Table `audit_observations`

Gérer la relation entre les audits et les constats.

```sql
create table audit_observations (
  id bigint primary key generated always as identity,
  audit_id bigint not null references audits (id) on delete cascade,
  observation_id bigint not null references observations (id) on delete cascade,
  created_at timestamp not null default now(),
  unique (audit_id, observation_id) -- Empêche les doublons
);
```

Explications :

- `id`: Clé primaire auto-incrémentée.
- `audit_id`: Référence un audit dans la table `audits`.
- `observation_id`: Référence un constat dans la table `observations`.
- `created_at`: Date de création de la relation.
- `unique` (audit_id, observation_id) : Empêche l'ajout de doublons.

#### Table `event_actions`

Gérer la relation entre les événements et les actions.

```sql
id: INT8 Primary key Identity Unique Non-Nullable
wording_action: TEXT Non-Nullable
action_id: INT8
event_id: INT8
process_id: INT8
company_id: INT8
type: VARCHAR
status: VARCHAR
uid_user: UUID Unique Non-Nullable
administrator: UUID Unique Non-Nullable
date: TIMESTAMP Non-Nullable
created_at: TIMESTAMP Non-Nullable
```

Explications :

- `id`: Clé primaire auto-incrémentée.
- `wording_action`: Nom.
- `action_id`: Référence à une action dans la table `actions`.
- `event_id`: Référence à un événement dans la table `events`.
- `process_id`: Référence à un processus dans la table `process`.
- `company_id`: Référence à l'entreprise de l'utilisateur connecté dans la table `companies`.
- `type` ->  Type d'action :
   -> "immediate" -> Action immédiate
   -> "corrective" -> Action correctif.
- `uid_user`: UUID d'un utilisateur.
- `status` ->  Type de statut :
   -> "achieved" -> Statut réalisé.
   -> "not_achieved" -> Statut non réalisé.
- `administrator`: UUID d'un utilisateur admin.
- `date`: Date attribuée à l'action.
- `created_at`: Date de création de la relation.

#### Table `company_suppliers`

```sql
CREATE TABLE company_suppliers (
  id BIGSERIAL PRIMARY KEY, -- Identifiant unique auto-incrémenté
  company_id BIGINT NOT NULL REFERENCES companies (id) ON DELETE CASCADE,
  supplier_id UUID NOT NULL REFERENCES suppliers (uid) ON DELETE CASCADE, -- `uid` est UNIQUE
  created_at TIMESTAMP DEFAULT now(),
  UNIQUE (company_id, supplier_id) -- Empêche un fournisseur d’être ajouté plusieurs fois à la même entreprise
);
```

Explications :

- `id` : Identifiant unique de la table (auto-incrémenté).
- `company_id` : Référence à une entreprise dans companies, suppression en cascade.
- `supplier_id` : Référence au uid du fournisseur dans suppliers, suppression en cascade.
- `created_at` : Date et heure d’ajout à l’entreprise, valeur par défaut now().
- `unique` (company_id, supplier_id) : Empêche un fournisseur d’être ajouté plusieurs fois à la même entreprise.

#### - Table `event_administrators`

Cette table établit une relation many-to-many entre les tables `events` et `administrators`. Elle permet de gérer les événements ayant plusieurs administrateurs qui peuvent être des utilisateurs de type `users` ou `suppliers`.

```sql
CREATE TABLE event_administrators (
    id BIGSERIAL PRIMARY KEY,
    event_id BIGINT NOT NULL REFERENCES events(id) ON DELETE CASCADE,
    administrator_uuid UUID NOT NULL REFERENCES administrators(administrator_uuid) ON DELETE CASCADE,
    administrator_type VARCHAR(10) NOT NULL CHECK (administrator_type IN ('user', 'supplier')),
    UNIQUE (event_id, administrator_uuid, administrator_type)
);
```

Explications :

- `id` : Clé primaire auto-incrémentée.
- `event_id` : Référence un événement dans la table events.
- `administrator_uuid` : UUID de l'administrateur, qui peut être un user ou un supplier.
- `administrator_type` : Indique si l'administrateur est un user ou un supplier.
- `UNIQUE` (event_id, administrator_uuid, administrator_type) : Empêche un administrateur d’être ajouté plusieurs fois au même événement.

Cette table permet de lier un événement à un ou plusieurs administrateurs, indépendamment du fait qu'ils soient utilisateurs ou fournisseurs.

## Fonctionnalités

### Rôles et accès

#### 🔹 Responsable QSE

- **Base de données** : `status : Admin`
- **Navigation** : Admin (Cockpit) + Utilisateur (Espace utilisateur)
- **Accès** : Full Access (Tout voir, tout modifier, tout supprimer)

#### 🔹 Auditeur Externe

- **Base de données** : `status : Extern`
- **Navigation** : Admin (Cockpit) + Utilisateur (Espace utilisateur)
- **Accès** : Lecture seule sur tout
- **Restrictions** : Aucune modification/création/suppression

#### 🔹 Auditeur Interne

- **Base de données** : `status : Intern`
- **Navigation** : Utilisateur (Espace utilisateur) + Admin (Cockpit)
- **Accès** : Lecture seule sur le cockpit
   -> ✅ Peut Créer un audit
   -> ✅ Peut Créer un constat
   -> ✅ Peut Conclure un audit
- **Restrictions** : Ne peut modifier ou supprimer que ce qu'il crée

#### 🔹 Clients

- **Base de données** : `status : User`
- **Navigation** : Utilisateur (Espace utilisateur)
- **Accès** : Lecture seule sur tout (comme un auditeur externe)
- **Restrictions** : Aucune modification/création/suppression

#### 🔹 Fournisseurs

- **Base de données** : `status : Supplier`
- **Navigation** : Utilisateur (Espace utilisateur), pas d'accès à l'espace Admin (Cockpit)
- **Accès** :
   -> ✅ Peut Créer des actions (mais pas des events)
   -> ✅ Peut Modifier ses propres actions
   -> ✅ Accès Event + Action uniquement
- **Restriction** :
   -> ❌ NE PEUT PAS supprimer ses propres actions
   -> ❌ Pas d’accès aux documents (section Documents grisée)

### Flashs QSE

Les Flashs QSE permettent de documenter des incidents ou des événements liés à la qualité, sécurité, et environnement (QSE) au sein d'une entreprise. Ils sont rattachés à une entreprise spécifique et peuvent être consultés, créés, modifiés ou supprimés.

#### Fonctionnalités principales

1. **Liste des Flashs QSE** :
   - Les Flashs QSE sont affichés sous forme de liste avec une image de couverture, un titre et une description.
   - La liste est filtrable par nom via une barre de recherche dynamique.
   - Chaque Flash QSE est cliquable pour afficher ses détails.

2. **Création d’un Flash QSE** :
   - Un formulaire permet de créer un Flash QSE pour l'entreprise associée à l'utilisateur connecté.
   - Les champs requis incluent :
     - **Nom** : Titre du Flash.
     - **Contexte** : Description de l'incident.
     - **Causes** : Causes identifiées.
     - **Actions** : Actions entreprises.
     - **Image** : Une photo pour illustrer le Flash (uploadée sur Supabase Storage).
   - Une alerte de succès s’affiche après la création réussie.

3. **Modification d’un Flash QSE** :
   - Un formulaire similaire à celui de création permet de modifier un Flash QSE existant.
   - Les valeurs initiales des champs sont préremplies avec les données existantes.
   - Une image préexistante peut être remplacée ou conservée.
   - Seules les données modifiées sont mises à jour dans la base de données.

4. **Détails d’un Flash QSE** :
   - Chaque Flash QSE affiche ses informations détaillées :
     - Image de couverture.
     - Nom, contexte, causes et actions.
   - Depuis cette vue, un utilisateur peut accéder à l'interface de modification.

#### Modèle de données `flash_qse`

La table `flash_qse` contient les informations suivantes :

- **`id`** *(INT8, PK)* : Identifiant unique.
- **`name`** *(VARCHAR)* : Nom du Flash QSE.
- **`context`** *(VARCHAR)* : Description de l’incident.
- **`causes`** *(VARCHAR)* : Causes identifiées.
- **`actions`** *(VARCHAR)* : Actions entreprises.
- **`cover`** *(VARCHAR)* : URL de l'image de couverture stockée sur Supabase.
- **`uid_company`** *(INT8, FK)* : Référence à l’entreprise associée.
- **`created_at`** *(TIMESTAMP)* : Date de création.

#### Processus d'implémentation

1. **Composants React Native** :
   - **`FlashQSEList.tsx`** : Liste des Flashs QSE avec recherche et navigation vers les détails.
   - **`FlashQSEDetails.tsx`** : Affichage détaillé d’un Flash QSE.
   - **`CreateFlashQSE.tsx`** : Formulaire pour créer un nouveau Flash QSE.
   - **`UpdateFlashQSE.tsx`** : Formulaire pour modifier un Flash QSE existant.

2. **Gestion des données via Supabase** :
   - Les Flashs QSE sont récupérés via des requêtes SQL sur la table `flash_qse`.
   - Les images sont uploadées sur Supabase Storage, et les URL publiques sont stockées dans la base de données.

3. **Navigation et paramètres** :
   - Les informations d’un Flash QSE sont transmises entre les composants via les paramètres de navigation.

#### Cas d’utilisation

- **Documentation des incidents** : Les entreprises peuvent centraliser les événements liés à la qualité, la sécurité et l'environnement.
- **Suivi des actions correctives** : Les causes et actions entreprises sont enregistrées pour un suivi efficace.
- **Partage interne** : Les Flashs QSE peuvent être consultés par tous les membres de l’entreprise pour sensibiliser ou prévenir des incidents similaires.

### Documents

Les documents permettent de gérer et centraliser les fichiers importants liés à l'activité d'une entreprise. Ces fichiers sont rattachés à une entreprise spécifique et peuvent être consultés, créés, modifiés ou supprimés.

#### Fonctionnalités principales

1. **Liste des documents** :
   - Les documents sont affichés sous forme de liste avec un titre, une description, et une activité associée.
   - Chaque document est cliquable pour accéder à ses détails.
   - Une barre de recherche dynamique permet de filtrer les documents par nom.

2. **Création d’un document** :
   - Un formulaire permet d’ajouter un document pour l'entreprise associée à l'utilisateur connecté.
   - Les champs requis incluent :
     - **Nom** : Titre du document.
     - **Description** : Brève explication du contenu du document.
     - **Activité** : Activité à laquelle le document est lié.
     - **Fichier** : Le fichier à importer (uploadé sur Supabase Storage).
   - Une alerte de succès s’affiche après la création réussie.

3. **Modification d’un document** :
   - Un formulaire similaire à celui de création permet de modifier un document existant.
   - Les valeurs initiales des champs sont préremplies avec les données existantes.
   - Le fichier préexistant peut être remplacé ou conservé.
   - Seules les données modifiées sont mises à jour dans la base de données.

4. **Détails d’un document** :
   - Chaque document affiche ses informations détaillées :
     - Fichier lié (aperçu ou téléchargement).
     - Nom, description et activité associée.
   - Depuis cette vue, un utilisateur peut accéder à l'interface de modification.

#### Modèle de données `documents`

La table `documents` contient les informations suivantes :

- **`id`** *(INT8, PK)* : Identifiant unique.
- **`name`** *(VARCHAR)* : Nom du document.
- **`description`** *(VARCHAR)* : Brève description du contenu.
- **`file`** *(VARCHAR)* : URL du fichier stocké sur Supabase Storage.
- **`activity`** *(VARCHAR)* : Activité associée au document.
- **`company_id`** *(INT8, FK)* : Référence à l’entreprise associée.
- **`created_at`** *(TIMESTAMP)* : Date de création.

#### Processus d'implémentation

1. **Composants React Native** :
   - **`DocumentList.tsx`** : Liste des documents avec recherche et navigation vers les détails.
   - **`DocumentDetails.tsx`** : Affichage détaillé d’un document.
   - **`CreateDocument.tsx`** : Formulaire pour créer un nouveau document.
   - **`UpdateDocument.tsx`** : Formulaire pour modifier un document existant.

2. **Gestion des données via Supabase** :
   - Les documents sont récupérés via des requêtes SQL sur la table `documents`.
   - Les fichiers sont uploadés sur Supabase Storage, et les URL publiques sont stockées dans la base de données.

3. **Navigation et paramètres** :
   - Les informations d’un document sont transmises entre les composants via les paramètres de navigation.

#### Cas d’utilisation

- **Archivage** : Les entreprises peuvent centraliser leurs documents pour une gestion simplifiée et un accès rapide.
- **Collaboration interne** : Les membres de l'entreprise peuvent accéder aux documents partagés en fonction des activités.
- **Gestion documentaire** : Suivi des fichiers liés aux différentes activités pour garantir une organisation optimale.

### Actions

Les **Actions** permettent de créer, gérer et centraliser les tâches ou objectifs liés à l'activité d'une entreprise. Une action est rattachée à une entreprise spécifique et peut inclure des informations liées à ses responsables, processus et événements.

#### Fonctionnalités principales

1. **Création d'une action** :
   - Un formulaire permet de créer une nouvelle action pour l'entreprise associée à l'utilisateur connecté.
   - Les champs requis incluent :
     - **Nom** : Libellé de l'action.
     - **Date** : Date attribuée à l'action.
     - **Responsable** : Utilisateur en charge de l'action (sélectionné parmi les membres de l'entreprise).
     - **Processus** : Processus auquel l'action est liée (récupéré depuis la table `process`).
     - **Événement** : Événement associé à l'action (récupéré depuis la table `events`).
     - **Type** : Catégorie de l'action, parmi les choix suivants :
       - Immédiate
       - Corrective
     - **Statut** : Statut de l'action :
       - `achieved` → Action réalisée.
       - `not_achieved` → Action non réalisée.

2. **Gestion des données** :
   - Les actions créées sont enregistrées dans la table `actions` avec les informations suivantes :
     - **Nom**, **date**, **type**, **statut** et **entreprise associée**.
   - Les relations entre les actions et leurs responsables, processus, et événements sont établies via des tables de jointures :
     - **`action_administrators`** : Lien avec les responsables.
     - **`action_processes`** : Lien avec les processus.
     - **`action_events`** : Lien avec les événements.

3. **Confirmation et navigation** :
   - Une alerte s'affiche en cas de succès ou d'erreur lors de la création de l'action.
   - Une fois l'action créée avec succès, l'utilisateur est redirigé vers la liste des actions pour visualiser la nouvelle entrée.

#### Affichage des actions

Les actions sont affichées selon leur **statut** et leur **date d'échéance**. Elles sont regroupées dans trois catégories principales :

- **Actions validées :**
  - Critère : `status = achieved`.
  - Correspond aux actions finalisées avec succès.

- **Actions en cours :**
  - Critère : `status = not_achieved` **et** `date` > date du jour.
  - Correspond aux actions non réalisées, mais encore dans le délai prévu.

- **Actions en retard :**
  - Critère : `status = not_achieved` **et** `date` < date du jour.
  - Correspond aux actions non réalisées dont la date d'échéance est dépassée.

Chaque catégorie affiche un maximum de 2 actions, avec la possibilité de consulter la liste complète via un bouton "Voir plus".

#### Modèle de données `actions`

La table `actions` contient les informations suivantes :

- **`id`** *(INT8, PK)* : Identifiant unique.
- **`name`** *(VARCHAR)* : Nom de l'action.
- **`date`** *(TIMESTAMP)* : Date attribuée à l'action.
- **`type`** *(VARCHAR)* : Type d'action (`immédiate`, `corrective`).
- **`status`** *(VARCHAR)* : Statut de l'action (`achieved`, `not_achieved`).
- **`company_id`** *(INT8, FK)* : Référence à l'entreprise associée.
- **`created_at`** *(TIMESTAMP)* : Date de création.

#### Processus d'implémentation

1. **Composant React Native** :
   - **`CreateAction.tsx`** : Formulaire permettant la création d'une action, incluant la gestion des relations avec les utilisateurs, processus, et événements.
   - **`ActionList.tsx`** : Liste des actions regroupées par statut et date.
   - **`RegularActionList.tsx`** : Liste des actions validées.
   - **`InProgressActionList.tsx`** : Liste des actions en cours.
   - **`LateActionList.tsx`** : Liste des actions en retard.
   - **`ActionDetails.tsx`** : Détails d'une action spécifique.
   - **`UpdateAction.tsx`** : Interface de modification des actions existantes.

2. **Gestion des données via Supabase** :
   - Les données des tables `company_users`, `process`, et `events` sont récupérées pour alimenter les menus déroulants.
   - Les actions sont insérées dans la table `actions` et leurs relations sont enregistrées dans les tables de jointures correspondantes.

3. **Navigation et validation** :
   - Validation des champs avant l'envoi.
   - Retour automatique vers la liste des actions après création ou modification.

## Installation

### Installer les dépendances

```shell
npm install
```

### Installation et configuration du SDK Supabase

Créer un fichier `.env` à la racine du projet (au même niveau que le fichier `package.json` par exemple) et ajouter les configurations **Supabase**

```shell
EXPO_PUBLIC_SUPABASE_URL=""
EXPO_PUBLIC_SUPABASE_KEY=""
```

## Lancement de l'application

```shell
npx expo start
```

Lancement de l'application avec Expo Go.
