/*
lib/supabase.js
*/

import AsyncStorage from "@react-native-async-storage/async-storage";
import { createClient } from "@supabase/supabase-js";

const supabaseUrl = "https://kclvkwjjrbvjgbpehmpz.supabase.co";
const supabaseAnonKey =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtjbHZrd2pqcmJ2amdicGVobXB6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzUyMjYxNDgsImV4cCI6MjA1MDgwMjE0OH0.X3xQmFl_D4D3hG9y5lWK4mBzuZ5dH7AbNBDoSX6_QQg";

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});
