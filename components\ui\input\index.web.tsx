"use client";
import React, { useState, useRef, useCallback, useImperativeHandle } from "react";
import { TextInput } from "react-native";
import { useStyleContext } from "@gluestack-ui/nativewind-utils/withStyleContext";
import { tva } from "@gluestack-ui/nativewind-utils/tva";

const SCOPE = "INPUT";

const inputFieldStyle = tva({
  base: "flex-1 text-typography-900 py-auto px-3 placeholder:text-typography-500 h-full web:cursor-text web:data-[disabled=true]:cursor-not-allowed",
});

type IInputFieldProps = {
  className?: string;
  value?: string;
  onChangeText?: (text: string) => void;
  placeholder?: string;
  secureTextEntry?: boolean;
};

const InputField = React.forwardRef<TextInput, IInputFieldProps>(
  ({ className, value: controlledValue, onChangeText, placeholder, secureTextEntry, ...props }, ref) => {
    const context = useStyleContext(SCOPE);

    if (!context) {
      console.error("❌ Erreur: useStyleContext(SCOPE) est undefined.");
      return null;
    }

    // ✅ Stocke la valeur en local pour éviter les re-renders
    const [inputValue, setInputValue] = useState(controlledValue || "");

    const inputRef = useRef<TextInput>(null);

    // ✅ Assure que le `ref` ne soit pas recréé
    useImperativeHandle(ref, () => inputRef.current as TextInput, []);

    const handleChangeText = useCallback(
      (text: string) => {
        setInputValue(text); // 🔥 Mise à jour locale
        if (onChangeText) {
          onChangeText(text);
        }
      },
      [onChangeText]
    );

    return (
      <TextInput
        ref={inputRef}
        {...props}
        placeholder={placeholder}
        secureTextEntry={secureTextEntry}
        value={inputValue} // ✅ Garde la valeur stable
        onChangeText={handleChangeText}
        className={inputFieldStyle({
          class: className,
        })}
        style={{
          outline: "none", // ✅ Évite la perte de focus sur Web
          width: "100%", // ✅ Assure que l'input ne change pas de taille dynamiquement
        }}
      />
    );
  }
);

InputField.displayName = "InputField";

export { InputField };
