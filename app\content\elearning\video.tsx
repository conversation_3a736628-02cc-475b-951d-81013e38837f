import { useLocalSearchParams, useRouter } from "expo-router";
import { View, StyleSheet, Text } from "react-native";
import { WebView } from "react-native-webview";
import Header from "@/components/common/Header";

export default function ELearningVideo() {
  const { url, title } = useLocalSearchParams();
  const router = useRouter();

  return (
    <View style={styles.container}>
      <Header
        title={title as string}
        onPressFlesh={() => router.back()}
      />
      <WebView
        source={{ uri: url as string }}
        style={styles.webview}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  webview: {
    flex: 1,
  },
}); 