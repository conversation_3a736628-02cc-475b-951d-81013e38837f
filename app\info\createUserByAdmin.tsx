/*
app/info/createUserByAdmin.tsx

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Extern`, `Intern` n'ont pas accès à cette page.
*/

import React, { useContext, useEffect, useState } from "react";
import ContainedButton from "@/components/ui/buttons/ContainedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useRouter } from "expo-router";
import {
  StyleSheet,
  Image,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  Linking,
} from "react-native";

import { useForm } from "react-hook-form";
import InputFileCustomized from "@/components/ui/inputs/InputFileCustomized";
import { AppContext } from "@/state/AppContext";
import TypeUserByAdmin from "@/types/typeUserByAdmin";
import * as ImagePicker from "expo-image-picker";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import uploadPhoto from "@/lib/uploadPictureStorage";
import CustomizedSelect from "@/components/ui/customizedSelect/customizedSelect";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { KeyboardAvoidingView, Platform } from "react-native"; // ✅ Ajouter à l'import

const roles = [
  { label: "Auditeur", value: "Auditeur" },
  { label: "Fournisseur", value: "Fournisseur" },
  { label: "Client", value: "Client" },
  { label: "Collaborateur", value: "Collaborateur" },
  { label: "Responsable QSE", value: "Responsable QSE" },
  { label: "Pilote de processus", value: "Pilote de processus" },
  { label: "Responsable QSE Adjoint", value: "Responsable QSE Adjoint" },
  { label: "Responsable Achat", value: "Responsable achat" },
  { label: "Responsable Maintenance", value: "Responsable maintenance" },

];

type UserRole = 
  | "Auditeur"
  | "Fournisseur"
  | "Client"
  | "Collaborateur"
  | "Responsable QSE"
  | "Pilote de processus";


const roleToStatus: Record<UserRole, string> = {
  Auditeur: "Extern",
  Fournisseur: "Supplier",
  Client: "User",
  Collaborateur: "User",
  "Responsable QSE": "Admin",
  "Pilote de processus": "Intern",
};



const inputs = [
  { label: "Nom", name: "last_name", placeholder: "Ecrire ton nom" },
  { label: "Prénom", name: "first_name", placeholder: "Ecrire ton prénom" },
  { label: "Téléphone", name: "phone_number", placeholder: "Ecrire le numéro de téléphone" },
  {
    label: "Role",
    name: "profil",
    placeholder: "Ecrire le role",
    type: "Select",
    data: roles,
  },
  {
    label: "Email",
    name: "email",
    placeholder: "Ecrire ton email",
  },
];

const generateSecurePassword = (length = 12) => {
  const lowerCase = "abcdefghijklmnopqrstuvwxyz";
  const upperCase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const numbers = "0123456789";
  const specialChars = "!@#$%^&*()_+[]{}|;:,.<>?";

  const allChars = lowerCase + upperCase + numbers + specialChars;

  let password = "";

  // Assurer qu'il y a au moins un caractère de chaque type
  password += lowerCase[Math.floor(Math.random() * lowerCase.length)];
  password += upperCase[Math.floor(Math.random() * upperCase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  password += specialChars[Math.floor(Math.random() * specialChars.length)];

  // Ajouter des caractères aléatoires jusqu'à la longueur souhaitée
  for (let i = password.length; i < length; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)];
  }

  // Mélanger les caractères pour éviter les prévisibilités
  password = password
    .split("")
    .sort(() => 0.5 - Math.random())
    .join("");

  return password;
};

export default function CreateUserByAdmin() {
  const [loading, setLoading] = useState(false);
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [showSupplierFields, setShowSupplierFields] = useState(false);
  const { user, companySelected } = useContext(AppContext);
  const router = useRouter();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const {
    register,
    handleSubmit,
    setError,
    watch,
    formState: { errors, touchedFields },
    control,
    reset,
  } = useForm<TypeUserByAdmin>({
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      phone_number: "",
      company_name: "",
      status: "regular"
    },
  });

  // Surveiller le changement de rôle
  const selectedRole = watch("profil");
  useEffect(() => {
    setShowSupplierFields(selectedRole === "Fournisseur");
  }, [selectedRole]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const roleOptions = [
    { label: "Auditeur", value: "Auditeur" },
    { label: "Fournisseur", value: "Fournisseur" },
    { label: "Client", value: "Client" },
    { label: "Collaborateur", value: "Collaborateur" },
    { label: "Responsable QSE", value: "Responsable QSE" },
    { label: "Pilote de processus", value: "Pilote de Processus" },
    { label: "Responsable QSE Adjoint", value: "Responsable QSE Adjoint" },
  ];

  const createUser = async (data: TypeUserByAdmin) => {
    if (!data.first_name || !data.last_name || !data.profil || !data.email) {
      Toast.show({
        type: "error",
        text1: "Les champs obligatoires ne sont pas remplis",
        text1Style: { color: "#1C3144" },
      });
      return;
    }

    if (data.profil === "Fournisseur" && !data.company_name) {
      Toast.show({
        type: "error",
        text1: "Le nom de l'entreprise est obligatoire pour un fournisseur",
        text1Style: { color: "#1C3144" },
      });
      return;
    }

    setLoading(true);

    try {
      const passwordGenerated = generateSecurePassword(12);

      // Étape 1 : Création de l'utilisateur dans Supabase Auth
      const { data: sessionData, error: signUpError } = await supabase.auth.signUp({
        email: data.email,
        password: passwordGenerated,
      });

      if (signUpError) {
        console.log("Erreur inscription :", signUpError);
        Toast.show({
          type: "error",
          text1: "Erreur lors de l'enregistrement de l'utilisateur",
          text1Style: { color: "#1C3144" },
        });
        setLoading(false);
        return;
      }

      const newUserId = sessionData.user?.id;
      if (!newUserId) {
        console.error("Erreur : Aucun UID récupéré pour le nouvel utilisateur.");
        setLoading(false);
        return;
      }

      // Étape 2 : Upload de l'image si disponible
      let uploadedImageUrl = null;
      if (imageUri) {
        const response = await uploadPhoto(imageUri, newUserId);
        uploadedImageUrl = response?.url || null;
      }

      const userStatus = roleToStatus[data.profil as UserRole] || "User";

      // Étape 3 : Ajout de l'utilisateur dans la table `users`
      const userInsert = {
        uid: newUserId,
        uid_admin: user?.uid,
        first_name: data.first_name,
        last_name: data.last_name,
        email: data.email,
        phone_number: data.phone_number || null,
        is_active: false,
        profil_picture: uploadedImageUrl,
        status: userStatus,
        profil: data.profil,
      };

      const { error: userError } = await supabase.from("users").insert(userInsert);
      if (userError) {
        console.log("Erreur insertion users :", userError);
        Toast.show({
          type: "error",
          text1: "Erreur lors de l'ajout de l'utilisateur",
          text1Style: { color: "#1C3144" },
        });
        setLoading(false);
        return;
      }

      // Étape 4 : Ajout de l'utilisateur dans `company_users`
      const { error: companyUserError } = await supabase.from("company_users").insert({
        company_id: companySelected?.id,
        user_id: newUserId,
        is_active: false,
      });

      if (companyUserError) {
        console.log("Erreur insertion company_users :", companyUserError);
        Toast.show({
          type: "error",
          text1: "Erreur lors de l'affectation à l'entreprise",
          text1Style: { color: "#1C3144" },
        });
        setLoading(false);
        return;
      }

      // Étape 5 : Si c'est un fournisseur, ajouter dans la table `suppliers`
      if (data.profil === "Fournisseur") {
        const { error: supplierError } = await supabase.from("suppliers").insert({
          uid_user: newUserId,
          company_name: data.company_name,
          status: data.status || "regular"
        });

        if (supplierError) {
          console.log("Erreur insertion suppliers :", supplierError);
          Toast.show({
            type: "error",
            text1: "Erreur lors de l'ajout des informations fournisseur",
            text1Style: { color: "#1C3144" },
          });
          setLoading(false);
          return;
        }
      }

      // Succès
      Toast.show({
        type: "success",
        text1: "Utilisateur créé avec succès",
        text1Style: { color: "#1C3144" },
      });

      reset();
      setImageUri("");
      router.back();
    } catch (err) {
      console.log("Erreur inattendue :", err);
      Toast.show({
        type: "error",
        text1: "Une erreur est survenue",
        text1Style: { color: "#1C3144" },
      });
    } finally {
      setLoading(false);
    }
  };

  const pickImage = async () => {
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (permissionResult.granted === false) {
        Alert.alert(
          "Permission requise",
          "L'application a besoin d'accéder à votre galerie pour sélectionner une photo de profil.",
          [
            {
              text: "Annuler",
              style: "cancel"
            },
            {
              text: "Ouvrir les paramètres",
              onPress: () => Linking.openSettings()
            }
          ]
        );
        return;
      }

      const pickerResult = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.5,
      });

      if (!pickerResult.canceled && pickerResult.assets && pickerResult.assets.length > 0) {
        setImageUri(pickerResult.assets[0].uri);
      }
    } catch (error) {
      console.error("Erreur lors de la sélection de l'image:", error);
      Alert.alert(
        "Erreur",
        "Une erreur est survenue lors de la sélection de l'image. Veuillez réessayer."
      );
    }
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 0}
    >
      <ScrollView
        contentContainerStyle={{ paddingBottom: 80 }} // 🔁 ou utiliser `flexGrow: 1` pour plus de flexibilité
        keyboardShouldPersistTaps="handled"
      >
      <View style={styles.containerContent}>
        <View style={styles.registerInfo}>
          <View style={styles.inputs}>
            {inputs.map((input, index) => {
              if (input?.type === "Select") {
                return (
                  <View key={index}>
                    <CustomizedSelect
                      name={input.name}
                      label={input.label}
                      register={register}
                      control={control}
                      errors={errors}
                      setError={setError}
                      data={input.data as any}
                    />
                  </View>
                );
              } else {
                return (
                  <View key={index}>
                    <InputCustomized
                      label={input.label}
                      placeholder={input.placeholder}
                      register={register}
                      name={input.name}
                      control={control}
                      errors={errors}
                      setError={setError}
                    />
                  </View>
                );
              }
            })}

            {/* Champs spécifiques aux fournisseurs */}
            {showSupplierFields && (
              <>
                <View>
                  <InputCustomized
                    label="Nom de l'entreprise"
                    placeholder="Ecrire le nom de l'entreprise"
                    register={register}
                    name="company_name"
                    control={control}
                    errors={errors}
                    setError={setError}
                  />
                </View>
                <View>
                  <CustomizedSelect
                    name="status"
                    label="Statut"
                    register={register}
                    control={control}
                    errors={errors}
                    setError={setError}
                    data={[
                      { label: "Bon", value: "good" },
                      { label: "Moyen", value: "regular" },
                      { label: "Mauvais", value: "bad" }
                    ]}
                  />
                </View>
              </>
            )}
          </View>
        </View>

        {imageUri ? (
          <>
            <Image
              source={{ uri: imageUri }}
              style={{
                width: 150,
                height: 150,
                marginVertical: 20,
                borderRadius: 150,
              }}
            />
            <TouchableOpacity onPress={() => setImageUri(null)}>
              <Text style={{ color: "blue", textAlign: "center" }}>Changer la photo</Text>
            </TouchableOpacity>
          </>
        ) : (
          <InputFileCustomized
            label={"Photo de profil"}
            placeholder={"Choisir la photo"}
            onPress={pickImage}
          />
        )}

        {/* Bouton de soumission */}
        <ContainedButton
            label="Créer"
            backgroundColor="#F99527"
            onPress={async () => {
              await handleSubmit(createUser)(); // ✅ Gère l'attente de la soumission
            }}
          />

      </View>
    </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  label: {
    fontWeight: "bold",
    marginBottom: 1,
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginTop: 4,
  },
  image: {
    width: 150,
    height: 150,
    borderRadius: 75,
    marginVertical: 20,
  },
  containerContent: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    // padding: 20,
    gap: 20,
    marginVertical: 20,
    width: "80%",
    alignSelf: "center",
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    // backgroundColor: "yellow",
    width: "100%",
    gap: 20,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  buttonPassword: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  titleContainer: {
    flexDirection: "row",
    gap: 8,
  },
  imgIsLoadingPage: {
    height: 75,
    width: 130,
    alignSelf: "center",
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
  input: {
    backgroundColor: "white",
    borderColor: "none",
    height: 40,
    padding: 10,
    borderRadius: 4,
  },
});
