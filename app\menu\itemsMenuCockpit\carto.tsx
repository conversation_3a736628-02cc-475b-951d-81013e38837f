/*
app/menu/itemsMenuCockpit/info.tsx

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `Supplier` n'ont pas accès à cette page.
*/

import { <PERSON><PERSON>, <PERSON><PERSON>View, StyleSheet, View, Platform, ActivityIndicator } from "react-native";
import ProcessInfosSection from "@/components/infoSection/process";
import ActivitiesInfosSection from "@/components/infoSection/activities";
import { useCallback, useContext, useEffect, useState } from "react";
import { AppContext } from "@/state/AppContext";
import typeUserByAdmin from "@/types/typeUserByAdmin";
import { supabase } from "@/lib/supabase";
import TypeProcess from "@/types/typeProcess";
import TypeActivity from "@/types/typeActivity";
import UsersInfosSection from "@/components/infoSection/users";
import { useFocusEffect } from "expo-router";
import ContextsInfoSection from "@/components/infoSection/context";
import IssuesInfoSection from "@/components/infoSection/issue";
import TypePolicy from "@/types/typePolicies";
import PoliciesInfoSection from "@/components/infoSection/policy";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import Header from "@/components/common/Header";
import { useRouter } from "expo-router";

export default function Info() {
  const [usersAdmin, setUsersAdmin] = useState<typeUserByAdmin[]>();
  const [process, setProcess] = useState<TypeProcess[]>();
  const [activites, setActivties] = useState<TypeActivity[]>();
  const [contexts, setContexts] = useState<TypeActivity[]>();
  const [issues, setIssues] = useState<TypeActivity[]>();
  const [policies, setPolicies] = useState<TypePolicy[]>();
  const { user, companySelected } = useContext(AppContext);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const router = useRouter();

  useEffect(() => {
    if (!user) {
      router.replace('/pre-login');
    }
  }, [user]);

  const fetchDataUsers = async () => {
    if (!user) return;
    // 🔥 Récupérer les utilisateurs de l'entreprise via `company_users`
    const { data, error } = await supabase
      .from("company_users")
      .select(`
        user_id,
        users: user_id (
          id, uid, uid_admin, created_at, first_name, last_name, profil_picture, is_active, email, profil, status
        )
      `)
      .eq("company_id", companySelected?.id);
  
    if (error) {
      console.error("Erreur récupération utilisateurs :", error);
      return;
    }
  
    const usersList = data.map((entry: any) => entry.users).flat(); 
    setUsersAdmin(usersList);
  };

  const fetchDataProcess = async () => {
    if (!user) return;
    const { data, error } = await supabase
      .from("process")
      .select("*, companies(*), users(*)s")
      .eq("uid_admin", user?.uid);

    if (!error) {
      setProcess(data as any);
    }
  };

  const fetchDataActivities = async () => {
    if (!user) return;
    const { data, error } = await supabase
      .from("activities")
      .select()
      .eq("uid_admin", user?.uid);

    if (!error) {
      setActivties(data as any);
    }
  };

  const fetchDataContexts = async () => {
    if (!user) return;
    const { data, error } = await supabase
      .from("contexts")
      .select()
      .eq("uid_admin", user?.uid);

    if (!error) {
      setContexts(data as any);
    }
  };

  const fetchDataIssues = async () => {
    if (!user) return;
    const { data, error } = await supabase
      .from("issues")
      .select()
      .eq("uid_admin", user?.uid);

    if (!error) {
      setIssues(data as any);
    }
  };

  const fetchDataPolicies = async () => {
    if (!user) return;
    const { data, error } = await supabase
      .from("policies")
      .select()
      .eq("uid_admin", user?.uid);

    if (!error) {
      setPolicies(data as any);
    }
  };

  useFocusEffect(
    useCallback(() => {
      if (!user) return;
      fetchDataUsers();
      fetchDataProcess();
      fetchDataActivities();
      fetchDataContexts();
      fetchDataIssues();
      fetchDataPolicies();
    }, [user])
  );

  if (!user) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#F99527" />
      </View>
    );
  }

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["User", "Supplier"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  return (    
    <View style={styles.mainContainer}>
      <ScrollView style={styles.container}>
        <View style={styles.content}>
          <View style={styles.section}>
            <UsersInfosSection data={usersAdmin as any} />
          </View>
          <View style={styles.section}>
            <ProcessInfosSection data={process as any} />
          </View>
          {/* <View style={styles.section}>
            <ActivitiesInfosSection data={activites as any} />
          </View> */}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  content: {
    gap: 20,
    padding: 20,
  },
  section: {
    marginBottom: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
