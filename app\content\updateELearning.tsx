/*
app/eLearning/createELearning.tsx

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern` et `Extern` n'ont pas accès à cette page.
*/

import ContainedButton from "@/components/ui/buttons/ContainedButton";
import OutlinedButton from "@/components/ui/buttons/OutlinedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useLocalSearchParams, useRouter } from "expo-router";
import {
  StyleSheet,
  Image,
  View,
  ScrollView,
  Alert,
  Linking,
  Text,
  TouchableOpacity,
  Platform,
} from "react-native";

import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import CustomizedSelect from "@/components/ui/customizedSelect/customizedSelect";
import { useNavigation } from "expo-router";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { ParamListBase } from "@react-navigation/native";
import TypeELearning from "@/types/typeELearning";
import InputFileCustomized from "@/components/ui/inputs/InputFileCustomized";
import * as ImagePicker from "expo-image-picker";
import * as DocumentPicker from "expo-document-picker";
import uploadPhoto from "@/lib/uploadPictureStorage";
import { WebView } from "react-native-webview";
import { useVideoPlayer, VideoView } from "expo-video";
import CustomizedSelectMultiple from "@/components/ui/customizedSelect/customizedSelectMultiple";
import { Button, ButtonText } from "@/components/ui/button";
import Header from "@/components/common/Header";
import colors from "tailwindcss/colors";

const fileTypes = [
  { value: "video", label: "Vidéo" },
  { value: "pdf", label: "Pdf" },
];

const roleOptions = [
  { label: "Auditeur", value: "Auditeur" },
  { label: "Fournisseur", value: "Fournisseur" },
  { label: "Client", value: "Client" },
  { label: "Collaborateur", value: "Collaborateur" },
  { label: "Responsable QSE", value: "Responsable QSE" },
  { label: "Pilote de processus", value: "Pilote de Processus" },
  { label: "Responsable QSE Adjoint", value: "Responsable QSE Adjoint" },
];

export default function UpdateELearning() {
  const {
    idELearning,
    title,
    description,
    videoSource,
    file_type,
    url_mignature,
    url_video,
    type_profil,
  } = useLocalSearchParams();

  // 🔥 Correction : Parser `type_profil`
  const parsedTypeProfil =
    type_profil && typeof type_profil === "string"
      ? JSON.parse(type_profil)
      : [];

  const [loading, setLoading] = useState(false);
  const [imageUri, setImageUri] = useState("");
  const [videoUri, setVideoUri] = useState("");
  const [pdfUri, setPdfUri] = useState("");
  const [users, setUsers] = useState([]);
  const [typeProfil, setTypeProfil] = useState<string[]>([]);

  const toggleSelection = (value: string) => {
    setTypeProfil((prevSelected) =>
      prevSelected.includes(value)
        ? prevSelected.filter((item) => item !== value)
        : [...prevSelected, value]
    );
  };

  const { user } = useContext(AppContext);
  const router = useRouter();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const {
    register,
    handleSubmit,
    setError,
    formState: { errors, touchedFields },
    control,
    reset,
    watch,
  } = useForm<TypeELearning>({
    defaultValues: {
      uid_user: user?.uid,
      title: title as string,
      description: description as string,
      file_type: file_type as string,
      url_mignature: url_mignature as string,
      url_video: url_video as string,
      url_media: videoSource as string,
      type_profil: type_profil as string,
    },
  });

  useEffect(() => {
    const fetchDataELearnings = async () => {
      const { data, error } = await supabase
        .from("elearnings")
        .select()
        .eq("uid_user", user?.uid)
        .eq("id", idELearning);
      if (!error) {
        setTypeProfil(Object.keys(data[0].type_profil) as any);
      }
    };

    fetchDataELearnings();
  }, []);

  const pickImage = async () => {
    const permissionResult =
      await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (!permissionResult.granted) {
      Toast.show({
        type: "error",
        text1: "Permissions refusées",
        text1Style: { color: "#1C3144" },
        text2: "Vous devez autoriser l'accès à vos photos.",
      });
      return;
    }

    const pickerResult = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ["images"],
      allowsEditing: true,
      // aspect: [500, 500],
      quality: 1,
    });

    if (!pickerResult.canceled && pickerResult.assets.length > 0) {
      const selectedImageUri = pickerResult.assets[0].uri;
      setImageUri(selectedImageUri); // ✅ Mise à jour du state (même si asynchrone)

      // 📌 Déclencher immédiatement la mise à jour du profil avec l'image sélectionnée
    }
  };

  const pickVideo = async () => {
    const permissionResult =
      await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (!permissionResult.granted) {
      Toast.show({
        type: "error",
        text1: "Permissions refusées",
        text1Style: { color: "#1C3144" },
        text2: "Vous devez autoriser l'accès à vos photos.",
      });
      return;
    }

    const pickerResult = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ["videos"],
      allowsEditing: true,
      // aspect: [500, 500],
      quality: 1,
    });

    if (!pickerResult.canceled && pickerResult.assets.length > 0) {
      const selectedImageUri = pickerResult.assets[0].uri;
      setVideoUri(selectedImageUri); // ✅ Mise à jour du state (même si asynchrone)

      // 📌 Déclencher immédiatement la mise à jour du profil avec l'image sélectionnée
    }
  };

  const pickPDF = async () => {
    const result = await DocumentPicker.getDocumentAsync({
      type: "application/pdf",
    });

    if (result.canceled) return;

    setPdfUri(result?.assets[0].uri);
  };

  useEffect(() => {
    setLoading(true);
    const fetchUsersAdmin = async () => {
      const { data: dataGetted, error } = await supabase
        .from("users")
        .select()
        .eq("uid_admin", user?.uid);

      setUsers(dataGetted as any);

      setLoading(false);
    };
    fetchUsersAdmin();
  }, []);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const inputs = [
    {
      label: "Titre",
      name: "title",
      placeholder: "Ecrire le titre du contenu",
      type: "text",
    },
    {
      label: "Description",
      name: "description",
      placeholder: "Description",
      type: "text",
      multiline: true,
    },
    {
      label: "Type de fichier",
      name: "file_type",
      placeholder: "Type de fichier",
      type: "Select",
      data: fileTypes,
      isDisabled: true,
    },
    {
      label: "URL Youtube",
      name: "url_video",
      placeholder: "https://youtube.com/...",
      type: "text",
    },
    {
      label: "Type d'utilisateur",
      name: "type_profil",
      placeholder: "Type d'utilisateur",
      type: "Select",
      multiple: true,

      data: roleOptions,
    },
  ];

  const player = useVideoPlayer(
    videoUri ? videoUri : (videoSource as any),
    (player) => {
      player.loop = true;
      player.pause();
    }
  );

  const createELearning = async (data: TypeELearning) => {
    setLoading(true);

    let responseImg: any;
    try {
      if (imageUri) {
        responseImg = await uploadPhoto(
          imageUri,
          user?.uid as any,
          "images",
          "mignatures",
          "image/jpeg"
        );
      }
      let response: any;
      if (videoUri) {
        response = await uploadPhoto(
          watch("file_type") === "video" ? videoUri : pdfUri,
          user?.uid as any,
          watch("file_type") === "video" ? "videos" : "pdf",
          "cartos",
          watch("file_type") === "video" ? "video/mp4" : "application/pdf"
        );
      }
      const selectedObject = Object.fromEntries(
        typeProfil.map((value) => [value, true])
      );

      const { error } = await supabase
        .from("elearnings")
        .update({
          title: data.title,
          description: data.description,
          url_mignature: responseImg?.url ? responseImg?.url : url_mignature,
          url_video: data.url_video,
          url_media: response?.url ? response?.url : videoSource,
          type_profil: selectedObject,
        })
        .eq("id", idELearning)
        .eq("uid_user", user?.uid);

      if (error) {
        console.log("error__", error);
        Toast.show({
          type: "error",
          text1: "Erreur lors d'enregistrement de la eLearning",

          text1Style: { color: "#1C3144" },
        });
        setLoading(false);
        return;
      }

      Toast.show({
        type: "success",
        text1: "La ELearning était bien crée",
        text1Style: { color: "#1C3144" },
      });

      router.back();

      reset();
      setLoading(false);
    } catch (err) {
      setLoading(false);
      console.log("err", err);
      Toast.show({
        type: "error",
        text1: "Une erreur est survenue",
        text1Style: { color: "#1C3144" },
      });
      // }
    }
  };

  const deleteELearning = async () => {
    if (Platform.OS === 'web') {
      if (window.confirm("Êtes-vous sûr de vouloir supprimer cet E-Learning ? Cette action supprimera également toutes les données associées aux salariés qui ont suivi ce cours.")) {
        await handleDelete();
      }
    } else {
      Alert.alert(
        "Supprimer l'E-Learning",
        "Êtes-vous sûr de vouloir supprimer cet E-Learning ? Cette action supprimera également toutes les données associées aux salariés qui ont suivi ce cours.",
        [
          {
            text: "Annuler",
            style: "cancel"
          },
          {
            text: "Supprimer",
            onPress: handleDelete,
            style: "destructive"
          }
        ]
      );
    }
  };

  const handleDelete = async () => {
    setLoading(true);
    try {
      // D'abord, supprimer les enregistrements dans elearnings_students
      const { error: studentsError } = await supabase
        .from("elearnings_students")
        .delete()
        .eq("elearning_id", idELearning);

      if (studentsError) {
        console.log('Erreur lors de la suppression des étudiants:', studentsError);
        Toast.show({
          type: "error",
          text1: "Erreur lors de la suppression des données étudiants",
          text1Style: { color: "#1C3144" },
        });
        setLoading(false);
        return;
      }

      // Ensuite, supprimer l'eLearning
      const { error } = await supabase
        .from("elearnings")
        .delete()
        .eq("id", idELearning)
        .eq("uid_user", user?.uid);

      if (error) {
        console.log('Erreur lors de la suppression:', error);
        Toast.show({
          type: "error",
          text1: "Erreur lors de la suppression",
          text1Style: { color: "#1C3144" },
        });
        setLoading(false);
        return;
      }

      Toast.show({
        type: "success",
        text1: "ELearning et données associées supprimés avec succès",
        text1Style: { color: "#1C3144" },
      });
      router.push("/content/elearning");
    } catch (err) {
      console.log('Erreur inattendue:', err);
      Toast.show({
        type: "error",
        text1: "Une erreur est survenue",
        text1Style: { color: "#1C3144" },
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={{ flex: 1 }}>
      <Header
        title="Modifier la vidéo"
        onPressFlesh={() => router.back()}
      />
      <ScrollView>
        <View style={styles.containerContent}>
          <View style={styles.registerInfo}>
            <View style={styles.inputs}>
              {inputs.map((input, index) => {
                if (input?.type === "Select") {
                  return (
                    <View key={index}>
                      {input.multiple === true ? (
                        <View>
                          <Text style={styles.label}>Type d'utilisateur</Text>
                          {typeProfil.length === 0 && (
                            <Text style={[styles.label, { color: colors.red[500] }]}>
                              * Vous devez sélectionner au moins un type d'utilisateur
                            </Text>
                          )}
                          <View
                            style={{
                              display: "flex",
                              flexDirection: "row",
                              flexWrap: "wrap",
                              gap: 10,
                            }}
                          >
                            {roleOptions.map((role) => (
                              <TouchableOpacity
                                key={role.value}
                                style={[
                                  styles.roleButton,
                                  typeProfil.includes(role.value) &&
                                    styles.roleButtonSelected,
                                ]}
                                onPress={() => toggleSelection(role.value)}
                              >
                                <Text
                                  style={
                                    typeProfil.includes(role.value)
                                      ? styles.roleTextSelected
                                      : styles.roleText
                                  }
                                >
                                  {role.label}
                                </Text>
                              </TouchableOpacity>
                            ))}
                          </View>
                        </View>
                      ) : (
                        <CustomizedSelect
                          name={input.name}
                          label={input.label}
                          register={register}
                          control={control}
                          errors={errors}
                          setError={setError}
                          data={input.data as any}
                          isDisabled={input.isDisabled}
                        />
                      )}
                    </View>
                  );
                } else {
                  return (
                    <View key={index}>
                      {!(
                        input.name === "url_video" &&
                        watch("file_type") !== "" &&
                        watch("file_type") === "pdf"
                      ) && (
                        <InputCustomized
                          label={input.label}
                          placeholder={input.placeholder}
                          register={register}
                          name={input.name}
                          control={control}
                          errors={errors}
                          setError={setError}
                          multiline={input.multiline}
                          isRequired={input.name === "url_video" && false}
                        />
                      )}
                    </View>
                  );
                }
              })}
            </View>
          </View>
          {watch("file_type") === "video" && watch("file_type") !== "" && (
            <>
              {!watch("url_video") && !videoUri && !videoSource && (
                <Text style={[styles.label, { color: colors.red[500] }]}>
                  * Vous devez soit renseigner une URL Youtube, soit télécharger une vidéo
                </Text>
              )}
              {!imageUri && (videoUri || videoSource) && !watch("url_video") && (
                <Text style={[styles.label, { color: colors.red[500] }]}>
                  * Une miniature est requise pour la vidéo
                </Text>
              )}
              {!imageUri && !watch("url_video") && (
                <InputFileCustomized
                  label={"Miniature"}
                  placeholder={"Choisir la photo"}
                  onPress={pickImage}
                />
              )}
              {imageUri && !watch("url_video") ? (
                <View style={{ display: "flex" }}>
                  <Text style={styles.label}>Miniature</Text>
                  <TouchableOpacity onPress={pickImage}>
                    <Image
                      source={{ uri: imageUri }}
                      style={{
                        width: "100%",
                        height: 200,
                        marginTop: 10,
                        alignSelf: "center",
                      }}
                    />
                  </TouchableOpacity>
                </View>
              ) : null}
            </>
          )}

          {watch("file_type") !== "" && (
            <>
              {(videoUri || videoSource) && !watch("url_video") && (
                <Text style={styles.label}>
                  {watch("file_type") === "video" ? "Vidéo" : "Fichier"}
                </Text>
              )}

              {!videoSource && !videoUri && !watch("url_video") && (
                <InputFileCustomized
                  label={watch("file_type") === "video" ? "Vidéo" : "Fichier"}
                  placeholder={
                    watch("file_type") === "video"
                      ? "Choisir la vidéo"
                      : "Choisir le fichier"
                  }
                  onPress={pickVideo}
                />
              )}

              {/* Affichage de l'aperçu */}
              {file_type === "video" && (videoUri || videoSource) && !watch("url_video") ? (
                <View style={{ width: "100%" }}>
                  <Button
                    size="xs"
                    variant="outline"
                    action="primary"
                    onPress={pickVideo}
                    style={{ width: 150, marginBottom: 10 }}
                  >
                    <ButtonText>Modifier la vidéo</ButtonText>
                  </Button>

                  <VideoView
                    style={{ width: "100%", height: 200 }}
                    player={player}
                  />
                </View>
              ) : null}

              {watch("file_type") === "pdf" && pdfUri ? (
                <View style={{ marginTop: 10 }}>
                  <OutlinedButton
                    label="Voir le PDF"
                    onPress={() => Linking.openURL(pdfUri)}
                  />
                </View>
              ) : null}
            </>
          )}

          <View style={styles.buttons}>
            <ContainedButton
              label="Modifier"
              backgroundColor="#F99527"
              onPress={handleSubmit(createELearning)}
              disabled={loading}
            />
            <ContainedButton
              label="Supprimer"
              backgroundColor="#FF4444"
              onPress={deleteELearning}
              disabled={loading}
            />
          </View>
        </View>
      </ScrollView>
    </View>
  );
}
const styles = StyleSheet.create({
  containerContent: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    // padding: 20,
    gap: 20,
    marginVertical: 20,
    width: "80%",
    alignSelf: "center",
    marginBottom: 200,
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    // backgroundColor: "yellow",
    width: "100%",
    gap: 20,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  buttonPassword: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  titleContainer: {
    flexDirection: "row",
    gap: 8,
  },
  imgIsLoadingPage: {
    height: 75,
    width: 130,
    alignSelf: "center",
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
  input: {
    backgroundColor: "white",
    borderColor: "none",
    height: 40,
    padding: 10,
    borderRadius: 4,
  },
  roleButton: {
    paddingHorizontal: 10,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "black",
  },
  roleButtonSelected: {
    backgroundColor: "#F99527",
    borderColor: "#F99527",
  },
  roleText: {
    color: "black",
  },
  roleTextSelected: {
    color: "white",
  },
  label: {
    fontSize: 14,
    color: "#262627",
    marginVertical: 1,
    fontWeight: "bold",
    alignSelf: "flex-start",
  },
});
