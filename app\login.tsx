import ContainedButton from "@/components/ui/buttons/ContainedButton";
import { useRouter } from "expo-router";
import React, { useContext, useState } from "react";
import {
  StyleSheet,
  View,
  Image,
  Text,
  KeyboardAvoidingView,
  Keyboard,
  Platform,
  ScrollView,
  TouchableWithoutFeedback,
  TouchableOpacity,
} from "react-native";
import { supabase } from "../lib/supabase";
import TypeRegister from "@/types/register";
import { useForm } from "react-hook-form";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import TypeLogin from "@/types/login";
import { AppContext } from "@/state/AppContext";
import Toast from "react-native-toast-message";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";

const inputs = [
  { label: "Email", name: "email", placeholder: "Ecrire ton email" },

  {
    label: "Mot de passe",
    name: "password",
    placeholder: "Ecrire ton mot de passe",
  },
];

export default function Login() {
  const router = useRouter();

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors, touchedFields },
    control,
    reset,
    getValues, // Import correct depuis useForm
  } = useForm<TypeRegister>({
    defaultValues: {
      email: "",
      password: "",
    },
  });
  const [loading, setLoading] = useState(false);
  const { setSession, setUser, setCompanySelected } = useContext(AppContext);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const [resetLoading, setResetLoading] = useState(false);
  const [showForgotPasswordInfo, setShowForgotPasswordInfo] = useState(false);

  async function signInWithEmail(data: TypeLogin) {
    try {
      setLoading(true);

      const { data: authData, error: authError } =
        await supabase.auth.signInWithPassword({
          email: data.email,
          password: data.password,
        });

      if (authError) {
        console.error("Authentication error:", authError);
        Toast.show({
          type: "error",
          text1: "Votre mail ou mot de passe est incorrect",
          text1Style: { color: "#1C3144" },
        });
        return;
      }

      // Mettre à jour la session dans le contexte
      if (authData.session) {
        setSession(authData.session);
      }

      const { data: user, error: userError } = await supabase
        .from("users")
        .select("*, companies(*)")
        .eq("uid", authData.user?.id)
        .single();

      if (userError) {
        console.error("User fetch error:", userError);
        Toast.show({
          type: "error",
          text1: "Une erreur est survenue",
          text1Style: { color: "#1C3144" },
        });
        return;
      }

      setUser(user);
      setCompanySelected(user?.companies[0]);
      
      // Attendre un court instant pour s'assurer que la session est bien mise à jour
      await new Promise(resolve => setTimeout(resolve, 100));
      
      router.replace("/Accueil");
      reset();
    } catch (error) {
      console.error("Login error:", error);
      Toast.show({
        type: "error",
        text1: "Une erreur est survenue",
        text1Style: { color: "#1C3144" },
      });
    } finally {
      setLoading(false);
    }
  }


  const getRedirectUrl = () => {
    if (__DEV__) return "http://localhost:8081/auth-redirect"; // pour React Native Web
    return "https://app.izys-qhse.com/auth-redirect";
  };
  
  const handleSendMagicLink = async (email: string) => {
    const { error } = await supabase.auth.signInWithOtp({
      email,
      options: {
        emailRedirectTo: getRedirectUrl(),
      },
    });
  
    if (error) {
      console.error("Erreur magic link :", error.message);
      Toast.show({
        type: "error",
        text1: "Une erreur est survenue lors de l'envoi du lien",
        text1Style: { color: "#1C3144" },
        visibilityTime: 4000,
      });
    } else {
      console.log("Lien magique envoyé !");
      Toast.show({
        type: "success",
        text1: "Email envoyé avec succès",
        text1Style: { color: "#1C3144", fontSize: 16 },
        text2: "Vérifiez votre boîte mail et cliquez sur le lien pour vous connecter",
        text2Style: { color: "#1C3144", fontSize: 14 },
        visibilityTime: 6000,
        position: "top",
        topOffset: 50,
      });
    }
  };
  

  // Fonction pour gérer le mot de passe oublié
  const handleForgotPassword = async (email: string) => {
    console.log("=== DEMANDE RÉINITIALISATION MOT DE PASSE ===");
    console.log("Email utilisé:", email);
    console.log("Timestamp:", new Date().toISOString());

    if (!email) {
      console.log("Erreur: Email non fourni");
      Toast.show({
        type: "error",
        text1:
          "Veuillez entrer votre email avant de réinitialiser le mot de passe.",
        text1Style: { color: "#1C3144" },
      });
      return;
    }

    setResetLoading(true);

    try {
      console.log("Tentative d'envoi d'email de réinitialisation...");
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: "myapp://reset-password",
        expiresIn: 7 * 24 * 60 * 60,
      });

      if (error) {
        console.log("=== ERREUR RÉINITIALISATION ===");
        console.log("Type d'erreur:", error.message);
        console.log("Code d'erreur:", error.status);
        
        let errorMessage = "Une erreur est survenue lors de l'envoi de l'email.";
        if (error.message.includes("invalid email")) {
          errorMessage = "L'adresse email n'est pas valide.";
        } else if (error.message.includes("rate limit")) {
          errorMessage = "Trop de tentatives. Veuillez réessayer plus tard.";
        }

        Toast.show({
          type: "error",
          text1: errorMessage,
          text1Style: { color: "#1C3144" },
        });
        return;
      }

      console.log("=== EMAIL ENVOYÉ AVEC SUCCÈS ===");
      console.log("Email de destination:", email);
      console.log("URL de redirection configurée:", "myapp://reset-password");
      
      Toast.show({
        type: "success",
        text1: "Email envoyé avec succès.",
        text1Style: { color: "#1C3144" },
        text2:
          "Veuillez vérifier votre boîte mail pour réinitialiser le mot de passe. Le lien sera valide pendant 7 jours.",
      });
    } catch (error) {
      console.log("=== ERREUR INATTENDUE ===");
      console.log("Erreur complète:", error);
      Toast.show({
        type: "error",
        text1: "Une erreur inattendue est survenue.",
        text1Style: { color: "#1C3144" },
      });
    } finally {
      setResetLoading(false);
    }
  };

  const handleForgotPasswordClick = (email: string) => {
    setShowForgotPasswordInfo(true);
    handleSendMagicLink(email);
    
    // Hide the info after 10 seconds
    setTimeout(() => {
      setShowForgotPasswordInfo(false);
    }, 10000);
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      keyboardVerticalOffset={Platform.select({ ios: 0, android: 100 })}
    >
      <TouchableWithoutFeedback onPress={Platform.OS !== "web" ? Keyboard.dismiss : undefined} accessible={false}>
        <ScrollView style={styles.form}>
          <Toast />
          <View style={styles.webContainer}>
            <View style={styles.containerContent}>
              <Image
                source={require("@/assets/images/login/logoIZYS.png")}
                style={styles.imgIsLoadingPage}
              />
              <View style={styles.registerInfo}>
                <Text
                  style={
                    {
                      color: "black",
                      fontSize: 24,
                      alignSelf: "left",
                    } as any
                  }
                >
                  Se connecter
                </Text>

                <View style={styles.inputs}>
                  {inputs.map((input, index) => {
                    return (
                      <View key={index}>
                        <InputCustomized
                          label={input.label}
                          placeholder={input.placeholder}
                          register={register}
                          name={input.name}
                          control={control}
                          errors={errors}
                          setError={setError}
                        />
                      </View>
                    );
                  })}
                </View>
              </View>

              <View style={styles.buttons}>
                <ContainedButton
                  label="Se connecter"
                  backgroundColor="#F99527"
                  onPress={handleSubmit(signInWithEmail)}
                  disabled={loading}
                />
                <TouchableOpacity 
                  onPress={() => {
                    const email = getValues("email");
                    handleForgotPasswordClick(email);
                  }}
                  style={styles.forgotPasswordLink}
                >
                  <Text style={styles.forgotPasswordText}>Mot de passe oublié ?</Text>
                  {showForgotPasswordInfo && (
                    <Text style={styles.forgotPasswordInfo}>
                      Un lien de connexion vous sera envoyé par email. Une fois connecté, vous pourrez modifier votre mot de passe dans les paramètres de votre compte. Vous pourrez faire une nouvelle demande après 5 minutes.
                    </Text>
                  )}
                </TouchableOpacity>
              </View>

              <View style={styles.noAccountWrapper}>
                <Text style={styles.noAccountText}>
                  Vous n'avez pas de compte ?{" "}
                </Text>
                <TouchableOpacity onPress={() => navigation.navigate("register")}>
                  <Text style={styles.noAccountButtonText}>Créer un compte</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    height: "100%",
    width: "100%",
    backgroundColor: "#fff",
    alignItems: "center",
    alignContent: "center",
  },
  form: {
    width: "100%",
  },
  webContainer: {
    width: "100%",
    alignItems: "center",
  },
  containerContent: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    paddingHorizontal: 20,
    gap: 20,
    marginTop: Platform.OS === "web" ? "10%" : "30%",
    width: Platform.OS === "web" ? "400px" : "100%",
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    // backgroundColor: "yellow",
    width: "100%",
    gap: 20,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  titleContainer: {
    flexDirection: "row",
    gap: 8,
  },
  imgIsLoadingPage: {
    height: 68,
    width: 116,
    alignSelf: "center",
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
  input: {
    backgroundColor: "white",
    borderColor: "none",
    height: 40,
    padding: 10,
    borderRadius: 4,
  },
  noAccountWrapper: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "center",
  },
  noAccountText: {
    color: "#CCC",
    fontSize: 11,
  },
  noAccountButtonText: {
    color: "purple",
    fontSize: 11,
  },
  forgotPasswordLink: {
    marginTop: 10,
    width: "100%",
    alignItems: "center",
  },
  forgotPasswordText: {
    color: "#F99527",
    fontSize: 14,
    textDecorationLine: "underline",
    marginBottom: 4,
    textAlign: "center",
  },
  forgotPasswordInfo: {
    color: "#666",
    fontSize: 12,
    lineHeight: 16,
    fontStyle: "italic",
    textAlign: "center",
    marginTop: 8,
  },
});
