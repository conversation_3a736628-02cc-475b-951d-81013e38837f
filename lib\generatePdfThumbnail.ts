import PdfPageImage from 'react-native-pdf-page-image';
import { Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';

export async function generatePdfThumbnail(pdfBuffer: ArrayBuffer): Promise<string> {
  try {
    // Créer un fichier temporaire pour le PDF
    const tempPdfPath = `${FileSystem.cacheDirectory}temp_${Date.now()}.pdf`;
    await FileSystem.writeAsStringAsync(tempPdfPath, Buffer.from(pdfBuffer).toString('base64'), {
      encoding: FileSystem.EncodingType.Base64,
    });

    // Générer la miniature de la première page
    const image = await PdfPageImage.generate(tempPdfPath, 1, 1.0);
    
    // Nettoyer le fichier temporaire
    await FileSystem.deleteAsync(tempPdfPath);

    // Pour le web, nous devons convertir l'URI en base64
    if (Platform.OS === 'web') {
      const response = await fetch(image.uri);
      const blob = await response.blob();
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    }

    return image.uri;
  } catch (error) {
    console.error('Erreur lors de la génération de la miniature:', error);
    throw error;
  }
} 