import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  TextInput,
  Platform,
} from "react-native";
import { ContainerImgText } from "@/components/common/containerImgText";
import PaperInfoImgEvent from "@/components/common/paperInfoImgEvent";
import { useFocusEffect, useRouter } from "expo-router";
import { Octicons, FontAwesome } from "@expo/vector-icons";
import { useCallback, useContext, useState } from "react";
import TypeEvent from "@/types/typeEvent2";
import { supabase } from "@/lib/supabase";
import { AppContext } from "@/state/AppContext";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { getEventTypeLabel } from "@/utils/eventUtils";

export default function Event() {
  const router = useRouter();
  const { user, companySelected } = useContext(AppContext);
  const [events, setEvents] = useState<TypeEvent[] | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState({
    immediateActions: false,
    causes: false,
    correctiveActions: false,
  });
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  const fetchUsersAdmin = async () => {
    const companyId = companySelected?.id;
  
    // Récupérer les événements
    const { data: eventsData, error: eventsError } = await supabase
      .from("events")
      .select("id, wording, causes, date, type, file, created_at, description")
      .eq("company_id", companyId)
      .order("created_at", { ascending: false });
  
    if (eventsError) {
      console.error("Erreur lors de la récupération des événements :", eventsError.message);
      return;
    }
  
    // Récupérer les actions associées aux événements
    const eventIds = eventsData.map((event) => event.id); // Liste des ID des événements
    const { data: actionsData, error: actionsError } = await supabase
      .from("event_actions")
      .select("id, event_id, type") // Ajoute d'autres champs si nécessaire
      .in("event_id", eventIds); // Filtre par événements
  
    if (actionsError) {
      console.error("Erreur lors de la récupération des actions :", actionsError.message);
      return;
    }
  
    // Regrouper les actions par événement
    const actionsByEvent = eventIds.reduce((acc, eventId) => {
      acc[eventId] = {
        immediateActions: actionsData.filter(
          (action) => action.event_id === eventId && action.type === "immediate"
        ),
        correctiveActions: actionsData.filter(
          (action) => action.event_id === eventId && action.type === "corrective"
        ),
      };
      return acc;
    }, {});
  
    // Ajouter les actions à chaque événement
    const enrichedEvents = eventsData.map((event) => ({
      ...event,
      immediateActions: actionsByEvent[event.id]?.immediateActions || [],
      correctiveActions: actionsByEvent[event.id]?.correctiveActions || [],
    }));
  
    setEvents(enrichedEvents);
  };
  

  useFocusEffect(
    useCallback(() => {
      fetchUsersAdmin();
    }, [])
  );

  // Filtrer les événements en fonction de la recherche et des filtres
  const filteredEvents = events?.filter((event) => {
    const matchesSearch = event.wording.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilters = 
      (!selectedFilters.immediateActions || event.immediateActions.length > 0) &&
      (!selectedFilters.causes || (event.causes && event.causes !== "")) &&
      (!selectedFilters.correctiveActions || event.correctiveActions.length > 0);
    
    return matchesSearch && matchesFilters;
  });

  return (
    <View style={{ flex: 1, alignItems: "center" }}>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={{ fontWeight: "bold" }}>Créer un nouvel évènement</Text>
          <TouchableOpacity
            onPress={() => router.push("/event/createEvent")}
          >
            <Octicons name="diff-added" size={25} color="#F99527" />
          </TouchableOpacity>
        </View>

        {/* Barre de recherche et filtres */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputWrapper}>
            <FontAwesome
              name="search"
              size={24}
              color="#3C3c4399"
              style={styles.searchIcon}
            />
            <TextInput
              style={styles.searchInput}
              placeholder="Rechercher un événement..."
              placeholderTextColor="#3C3c4399"
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            <TouchableOpacity
              style={styles.filterButton}
              onPress={() => setShowFilters(!showFilters)}
            >
              <FontAwesome
                name="filter"
                size={24}
                color="#F99527"
              />
            </TouchableOpacity>
          </View>

          {/* Panneau des filtres */}
          {showFilters && (
            <View style={styles.filtersPanel}>
              <View style={styles.filterOption}>
                <TouchableOpacity
                  style={[
                    styles.filterCircle,
                    selectedFilters.immediateActions && styles.filterCircleActive,
                  ]}
                  onPress={() => setSelectedFilters(prev => ({
                    ...prev,
                    immediateActions: !prev.immediateActions
                  }))}
                />
                <Text style={styles.filterLabel}>Actions immédiates</Text>
              </View>
              <View style={styles.filterOption}>
                <TouchableOpacity
                  style={[
                    styles.filterCircle,
                    selectedFilters.causes && styles.filterCircleActive,
                  ]}
                  onPress={() => setSelectedFilters(prev => ({
                    ...prev,
                    causes: !prev.causes
                  }))}
                />
                <Text style={styles.filterLabel}>Causes identifiées</Text>
              </View>
              <View style={styles.filterOption}>
                <TouchableOpacity
                  style={[
                    styles.filterCircle,
                    selectedFilters.correctiveActions && styles.filterCircleActive,
                  ]}
                  onPress={() => setSelectedFilters(prev => ({
                    ...prev,
                    correctiveActions: !prev.correctiveActions
                  }))}
                />
                <Text style={styles.filterLabel}>Actions correctives</Text>
              </View>
            </View>
          )}
        </View>

        <FlatList
          data={filteredEvents}
          keyExtractor={(item) => `event-${item.id}`}
          renderItem={({ item }) => (
            <View style={styles.itemContainer}>
              <PaperInfoImgEvent
                title={item.wording.length > 30 ? item.wording.slice(0, 30) + "..." : item.wording}
                text1={getEventTypeLabel(item.type)}
                text2={item.description ? (item.description.length > 90 ? item.description.slice(0, 90) + "..." : item.description) : "Pas de description"}
                circle1={item.immediateActions.length > 0}
                circle2={item.causes !== "" && item.causes !== null}
                circle3={item.correctiveActions.length > 0}
                imgSrc={item?.file?.length ? item.file[0] : ""}
                onPressPen={() =>
                  router.push({
                    pathname: "/event/updateEvent",
                    params: {
                      date: item.date,
                      wording: item.wording,
                      causes: item.causes,
                      idEvent: item.id,
                      description: item.description,
                    },
                  })
                }
                onPressEye={() =>
                  router.push({
                    pathname: "/event/detailsEvent",
                    params: {
                      date: item.date,
                      wording: item.wording,
                      causes: item.causes,
                      idEvent: item.id,
                      description: item.description,
                    },
                  })
                }
              />
            </View>
          )}
          contentContainerStyle={{ paddingBottom: 500 }}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={<Text>Aucun événement disponible</Text>}
          ItemSeparatorComponent={() => <View style={styles.separator} />}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    gap: 10,
    paddingHorizontal: 10,
    maxWidth: 800,
    width: "100%",
    alignSelf: "center",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 10,
    marginTop: 14,
  },
  searchContainer: {
    width: "100%",
    marginBottom: 20,
    marginTop: 10,
    paddingHorizontal: 10,
  },
  searchInputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1C314414",
    borderRadius: 8,
    paddingHorizontal: 10,
    height: 60,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    padding: 10,
    fontSize: 16,
    color: "#000000",
  },
  filterButton: {
    padding: 10,
  },
  filtersPanel: {
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    padding: 15,
    marginTop: 10,
    ...Platform.select({
      web: {
        boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
      },
      default: {
        elevation: 3,
      },
    }),
  },
  filterOption: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 10,
  },
  filterCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: "#F99527",
    marginRight: 10,
  },
  filterCircleActive: {
    backgroundColor: "#F99527",
  },
  filterLabel: {
    fontSize: 16,
    color: "#262627",
  },
  itemContainer: {
    marginBottom: 10,
  },
  separator: {
    height: 1,
  },
});
