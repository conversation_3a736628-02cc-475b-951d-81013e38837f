import ContainedButton from "@/components/ui/buttons/ContainedButton";
import OutlinedButton from "@/components/ui/buttons/OutlinedButton";
import { useRouter } from "expo-router";
import { StyleSheet, Image, View, Text } from "react-native";

export default function PreLogin() {
  const router = useRouter();

  return (
    <View style={styles.container}>
      <View style={styles.containerContent}>
        <View style={styles.imgText}>
          <Image
            source={require("@/assets/images/login/isLoadingPage.png")}
            style={styles.imgIsLoadingPage}
          />
          <Text
            style={{
              color: "white",
              textAlign: "center",
              marginHorizontal: 40,
            }}
          >
            Le premier système QHSE intelligent qui tient dans une poche
          </Text>
        </View>
        <View style={styles.buttons}>
          <ContainedButton
            label="S'inscrire"
            backgroundColor="#F99527"
            onPress={() => router.navigate("/register")}
          />
          <OutlinedButton
            label="Déjà un compte ?"
            borderColor="white"
            onPress={() => router.navigate("/login")}
          />
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: "100%",
    justifyContent: "center", // Centre les enfants verticalement
    alignItems: "center", // Centre les enfants horizontalement
    backgroundColor: "#1C3144",
  },
  containerContent: {
    width: "100%",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center", // Ajoute une centration verticale à ce conteneur
    gap: 40, // Ajuste l'espacement entre les sections pour une meilleure esthétique
  },
  imgText: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "80%",
  },
  titleContainer: {
    flexDirection: "row",
    gap: 8,
  },
  imgIsLoadingPage: {
    height: 178,
    width: 290,

    // bottom: 0,
    // left: 0,
    // position: "absolute",
  },
});
