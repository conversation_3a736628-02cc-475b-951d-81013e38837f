/*
components/infoSection/policy.tsx

Composant pour afficher la politique de l'entreprise.
*/

import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Platform,
  ViewStyle,
  TextStyle,
} from "react-native";
import { useRouter, usePathname } from "expo-router";
import PaperInfoImg from "../common/paperInfoImg";
import { Spinner } from "../ui/spinner";
import TypePolicy from "@/types/typePolicies";
import PaperCompany from "../common/paperCompany";

type typeContextsProps = {
  icon?: any;
  data: TypePolicy[];
  isLanding?: boolean;
};

export default function PoliciesInfoSection({
  icon,
  data,
  isLanding,
}: typeContextsProps) {
  const router = useRouter();
  const path = usePathname();

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Politique</Text>
        <TouchableOpacity onPress={() => router.navigate("/info/policy")}>
          {icon ? (
            icon
          ) : (
            <Text style={styles.seeMore}>Voir plus</Text>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView 
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {isLanding ? (
          <View style={styles.spinnerContainer}>
            <Spinner size="small" color="#1C3144" />
          </View>
        ) : data?.length > 0 ? (
          (path === "/Informations" || path === "/menu/itemsMenuCockpit/info"
            ? data?.slice(-2)
            : data
          ).map((item, index) => (
            <PaperCompany
              key={index}
              title="Politique de l'entreprise"
              text1={item.context}
            />
          ))
        ) : (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>Aucune politique d'entreprise.</Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: "100%",
  } as ViewStyle,
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#E6E6E6",
  } as ViewStyle,
  title: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1C3144",
  } as TextStyle,
  seeMore: {
    color: "#F99527",
    textDecorationLine: "underline",
    fontFamily: "Poppins",
    fontWeight: "bold",
  } as TextStyle,
  content: {
    flex: 1,
  } as ViewStyle,
  scrollContent: {
    paddingVertical: 16,
    ...(Platform.OS === 'web' ? {
      maxWidth: 1200,
      marginHorizontal: 'auto',
      width: '100%',
      paddingHorizontal: 16,
    } : {}),
  } as ViewStyle,
  spinnerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  } as ViewStyle,
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  } as ViewStyle,
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  } as TextStyle,
});
