import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  Platform
} from "react-native";
import { useRouter, useLocalSearchParams, useFocusEffect } from "expo-router";
import { useCallback, useContext, useState } from "react";
import { Octicons } from "@expo/vector-icons";
import { AppContext } from "@/state/AppContext";
import PaperInfo from "@/components/common/paperInfo";
import { supabase } from "@/lib/supabase";
import Header from "@/components/common/Header";
import Paper from "@/components/common/paper";
import { Spinner } from "@/components/ui/spinner";

export default function DetailsUser() {
  const { user } = useContext(AppContext);
  const [userDetails, setUserDetails] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { user_id, user_name } = useLocalSearchParams();

  const fetchData = async () => {
    setIsLoading(true);
    console.log("Fetching data for user_id:", user_id);
    
    const { data: userData, error } = await supabase
      .from("users")
      .select("*")
      .eq("id", user_id)
      .single();

    console.log("User data:", userData);
    console.log("Error:", error);

    if (!error && userData) {
      setUserDetails(userData);
    } else {
      console.error("Error fetching user data:", error);
    }
    setIsLoading(false);
  };

  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [])
  );

  return (
    <View style={{ flex: 1 }}>
      <Header
        title="Informations"
        onPressFlesh={() => router.back()}
        onPressIcon={() => 
          router.push({
            pathname: "/info/updateUser",
            params: { id: user_id }
          })
        }
      />
      <View style={styles.mainContainer}>
        <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
          <View style={styles.containerContent}>
            <View style={styles.registerInfo}>
              {userDetails && (
                <Paper data={[
                  { label: "Nom", value: userDetails.last_name },
                  { label: "Prénom", value: userDetails.first_name },
                  { label: "Email", value: userDetails.email },
                  { label: "Téléphone", value: userDetails.phone_number || "Non renseigné" },
                  { label: "Rôle", value: userDetails.profil }
                ]} />
              )}

              <View style={{ gap: 10, width: "100%" }}>
                <View style={styles.header}>
                  <Text style={{ fontWeight: "bold" }}>Photo de profil</Text>
                </View>
                <View style={{ width: "100%" }}>
                  {isLoading ? (
                    <Spinner size="small" color="#1C3144" />
                  ) : userDetails ? (
                    <View style={{ gap: 10, alignItems: "center" }}>
                      {userDetails.profil_picture ? (
                        <Image
                          source={{ uri: userDetails.profil_picture }}
                          style={styles.profileImage}
                        />
                      ) : (
                        <View style={[styles.profileImage, styles.noImage]}>
                          <Text style={{ color: "#666" }}>Aucune photo</Text>
                        </View>
                      )}
                    </View>
                  ) : (
                    <Text>Aucune information disponible</Text>
                  )}
                </View>
              </View>

              <TouchableOpacity 
                style={styles.editButton}
                onPress={() => 
                  router.push({
                    pathname: "/info/updateUser",
                    params: { id: user_id }
                  })
                }
              >
                <Text style={styles.editButtonText}>Modifier les informations</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    alignItems: 'center',
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  contentContainer: {
    flexGrow: 1,
  },
  containerContent: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    width: "100%",
    gap: 40,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
  },
  profileImage: {
    width: 200,
    height: 200,
    borderRadius: 100,
    marginVertical: 20,
  },
  noImage: {
    backgroundColor: "#f0f0f0",
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#ddd",
  },
  editButton: {
    backgroundColor: "#F99527",
    padding: 15,
    borderRadius: 8,
    width: "100%",
    alignItems: "center",
    marginTop: 20,
  },
  editButtonText: {
    color: "white",
    fontWeight: "bold",
    fontSize: 16,
  },
}); 