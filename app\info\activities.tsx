/*
app/info/activities.tsx

Liste des activités.
*/

import { StyleSheet, ScrollView, View, TextInput } from "react-native";
import { useCallback, useContext, useEffect, useState } from "react";
import { AppContext } from "@/state/AppContext";
import { FontAwesome, Octicons } from "@expo/vector-icons";
import { useFocusEffect, useRouter } from "expo-router";
import { supabase } from "@/lib/supabase";
import TypeActivites from "@/types/typeActivity";
import ActivitiesInfoSection from "@/components/infoSection/activities";

export default function Activities() {
  const router = useRouter();
  const [activities, setActivites] = useState<TypeActivites[] | null>(null);
  const { user } = useContext(AppContext);
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredDataList, setFilteredDataList] = useState<any>([]);

  useEffect(() => {
    const filteredList = activities?.filter((item) =>
      `${item.activity_name}`.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredDataList(filteredList);
  }, [searchQuery, activities]);

  const fetchData = async () => {
    const { data, error } = await supabase
      .from("activities")
      .select()
      .eq("uid_admin", user?.uid);

    if (!error) {
      setActivites(data || null);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [])
  );

  return (
    <ScrollView>
      {/* Barre de recherche */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputWrapper}>
          <FontAwesome
            name="search"
            size={24}
            color="#3C3c4399"
            style={styles.searchIcon}
          />
          <TextInput
            style={styles.searchInput}
            placeholder="Rechercher par nom..."
            placeholderTextColor="#3C3c4399"
            value={searchQuery}
            onChangeText={(text) => setSearchQuery(text)}
          />
        </View>
      </View>
      
      <ActivitiesInfoSection
        icon={
          <Octicons
            name="diff-added"
            size={24}
            color="#F99527"
            onPress={() => router.navigate("/info/createActivity")}
          />
        }
        data={filteredDataList as any}
      />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  searchContainer: {
    width: "100%",
    marginBottom: 20,
    marginTop: 10,
    paddingHorizontal: 10,
  },
  searchInputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1C314414",
    borderRadius: 8,
    paddingHorizontal: 10,
    height: 60,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    padding: 10,
    fontSize: 16,
    color: "#000000",
  },
});
