/*
app/(tabs)/index.tsx

Page d'accueil de l'application.

Informations pertinentes :

- On récupère et affiche le dernier Flash QSE de l'entreprise depuis la table `flash_qse`
- On affiche tous les événements de l'entreprise depuis la table `events`
- On récupère les constats de l'entreprise de la table `observations` qui ont un type :
  -> `non_compliant`
  -> `sensitive_point`
*/

import {
  FlatList,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Platform,
  ActivityIndicator,
} from "react-native";
import { AppContext } from "@/state/AppContext";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { ParamListBase } from "@react-navigation/native";
import { useRouter } from "expo-router";
import { supabase } from "@/lib/supabase";
import { useFocusEffect } from "@react-navigation/native";
import React, { useContext, useState, useEffect } from "react";
import PaperInfoImgEvent from "@/components/common/paperInfoImgEvent";
import TypeEvent from "@/types/typeEvent2";
import moment from "moment";

// Ajout de la fonction de conversion type vers label
const getEventTypeLabel = (typeValue: string): string => {
  const typesEvent = [
    { label: "Non conformité - Fournisseur", value: "supplier" },
    { label: "Audit - Non-conformité (code NC)", value: "non_compliant" },
    { label: "Audit - Point sensible (code PS)", value: "sensitive_point" },
    { label: "Situation dangereuse (code SD)", value: "dangerous_situation" },
    { label: "Accident du travail (code AT)", value: "work_accident" },
    { label: "Presqu'accident (code PAT)", value: "near_miss" },
    { label: "Malfaçon produit ou service (code NQ)", value: "product_defect" },
    { label: "Danger environnemental (code DE)", value: "environmental_danger" },
    { label: "Accident environnemental (code AE)", value: "environmental_accident" },
    { label: "Réclamation client (code REC)", value: "customer_complaint" },
    { label: "Risque (code RI)", value: "risk" },
    { label: "Opportunité (code OPP)", value: "opportunity" },
    { label: "Objectif (code OBJ)", value: "objective" },
    { label: "Partie intéressée pertinente (code PIP)", value: "interested_party" },
    { label: "Matériel - Défaut (code MAT)", value: "material_defect" },
    { value: "audit_report", label: "Constat d'audit (code CO)", ActionImmediate: "OUI" },
  ];

  const type = typesEvent.find(t => t.value === typeValue);
  return type ? type.label : typeValue;
};

export default function HomeScreen() {
  const { user, companySelected } = useContext(AppContext);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const [events, setEvents] = useState<TypeEvent[] | null>(null);
  const router = useRouter();
  const [lastFlashQSE, setLastFlashQSE] = useState<any>(null);

  useEffect(() => {
    if (!user) {
      router.replace('/pre-login');
    }
  }, [user]);

  // Récupérer le dernier Flash QSE de l'entreprise
  useFocusEffect(
    React.useCallback(() => {
      if (!user) return;

      const fetchLastFlashQSE = async () => {
        try {
          const companyId = companySelected?.id;
          // Récupérer le dernier Flash QSE de l'entreprise de l'utilisateur
          const { data, error } = await supabase
            .from("flash_qse")
            .select("*")
            .eq("uid_company", companyId)
            .order('created_at', { ascending: false })
            .limit(1);

          if (error) {
            console.error(
              "Erreur lors de la récupération du Flash QSE :",
              error.message
            );
            return;
          }

          setLastFlashQSE(data?.[0] || null);
        } catch (err) {
          console.error("Erreur inattendue :", err);
        }
      };

      const fetchUsersAdmin = async () => {
        if (!user) return;
        const companyId = companySelected?.id;
      
        // Récupérer les événements
        const { data: eventsData, error: eventsError } = await supabase
          .from("events")
          .select("*")
          .eq("company_id", companyId)
          .order("created_at", { ascending: false })
          .limit(5);
          
        if (eventsError) {
          console.error("Erreur lors de la récupération des événements :", eventsError.message);
          return;
        }
      
        // Récupérer les actions associées aux événements
        const eventIds = eventsData.map((event: any) => event.id);
        const { data: actionsData, error: actionsError } = await supabase
          .from("event_actions")
          .select("id, event_id, type")
          .in("event_id", eventIds);
      
        if (actionsError) {
          console.error("Erreur lors de la récupération des actions :", actionsError.message);
          return;
        }
      
        // Regrouper les actions par événement
        const actionsByEvent = eventIds.reduce((acc: any, eventId: any) => {
          acc[eventId] = {
            immediateActions: actionsData.filter(
              (action: any) => action.event_id === eventId && action.type === "immediate"
            ),
            correctiveActions: actionsData.filter(
              (action: any) => action.event_id === eventId && action.type === "corrective"
            ),
          };
          return acc;
        }, {});
      
        // Ajouter les actions à chaque événement
        const enrichedEvents = eventsData.map((event:any) => ({
          ...event,
          immediateActions: actionsByEvent[event.id]?.immediateActions || [],
          correctiveActions: actionsByEvent[event.id]?.correctiveActions || [],
        }));
      
        setEvents(enrichedEvents);
      };

      fetchLastFlashQSE();
      fetchUsersAdmin();
    }, [user])
  );

  if (!user) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#F99527" />
      </View>
    );
  }

  return (
    <View style={{ flex: 1, alignItems: "center", backgroundColor: "#F5F5F5" }}>
      <View style={styles.container}>
        {/* Conteneur dernier Flash QSE */}
        <View style={styles.cardWrapper}>
          <View style={styles.sectionTitleContainer}>
            <Text style={styles.sectionTitle}>Dernier Flash QSE</Text>
          </View>
          {lastFlashQSE ? (
            // Card Flash QSE dynamique
            <TouchableOpacity
              onPress={() => navigation.navigate("screens/notifications/FlashQSEList")}
              style={styles.flashCard}
            >
              {/* Image */}
              <Image
                source={{
                  uri: lastFlashQSE.cover || "https://picsum.photos/200/300",
                }}
                style={styles.flashCardImage}
                resizeMode="cover"
                onError={(e) => console.log("Erreur de chargement de l'image:", e.nativeEvent.error)}
              />

              {/* Textes */}
              <View style={styles.flashCardTexts}>
                <Text style={styles.flashCardTitle} numberOfLines={1} ellipsizeMode="tail">
                  {lastFlashQSE.name}
                </Text>
                <Text style={styles.flashCardName} numberOfLines={2} ellipsizeMode="tail">
                  {lastFlashQSE.context || "Description non disponible."}
                </Text>
                <Text style={styles.flashCardDate}>
                  {moment(lastFlashQSE.created_at).format("DD/MM/YYYY")}
                </Text>
              </View>
            </TouchableOpacity>
          ) : (
            // Message si aucun Flash QSE
            <TouchableOpacity
              style={styles.createFlashQSEButton}
              onPress={() => navigation.navigate("screens/notifications/FlashQSEList")}
            >
              <Text style={styles.createFlashQSEButtonText}>
                Créer un Flash QSE
              </Text>
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.titleWrapper}>
          <Text style={styles.title}>Les évènements QSE</Text>
        </View>
      </View>
      <View style={{ flex: 1, gap: 10, paddingHorizontal: 10, maxWidth: 800, width: "100%", alignSelf: "center", backgroundColor: "#F5F5F5" }}>
        <FlatList
          data={events}
          keyExtractor={(item) => `event-${item.id}`}
          renderItem={({ item }) => (
            <View style={styles.itemContainer}>
              <PaperInfoImgEvent
                title={item.wording.length > 30 ? item.wording.slice(0, 30) + "..." : item.wording}
                text1={getEventTypeLabel(item.type)}
                text2={item.description ? (item.description.length > 90 ? item.description.slice(0, 90) + "..." : item.description) : "Pas de description"}
                circle1={item.immediateActions.length > 0}
                circle2={item.causes !== "" && item.causes !== null}
                circle3={item.correctiveActions.length > 0}
                imgSrc={item?.file?.length ? item.file[0] : ""}
                onPressPen={() => router.push({
                  pathname: "/event/updateEvent",
                  params: {
                    date: item.date,
                    wording: item.wording,
                    causes: item.causes,
                    idEvent: item.id,
                    description: item.description,
                  },
                })}
                onPressEye={() => router.push({
                  pathname: "/event/detailsEvent",
                  params: {
                    date: item.date,
                    wording: item.wording,
                    causes: item.causes,
                    idEvent: item.id,
                    description: item.description,
                  },
                })} />
            </View>
          )}
          contentContainerStyle={{ paddingBottom: 200 }}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={<Text style={styles.emptyText}>Aucun événement disponible</Text>}
          ItemSeparatorComponent={() => <View style={styles.separator} />} />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    gap: 10,
    paddingHorizontal: 10,
    maxWidth: 800,
    width: "100%",
    alignSelf: "center",
    backgroundColor: "#F5F5F5",
  },
  titleWrapper: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    marginVertical: 15,
  },
  title: {
    color: "#000000",
    fontSize: 14,
    textAlign: "left",
    fontWeight: "bold",
  },
  cardWrapper: {
    width: "100%",
    backgroundColor: "#F5F5F5",
    alignItems: "center",
    justifyContent: "center",
    marginVertical: 15,
  },
  sectionTitleContainer: {
    width: "100%",
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#262627",
    marginBottom: 5,
  },
  flashCard: {
    width: "100%",
    height: 120,
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    overflow: "hidden",
    flexDirection: "row",
    marginBottom: 15,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: 2,
    borderColor: "#f99527",
  },
  flashCardImage: {
    width: Platform.OS === 'web' ? 120 : 140,
    height: "100%",
    backgroundColor: "#f5f5f5",
    borderTopLeftRadius: 10,
    borderBottomLeftRadius: 10,
  },
  flashCardTexts: {
    flex: 1,
    padding: Platform.OS === 'web' ? 12 : 16,
    justifyContent: "space-between",
  },
  flashCardTitle: {
    fontSize: Platform.OS === 'web' ? 16 : 15,
    fontWeight: "600",
    color: "#262627",
    marginBottom: Platform.OS === 'web' ? 4 : 8,
  },
  flashCardName: {
    fontSize: Platform.OS === 'web' ? 14 : 13,
    color: "#525252",
    lineHeight: Platform.OS === 'web' ? 20 : 18,
    marginBottom: Platform.OS === 'web' ? 0 : 8,
  },
  flashCardDate: {
    fontSize: Platform.OS === 'web' ? 12 : 11,
    color: "#888888",
    marginTop: Platform.OS === 'web' ? 4 : 8,
  },
  createFlashQSEButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    marginBottom: 16,
    backgroundColor: "#f99527",
    marginTop: 10,
  },
  createFlashQSEButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  itemContainer: {
    marginBottom: 10,
  },
  separator: {
    height: 10,
  },
  emptyText: {
    textAlign: "center",
    marginTop: 20,
    fontSize: 14,
    color: "#525252",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
});


