/*
app/info/context.tsx

Affichage du contexte créé.
*/

import {
  StyleSheet,
  ScrollView,
  View,
  TextInput,
  TouchableOpacity,
} from "react-native";
import { useCallback, useContext, useEffect, useState } from "react";
import { AppContext } from "@/state/AppContext";
import { FontAwesome, Octicons } from "@expo/vector-icons";
import { useFocusEffect, useRouter } from "expo-router";
import { supabase } from "@/lib/supabase";
import TypeContext from "@/types/typeContext";
import ContextsInfoSection from "@/components/infoSection/context";
import AntDesign from '@expo/vector-icons/AntDesign';

export default function Contexts() {
  const router = useRouter();
  const [contexts, setContexts] = useState<TypeContext[] | null>(null);
  const { user } = useContext(AppContext);
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredDataList, setFilteredDataList] = useState<any>([]);

  // Récupérer le contexte
  useEffect(() => {
    const filteredList = contexts?.filter((item) =>
      `${item.history}`.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredDataList(filteredList);
  }, [searchQuery, contexts]);

  // Récupérer le contexte
  const fetchData = async () => {
    const { data, error } = await supabase
      .from("contexts")
      .select()
      .eq("uid_admin", user?.uid);

    if (!error) {
      setContexts(data || null);
    }
  };

  // Récupérer le contexte
  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [])
  );

  return (
    <ScrollView>
      {/* Barre de recherche */}
      {/* <View style={styles.searchContainer}>
        <View style={styles.searchInputWrapper}>
          <FontAwesome
            name="search"
            size={24}
            color="#3C3c4399"
            style={styles.searchIcon}
          />
          <TextInput
            style={styles.searchInput}
            placeholder="Rechercher par nom..."
            placeholderTextColor="#3C3c4399"
            value={searchQuery}
            onChangeText={(text) => setSearchQuery(text)}
          />
        </View>
      </View> */}

      <ContextsInfoSection
        icon={
          !contexts || contexts.length === 0 ? ( // Vérifie s'il n'y a pas encore de contexte
            <Octicons
              name="diff-added"
              size={24}
              color="#F99527"
              onPress={() => router.navigate("/info/createContext")}
            />
          ) : (
            <AntDesign
              name="sync"
              size={24}
              color="#F99527"
              onPress={() => router.navigate("/info/updateContext")}
            />
          )
        }
        data={filteredDataList as any}
      />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  searchContainer: {
    width: "100%",
    marginBottom: 20,
    marginTop: 10,
    paddingHorizontal: 10,
  },
  searchInputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1C314414",
    borderRadius: 8,
    paddingHorizontal: 10,
    height: 60,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    padding: 10,
    fontSize: 16,
    color: "#000000",
  },
});
