/*
components/infoSection/context.tsx

Composant pour afficher le contexte de l'entreprise.
*/

import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  Platform,
} from "react-native";
import { usePathname } from "expo-router";
import { Spinner } from "../ui/spinner";
import TypeContext from "@/types/typeContext";
import PaperCompany from "../common/paperCompany";

type typeContextsProps = {
  data: TypeContext[];
  isLanding?: boolean;
};

export default function ContextsInfoSection({
  data,
  isLanding,
}: typeContextsProps) {
  const path = usePathname();

  return (
    <View style={styles.container}>
      <ScrollView 
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {isLanding ? (
          <View style={styles.spinnerContainer}>
            <Spinner size="small" color="#1C3144" />
          </View>
        ) : data?.length > 0 ? (
          (path === "/Informations" || path === "/menu/itemsMenuCockpit/info"
            ? data?.slice(-2)
            : data
          ).map((item, index) => (
            <PaperCompany
              key={index}
              title={item.context}
              text1={item.context}
              text2={item.organisation}
              text3={item.website}
              text4={item.history}
            />
          ))
        ) : (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>Aucun contexte d'entreprise.</Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: "100%",
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingVertical: 8,
    ...(Platform.OS === 'web' && {
      maxWidth: 1200,
      marginHorizontal: 'auto',
      display: 'flex',
      flexDirection: 'column',
      gap: 16,
    }),
  },
  spinnerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});
