/*
lib/uploadFlashSQECoverStorage.ts

Fonction pour envoyer les images des Flash QSE vers le Storage Supabase.
*/

import Toast from "react-native-toast-message";
import { supabase } from "./supabase.native";

export default async function uploadPhoto(fileUri: string, userId: string) {
  try {
    // Lire le fichier localement
    const response = await fetch(fileUri);
    const blob = await response.blob();

    // Générer un nom de fichier unique (ex: `userId-timestamp.jpg`)
    const fileName = `${userId}_${Date.now()}_flash_qse.jpeg`;

    const arrayBuffer = await new Response(blob).arrayBuffer();
    const { data: dataPicture, error } = await supabase.storage
      .from("images")
      .upload(`flash_qse/${fileName}`, arrayBuffer, {
        contentType: "image/jpeg",
        upsert: false, // Permet d'écraser un fichier existant
      });

    if (error) {
      console.error("Upload error:", error);
      Toast.show({
        type: "error",
        text1: "Une erreur est survenue lors de l'upload",
        text1Style: { color: "#1C3144" },
      });
      return null;
    }

    // Générer une URL signée pour accéder au fichier

    if (dataPicture.id) {
      const { data } = await supabase.storage
        .from("images")
        .getPublicUrl(`flash_qse/${fileName}`);

      if (error) {
        console.error("Signed URL error:", error);

        Toast.show({
          type: "error",
          text1: "Une erreur est survenue",
          text1Style: { color: "#1C3144" },
        });
        return null;
      }
      return { url: data?.publicUrl, idPicture: dataPicture?.id }; // Retourne l'URL signée
    }
  } catch (err) {
    console.error("Error uploading file:", err);
    Toast.show({
      type: "error",
      text1: "Une erreur est survenue lors de l'upload",
      text1Style: { color: "#1C3144" },
    });
    return null;
  }
}
