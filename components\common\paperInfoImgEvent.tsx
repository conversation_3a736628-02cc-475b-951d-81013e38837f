/*
components/common/paperInfoImgEvent.tsx
*/

import React, { useContext } from "react";
import { View, Text, StyleSheet, Image, TouchableOpacity } from "react-native";
import { Badge, BadgeText } from "../ui/badge";
import FontAwesome5 from "@expo/vector-icons/FontAwesome5";
import { MaterialCommunityIcons, MaterialIcons } from "@expo/vector-icons";
import FontAwesome from "@expo/vector-icons/FontAwesome";
import Ionicons from "@expo/vector-icons/Ionicons";

type typePropPaper = {
  imgSrc?: string;
  title?: string;
  badge?: string;
  text1?: string;
  text2?: string;
  text3?: string;
  text4?: string;
  circle1?: boolean;
  circle2?: boolean;
  circle3?: boolean;
  onPressPen?: any;
  onPressEye?: any;
};

const PaperInfoImgEvent = ({
  imgSrc = "",
  title,
  text1,
  text2,
  circle1,
  circle2,
  circle3,
  onPressPen,
  onPressEye,
}: typePropPaper) => {
  return (
    <TouchableOpacity style={styles.container} onPress={onPressEye}>
      <View style={styles.containerImgInfo}>
        {imgSrc ? (
          <Image source={{ uri: imgSrc }} style={styles.img} />
        ) : (
          <Image
            source={require("@/assets/images/imgDefaultEvent.png")}
            style={styles.img}
          />
        )}
        <View style={styles.containerInfo}>
          <Text style={{ fontWeight: "bold" }}>{title}</Text>

          {text1 && <Text style={{ color: "#525252", fontSize: 11 }}>{text1}</Text>}
          {text2 && <Text style={{ color: "#525252" }}>{text2}</Text>}

        </View>
      </View>
      <View style={styles.containerBottom}>
        <View style={{ flexDirection: "row", gap: 10 }}>
          <TouchableOpacity style={styles.download} onPress={onPressEye}>
            <Ionicons name="eye-outline" size={20} color="black" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.download} onPress={onPressPen}>
            <MaterialCommunityIcons
              name="file-document-edit-outline"
              size={20}
              color="black"
            />
          </TouchableOpacity>
        </View>
        <View style={{ flexDirection: "row", gap: 10 }}>
          <FontAwesome
            name="circle"
            size={24}
            color={circle1 ? "#2FC12B" : "#A5A3A3"}
          />
          <FontAwesome
            name="circle"
            size={24}
            color={circle2 ? "#2FC12B" : "#A5A3A3"}
          />
          <FontAwesome
            name="circle"
            size={24}
            color={circle3 ? "#2FC12B" : "#A5A3A3"}
          />
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: "#E6E6E6",
    borderRadius: 10,
    padding: 10,
    gap: 10,
    // width: "95%",
    // Ombre pour iOS
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 5,
    // Ombre pour Android
    elevation: 5,
  },
  img: {
    width: 80,
    height: 80,
    borderRadius: 10,
  },
  containerImgInfo: {
    gap: 10,
    flexDirection: "row",
    // padding: 10,
    // alignItems: "center",
  },
  containerInfo: {
    gap: 5,
    flexShrink: 1, // Permet au texte de ne pas dépasser
    maxWidth: "95%", // Ajuste la largeur max
  },
  containerBottom: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
  },
  download: {
    borderRadius: 8,
    borderStyle: "solid",
    borderWidth: 1,
    borderColor: "#F99527",
    padding: 5,
  },
});

export default PaperInfoImgEvent;
