/*
components/common/Header.tsx
*/

import React, { useContext, useEffect } from "react";
import { View, Text, StyleSheet, Image, TouchableOpacity, Linking, Platform } from "react-native";
import Octicons from "@expo/vector-icons/Octicons";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import Ionicons from "@expo/vector-icons/Ionicons";
import { usePathname, useRouter } from "expo-router";
import { Menu, MenuItem, MenuItemLabel } from "../ui/menu";
import { AppContext } from "@/state/AppContext";
import Feather from "@expo/vector-icons/Feather";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { FontAwesome } from "@expo/vector-icons";
import { useSupabase } from "@/lib/useSupabase";
import * as WebBrowser from 'expo-web-browser';
import { Link } from "expo-router";
import { supabase } from '@/lib/supabase';
import { useToast } from "@/context/ToastContext";

const tabs = [
  // ------------- Menu -------------
  {
    path: "/Objectifs",
    title: "Objectifs",
    icon: <Ionicons name="filter-sharp" size={25} color="black" />,
  },
  {
    path: "/menu/maintenance",
    title: "Produits",
    icon: <Ionicons name="filter-sharp" size={25} color="black" />,
  },
  {
    path: "/Risques",
    title: "Risques",
    // icon: <Ionicons name="filter-sharp" size={25} color="black" />,
  },
  {
    path: "/Informations",
    title: "Informations",
  },
  // {
  //   path: "/Carto",
  //   title: "E-learning",
  // },
  {
    path: "/risk/detailsRisk",
    title: "Risque concerné",
    icon: <Feather name="edit" size={24} color="black" />,
  },
  {
    path: "/risk/createRisk",
    title: "Créer un risque",
  },
  {
    path: "/risk/updateRisk",
    title: "Modifier un risque",
  },
  {
    path: "/PIP",
    title: "Parties intéressées",
  },
  {
    path: "/menu/itemsMenuCockpit/pip",
    title: "Parties intéressées",
  },
  {
    path: "/menu/itemsMenuCockpit/risk",
    title: "Risques",
  },
  {
    path: "/menu/itemsMenuCockpit/info",
    title: "Informations",
  },
  {
    path: "/menu/itemsMenuCockpit/objectif",
    title: "Objectifs",
    icon: <Ionicons name="filter-sharp" size={25} color="black" />,
  },
  {
    path: "/Documents",
    title: "Mes documents",
  },
  {
    path: "/menu/itemsMenuDefault/equipment",
    title: "Équipements",
    icon: <Ionicons name="filter-sharp" size={25} color="black" />,
  },
  {
    path: "/Equipements",
    title: "Équipements",
    // icon: <Ionicons name="filter-sharp" size={25} color="black" />,
  },
  {
    path: "/Action",
    title: "Mes actions",
  },
  {
    path: "/Audit",
    title: "Mes audits",
  },
  // ------------- Actions -------------
  {
    path: "/screens/actions/ActionDetails",
    title: "Action",
    icon: <FontAwesome name="pencil-square-o" size={35} color="black" />,
  },
  {
    path: "/event/createActionEvent",
    title: "Créer une action",
  },
  {
    path: "/screens/actions/CreateAction",
    title: "Créer une action",
  },
  {
    path: "/screens/actions/InProgressActionsList",
    title: "Mes actions",
  },
  {
    path: "/screens/actions/LateActionsList",
    title: "Mes actions",
  },
  {
    path: "/screens/actions/RegularActionsList",
    title: "Mes actions",
  },
  {
    path: "/screens/actions/UpdateAction",
    title: "Modifier l'action",
  },
  {
    path: "/screens/actions/RegularActionsList",
    title: "Mes actions",
  },
  // ------------- Événements -------------
  {
    path: "/event",
    title: "Événement QSE",
    icon: <Octicons name="diff-added" size={35} color="black" />,
  },
  {
    path: "/Evenements",
    title: "Évènements QSE",
  },
  {
    path: "/event/updateEvent",
    title: "Modifier l'évènement",
  },
  {
    path: "/event/createEvent",
    title: "Créer un évènement",
  },
  {
    path: "/event/eventSpecific",
    title: "Mes événements",
  },
  {
    path: "/event/eventsCompany",
    title: "Les événements",
  },
  // ------------- Flash QSE -------------
  {
    path: "/screens/notifications/FlashQSEDetails",
    title: "Flash QSE",
    icon: <FontAwesome name="pencil-square-o" size={35} color="black" />,
  },
  {
    path: "/screens/notifications/CreateFlashQSE",
    title: "Créer un Flash",
  },
  {
    path: "/screens/notifications/UpdateFlashQSE",
    title: "Modifier le Flash",
  },
  // ------------- Documents -------------
  {
    path: "/screens/documents/CreateDocument",
    title: "Créer un document",
  },
  {
    path: "/screens/documents/DocumentDetails",
    title: "Document",
    icon: <FontAwesome name="pencil-square-o" size={35} color="black" />,
  },
  {
    path: "/screens/documents/UpdateDocument",
    title: "Modifier le document",
  },
  // ------------- PIP -------------
  {
    path: "/pip/detailsPIP",
    title: "PIP concerné",
    icon: <Feather name="edit" size={24} color="black" />,
  },
  {
    path: "/pip/createPIP",
    title: "Créer une PIP",
  },
  {
    path: "/pip/updatePIP",
    title: "Modifier une PIP",
  },
  // ------------- Profil -------------
  {
    path: "/profil",
    title: "Profil",
  },
  {
    path: "/info/usersAdmin",
    title: "Mes utilisateurs",
  },
  {
    path: "/info/createUserByAdmin",
    title: "Créer un utilisateur",
  },
  // ------------- Processus -------------
  {
    path: "/info/process",
    title: "Mes processus",
  },
  {
    path: "/info/createProcess",
    title: "Créer un processus",
  },
  {
    path: "/product/createProduct",
    title: "Créer un matériel",
  },
  // ------------- Activités -------------
  {
    path: "/info/activities",
    title: "Mes activités",
  },
  {
    path: "/info/detailsActivity",
    title: "Informations",
    icon: <Feather name="edit" size={24} color="black" />,
  },
  {
    path: "/info/createActivity",
    title: "Créer une activité",
  },
  {
    path: "/info/updateActivity",
    title: "Modifier l'activité",
  },
  // ------------- Contexte -------------
  {
    path: "/info/context",
    title: "Contexte",
  },
  {
    path: "/info/createContext",
    title: "Créer le contexte",
  },
  {
    path: "/info/updateContext",
    title: "Modifier le contexte",
  },
  // ------------- Enjeux -------------
  {
    path: "/info/issue",
    title: "Enjeux de l'entreprise",
  },
  {
    path: "/info/createIssue",
    title: "Enjeux de l'entreprise",
  },
  {
    path: "/info/updateIssue",
    title: "Enjeux de l'entreprise",
  },
  // ------------- Politique -------------
  {
    path: "/info/policy",
    title: "Politique de l'entreprise",
  },
  {
    path: "/info/createPolicy",
    title: "Politique de l'entreprise",
  },
  {
    path: "/info/updatePolicy",
    title: "Politique de l'entreprise",
  },
  // ------------- Audits -------------
  {
    path: "/screens/audits/CreateAudit",
    title: "Créer un audit",
  },
  {
    path: "/screens/audits/AuditDetails",
    title: "Audit",
    icon: <FontAwesome name="pencil-square-o" size={35} color="black" />,
  },
  {
    path: "/screens/audits/UpdateAudit",
    title: "Modifier l'audit",
  },
  // ------------- Objectifs -------------
  {
    path: "/objectif/createObjectif",
    title: "Créer un objectif",
  },
  {
    path: "/content/elearning",
    title: "E-Learning",
    icon: <Octicons name="diff-added" size={35} color="black" />,
  },
  {
    path: "/content/createELearning",
    title: "Créer un contenu",
  },
  {
    path: "/content/updateELearning",
    title: "Modifier ce contenu",
  },
  {
    path: "/content/updateELearningPdf",
    title: "Modifier ce contenu",
  },
  {
    path: "/content/listELearning",
    title: "Vidéos",
  },
  {
    path: "/content/listELearningPdf",
    title: "PDF",
  },
  {
    path: "/content/oneELearning",
    title: "Vidéo",
    icon: <Feather name="edit" size={24} color="black" />,
  },
  {
    path: "/content/oneELearningPdf",
    title: "Pdf",
    icon: <Feather name="edit" size={24} color="black" />,
  },
  {
    path: "/menu/carto",
    title: "PDF",
    icon: <Octicons name="diff-added" size={35} color="black" />,
  },
  {
    path: "/objectif/detailsObjectif",
    title: "Objectif",
    icon: <Feather name="edit" size={24} color="black" />,
  },
  {
    path: "/product/detailsProduct",
    title: "Product",
    icon: <Feather name="edit" size={24} color="black" />,
  },
  {
    path: "/product/updateProduct",
    title: "Modifier un produit",
  },
  {
    path: "/objectif/updateObjectif",
    title: "Modifier l'objectif",
  },
  {
    path: "/objectif/createValue",
    title: "Ajouter une valeur",
  },
  {
    path: "/objectif/updateValue",
    title: "Modifier la valeur",
  },
  {
    path: "/screens/actions/RegularActionsList",
    title: "Mes actions",
  },
  {
    path: "/screens/observations/CreateObservation",
    title: "Créer un constat",
  },
  {
    path: "/screens/observations/DetailObservation",
    title: "Constat",
    icon: <FontAwesome name="pencil-square-o" size={35} color="black" />,
  },
  {
    path: "/screens/observations/UpdateObservation",
    title: "Modifier le constat",
  },
  {
    path: "/screens/observations/CreateCloture",
    title: "Clôturer l'audit",
  },
  // ------------- Autres... -------------
  {
    path: "/objectif/createValue",
    title: "Ajouter une valeur",
  },
  {
    path: "/menu/itemsMenuDefault/audit",
    title: "Mes audit",
  },
  // ------------- Fournisseurs -------------
  {
    path: "/supplier/detailsSupplier",
    title: "Fournisseurs",
    icon: <Feather name="edit" size={24} color="black" />,
  },
  {
    path: "/supplier/updateSupplier",
    title: "Modifier le fournisseur",
  },
  {
    path: "/supplier/createSupplier",
    title: "Créer le fournisseur",
  },
  // ------------- Équipements -------------
  {
    path: "/equipment/detailsEquipment",
    title: "Informations",
    icon: <Feather name="edit" size={24} color="black" />,
  },
  {
    path: "/equipment/updateEquipment",
    title: "Modifier l'équipement",
  },
  {
    path: "/equipment/createEquipment",
    title: "Créer l'équipement",
  },
  {
    path: "/equipment/assignEquipment",
    title: "Assigner l'EPI",
  },
  {
    path: "/info/detailsProcess",
    title: "Détails du processus",
    icon: <Feather name="edit" size={24} color="black" />,
  },
];

type propsType = {
  onPressFlesh?: any;
  onPressIcon?: any;
  title?: string;
};

const Header = ({ onPressIcon, title, onPressFlesh }: propsType) => {
  const { user, inCockpit, setInCockpit } = useContext(AppContext);
  const router = useRouter();
  const pathname = usePathname();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const { showError } = useToast();
  const [isMenuOpen, setIsMenuOpen] = React.useState(false);
  const { session } = useSupabase();

  const handleMenuPress = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleCockpitPress = () => {
    if (!inCockpit) {
      setInCockpit(true);
      router.navigate("/");
    }
    setIsMenuOpen(false);
  };

  const handleHomePress = () => {
    if (inCockpit) {
      setInCockpit(false);
      router.navigate("/");
    }
    setIsMenuOpen(false);
  };

  const {
    setSession,
    platformUser,
    setPlatformUser,
    companySelected,
    setCompanySelected,
  } = useContext(AppContext);

  console.log("path_name__", pathname);

  // Initialisation du localStorage
  useEffect(() => {
    const initLocalStorage = async () => {
      try {
        if (Platform.OS === 'web') {
          // Test du localStorage
          localStorage.setItem('test', 'test');
          localStorage.removeItem('test');
        }
      } catch (error) {
        console.error('localStorage not available:', error);
        showError('Erreur de stockage local');
      }
    };

    initLocalStorage();
  }, []);

  // Gestion des deep links
  useEffect(() => {
    const handleDeepLink = async (url: string) => {
      try {
        const urlObj = new URL(url);
        const error = urlObj.searchParams.get('error');
        const details = urlObj.searchParams.get('details');
        
        if (error) {
          showError(details || error);
          return;
        }
      } catch (error) {
        console.error('Error handling deep link:', error);
      }
    };

    // Écouter les deep links
    const subscription = Linking.addEventListener('url', ({ url }) => {
      handleDeepLink(url);
    });

    // Vérifier l'URL initiale
    Linking.getInitialURL().then(url => {
      if (url) {
        handleDeepLink(url);
      }
    });

    return () => {
      subscription.remove();
    };
  }, []);

  const handleWebAppRedirect = async () => {
    if (!session) {
      console.log('No session found, redirecting to login');
      router.push('/login');
      return;
    }

    try {
      // Vérifier que le token est valide
      if (!session.access_token) {
        console.error('No access token found in session');
        router.push('/login');
        return;
      }

      // Vérifier que l'email est présent
      if (!session.user?.email) {
        console.error('No email found in session');
        router.push('/login');
        return;
      }

      // Log de la session pour debug
      console.log('Current session:', {
        hasToken: !!session.access_token,
        tokenLength: session.access_token?.length,
        email: session.user.email,
        expiresAt: session.expires_at,
        currentTime: Date.now() / 1000,
        timeUntilExpiry: session.expires_at ? session.expires_at - (Date.now() / 1000) : 'N/A'
      });

      // Vérifier si le token est expiré ou va expirer bientôt (dans les 5 minutes)
      const now = Date.now() / 1000;
      const expiresAt = session.expires_at || 0;
      const isExpired = expiresAt < now;
      const willExpireSoon = expiresAt - now < 300; // 5 minutes

      if (isExpired || willExpireSoon) {
        console.log('Token expired or will expire soon, refreshing...');
        const { data: { session: newSession }, error } = await supabase.auth.refreshSession();
        
        if (error) {
          console.error('Error refreshing session:', error);
          router.push('/login');
          return;
        }

        if (!newSession) {
          console.error('No new session after refresh');
          router.push('/login');
          return;
        }

        // Utiliser le nouveau token
        session.access_token = newSession.access_token;
        session.expires_at = newSession.expires_at;
        
        console.log('Session refreshed successfully');
      }

      // Récupérer les informations de l'utilisateur et de la société
      const userMetadata = session.user.user_metadata || {};
      const companyInfo = {
        id: companySelected?.id?.toString() || '',
        name: companySelected?.company_name || '',
      };

      // Construire l'URL avec toutes les informations
      const params = new URLSearchParams();
      params.append('token', session.access_token);
      params.append('email', session.user.email);
      params.append('userId', session.user.id);
      params.append('fullName', userMetadata.full_name || session.user.email.split('@')[0]);
      params.append('avatarUrl', userMetadata.avatar_url || '');
      params.append('companyId', companyInfo.id);
      params.append('companyName', companyInfo.name);
      params.append('userRole', userMetadata.role || 'user');
      params.append('userStatus', userMetadata.status || 'active');
      
      // Déterminer l'URL de base en fonction de l'environnement
      const isDevelopment = process.env.NODE_ENV === 'development';
      const baseUrl = isDevelopment 
        ? 'http://localhost:3000'
        : 'https://management-qse-web.vercel.app';
      
      console.log('Environment:', process.env.NODE_ENV);
      console.log('Using base URL:', baseUrl);
      
      const redirectUrl = `${baseUrl}/api/auth/cross-app?${params.toString()}`;
      
      console.log('Final redirect URL:', redirectUrl);
      
      // Ouvrir dans le navigateur avec gestion des erreurs
      try {
        const result = await WebBrowser.openBrowserAsync(redirectUrl);
        
        if (result.type === WebBrowser.WebBrowserResultType.CANCEL) {
          console.log('Browser was closed by user');
        }

        // Attendre un peu pour s'assurer que la redirection est terminée
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Vérifier si nous sommes sur une page d'erreur en utilisant Linking
        const url = await Linking.getInitialURL();
        if (url?.includes('error=')) {
          const errorUrl = new URL(url);
          const error = errorUrl.searchParams.get('error');
          console.error('Received error from web app:', error);
          showError(error || 'Une erreur est survenue');
          return;
        }
      } catch (browserError) {
        console.error('Error opening browser:', browserError);
        showError('Erreur lors de l\'ouverture du navigateur');
      }
    } catch (error) {
      console.error('Error in handleWebAppRedirect:', error);
      showError('Erreur lors de la redirection');
      router.push('/login');
    }
  };

  const handleLogout = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (!error) {
        // Attendre un court instant pour s'assurer que la session est bien mise à jour
        await new Promise(resolve => setTimeout(resolve, 100));
        router.replace("/pre-login");
      }
    } catch (error) {
      console.error("Erreur lors de la déconnexion:", error);
    }
  };

  return (
    <View style={styles.headersContainer}>
      <View style={styles.TopHeaderContainer}>
        <Menu
          placement={"bottom start" as any}
          offset={5}
          disabledKeys={["Settings"]}
          trigger={({ ...triggerProps }) => {
            return (
              <TouchableOpacity {...triggerProps}>
                <Octicons name="three-bars" size={30} color="white" />
              </TouchableOpacity>
            );
          }}
        >
          {platformUser ? (
            <MenuItem
              key="Cockpit"
              textValue="Cockpit"
              onPress={() => {
                router.replace("/");
                setPlatformUser(!platformUser);
              }}
            >
              <MenuItemLabel size="sm">Cockpit</MenuItemLabel>
            </MenuItem>
          ) : (
            <MenuItem
              key="Espace utilisateur"
              textValue="Espace utilisateur"
              onPress={() => {
                router.replace("/");
                setPlatformUser(!platformUser);
              }}
            >
              <MenuItemLabel size="sm">Espace utilisateur</MenuItemLabel>
            </MenuItem>
          )}
          <MenuItem
            key="content"
            textValue="E-learning"
            onPress={() => {
              router.replace("/content/elearning");
              setPlatformUser(!platformUser);
            }}
          >
            <MenuItemLabel size="sm">E-learning</MenuItemLabel>
          </MenuItem>
          <MenuItem
            key="Maintenance"
            textValue="Maintenance"
            onPress={() => {
              router.replace("/menu/itemsMenuDefault/maintenance");
            }}
          >
            <MenuItemLabel size="sm">Maintenance</MenuItemLabel>
          </MenuItem>
          {(user?.status === "Supplier" || user?.status === "Admin") && (
            <MenuItem
              key="Suppliers"
              textValue="Fournisseurs"
              onPress={() => {
                router.replace("/supplier/supplier");
                setPlatformUser(!platformUser);
              }}
            >
              <MenuItemLabel size="sm">Fournisseurs</MenuItemLabel>
            </MenuItem>
          )}
          {user?.status === "Admin" && (
            <MenuItem
              key="Equipments"
              textValue="Équipements"
              onPress={() => {
                router.replace("/menu/itemsMenuDefault/equipment");
                setPlatformUser(!platformUser);
              }}
            >
              <MenuItemLabel size="sm">Équipements</MenuItemLabel>
            </MenuItem>
          )}

          {user?.status === "Admin" && (
            <MenuItem
              key="Dashboard"
              textValue="Tableau de bord"
              onPress={handleWebAppRedirect}
            >
              <MenuItemLabel size="sm">Tableau de bord</MenuItemLabel>
            </MenuItem>
          )}

          <MenuItem
            key="Se déconnecter"
            textValue="Se déconnecter"
            onPress={handleLogout}
          >
            <MenuItemLabel size="sm">Se déconnecter</MenuItemLabel>
          </MenuItem>

          {user?.companies?.map((company: any, index: number) => {
            return (
              <MenuItem
                key={index}
                textValue={company.company_name}
                onPress={() => setCompanySelected(company)}
                style={{
                  backgroundColor:
                    company?.company_name === companySelected?.company_name
                      ? "#f99527"
                      : "",
                }}
              >
                <MenuItemLabel size="sm">{company.company_name}</MenuItemLabel>
              </MenuItem>
            );
          })}

          <MenuItem
            key="Ajouter une nouvelle entreprise"
            textValue="Ajouter une nouvelle entreprise"
          >
            <MenuItemLabel size="sm">
              Ajouter une nouvelle entreprise
            </MenuItemLabel>
          </MenuItem>
        </Menu>

        {/* Logo */}

        <Image
          source={require("@/assets/images/login/logoIZYS_white.png")}
          style={styles.logo}
        />

        <TouchableOpacity onPress={() => router.navigate("/profil")}>
          {user?.profil_picture ? (
            <Image
              source={{ uri: user.profil_picture }}
              style={{
                width: 30,
                height: 30,
                borderRadius: 150,
              }}
            />
          ) : (
            <MaterialIcons name="account-circle" size={35} color="white" />
          )}
        </TouchableOpacity>
      </View>

      {/* bottom header */}
    
      {pathname !== "/Accueil" && (
        <View style={styles.BottomHeaderContainer}>
          <TouchableOpacity
              onPress={() => {
                if (onPressFlesh) {
                  onPressFlesh();
                } else {
                  if (router.canGoBack()) {
                    router.back();
                  } else {
                    router.replace("/");
                  }
                }
              }}
          >
            <Ionicons name="arrow-back" size={30} color="black" />
          </TouchableOpacity>

          <Text style={{ fontSize: 20, fontWeight: "bold" }}>
            {title ? title : tabs.find((tab) => tab.path === pathname)?.title}
          </Text>

          {tabs.find((tab) => tab.path === pathname)?.icon ? (
            <TouchableOpacity onPress={onPressIcon}>
              {tabs.find((tab) => tab.path === pathname)?.icon}
            </TouchableOpacity>
          ) : (
            <View style={{ width: 35 }}></View>
          )}
        </View>
      )}

    </View>
  );
};

const styles = StyleSheet.create({
  headersContainer: {},
  TopHeaderContainer: {
    height: 120,
    backgroundColor: "#1C3144", // Couleur de votre barre de navigation
    flexDirection: "row",
    alignItems: "flex-end",
    justifyContent: "space-between",
    paddingHorizontal: 30,
    paddingBlockEnd: 16,
  },
  BottomHeaderContainer: {
    // backgroundColor: "green", // Couleur de votre barre de navigation
    height: 50,
    // backgroundColor: "#fff", // Couleur de votre barre de navigation

    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 15,
  },
  logo: {
    width: 90,
    height: 40,
    resizeMode: "contain",
  },
});

export default Header;
