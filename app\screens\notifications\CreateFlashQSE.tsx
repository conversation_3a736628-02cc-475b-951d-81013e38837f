/*
app/screens/notifications/CreateFlashQSE.tsx

Formulaire pour créer un Flash QSE.

Informations pertinentes :

- Un Flash QSE est relié à l'entreprise de l'utilisateur connecté qui le crée.

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern` et `Extern` n'ont pas accès à cette page.
*/

import React, { useContext, useEffect, useState } from "react";
import {
  Alert,
  StyleSheet,
  View,
  TextInput,
  Image,
  Text,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { supabase } from "../../../lib/supabase.native";
import { Controller, useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import Octicons from "@expo/vector-icons/Octicons";
import * as ImagePicker from "expo-image-picker";
import uploadFlashSQECoverStorage from "@/lib/uploadFlashSQECoverStorage";
import ContainedButton from "@/components/ui/buttons/ContainedButton";

export default function CreateFlashQSE() {
  const [uidCompany, setUidCompany] = useState("");
  const [cover, setCover] = useState<string | null>(null);
  const { user } = useContext(AppContext);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const {
    handleSubmit,
    formState: { errors },
    control,
    reset,
  } = useForm({
    defaultValues: {
      name: "",
      context: "",
      cover: "",
      causes: "",
      actions: "",
      uid_company: "",
    },
  });

  // Récupérer les informations de l'utilisateur connecté
  useEffect(() => {
    const fetchUserCompany = async () => {
      try {
        console.log("Récupération de l'entreprise associée à l'utilisateur...");
        const { data, error } = await supabase
          .from("companies")
          .select("id")
          .eq("uid_user", user?.uid)
          .limit(1);

        if (error) {
          console.error(
            "Erreur lors de la récupération de l'entreprise :",
            error.message
          );
          return;
        }

        console.log("Entreprise récupérée :", data);
        setUidCompany(data?.[0]?.id || "");
      } catch (err) {
        console.error("Erreur inattendue :", err);
      }
    };

    if (user?.uid) {
      fetchUserCompany();
    }
  }, [user?.uid]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  // Enregistrer l'image
  const handleImagePicker = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled) {
      setCover(result.assets[0].uri);
    }
  };

  // Valider le formulaire
  const handleSubmitCreateFlashQSE = async (data: any) => {
    try {
      if (!user) {
        if (Platform.OS === "web") {
          window.alert("Erreur: Aucune entreprise associée trouvée.");
        } else {
          Alert.alert("Erreur", "Aucune entreprise associée trouvée.");
        }
        return;
      }

      console.log("Entreprise trouvée : ", uidCompany);
      if (!uidCompany) {
        if (Platform.OS === "web") {
          window.alert("Erreur: Aucune entreprise associée trouvée.");
        } else {
          Alert.alert("Erreur", "Aucune entreprise associée trouvée.");
        }
        return;
      }

      // Vérifier et uploader l'image sur Supabase Storage
      let publicUrl = "";
      if (cover) {
        const uploadResult = await uploadFlashSQECoverStorage(cover, user?.uid);

        if (!uploadResult || !uploadResult.url) {
          if (Platform.OS === "web") {
            window.alert("Erreur: Impossible de télécharger l'image.");
          } else {
            Alert.alert("Erreur", "Impossible de télécharger l'image.");
          }
          return;
        }

        publicUrl = uploadResult.url; // URL publique de l'image
      }

      const flashQSEData = {
        ...data,
        cover: publicUrl, // Utiliser l'URL publique de l'image
        uid_company: uidCompany,
        created_at: new Date(),
      };

      const { error } = await supabase.from("flash_qse").insert(flashQSEData);

      if (error) {
        if (Platform.OS === "web") {
          window.alert("Échec de la création: " + (error.message || "Une erreur est survenue."));
        } else {
          Alert.alert(
            "Échec de la création",
            error.message || "Une erreur est survenue."
          );
        }
        return;
      }

      if (Platform.OS === "web") {
        window.alert("Succès: Le Flash QSE a été créé avec succès.");
        setTimeout(() => {
          reset();
          navigation.goBack();
        }, 1000);
      } else {
        Alert.alert("Succès", "Le Flash QSE a été créé avec succès.", [
          {
            text: "OK",
            onPress: () => {
              setTimeout(() => {
                reset();
                navigation.goBack();
              }, 1000);
            },
          },
        ]);
      }
    } catch (err) {
      if (Platform.OS === "web") {
        window.alert("Erreur inattendue: Veuillez réessayer plus tard.");
      } else {
        Alert.alert("Erreur inattendue", "Veuillez réessayer plus tard.");
      }
      console.error("Erreur inattendue :", err);
    }
  };

  return (
    <View style={styles.mainContainer}>
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.select({ ios: -100, android: 100 })}
      >
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={styles.form}
          contentContainerStyle={styles.formContent}
        >
          {/* Champ Nom */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Nom</Text>
            <Controller
              control={control}
              name="name"
              rules={{ required: "Le nom est requis." }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  maxLength={40}
                  placeholderTextColor={"#6B7280"}
                  style={styles.input}
                  placeholder="Nom du Flash QSE"
                  onChangeText={onChange}
                  value={value}
                />
              )}
            />
            {errors.name && (
              <Text style={styles.errorText}>{errors.name.message}</Text>
            )}
          </View>

          {/* Champ Contexte */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Ce qu'il s'est passé</Text>
            <Controller
              control={control}
              name="context"
              rules={{ required: "Le contexte est requis." }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  maxLength={300}
                  placeholderTextColor={"#6B7280"}
                  style={styles.input}
                  placeholder="Ce qu'il s'est passé"
                  onChangeText={onChange}
                  value={value}
                  multiline
                />
              )}
            />
            {errors.context && (
              <Text style={styles.errorText}>{errors.context.message}</Text>
            )}
          </View>

          {/* Champ Image */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Photo</Text>
            <TouchableOpacity
              style={styles.imagePicker}
              onPress={handleImagePicker}
            >
              {!cover && (
                <View
                  style={{
                    width: "100%",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <Octicons
                    style={{
                      borderWidth: 1,
                      borderColor: "#f99527",
                      borderRadius: 7,
                      padding: 5,
                      textAlign: "center",
                    }}
                    name="upload"
                    size={24}
                    color="black"
                  />
                  <Text style={{ textAlign: "center", fontWeight: "normal" }}>
                    Choisir la photo
                  </Text>
                </View>
              )}
            </TouchableOpacity>
            {cover && (
              <Image source={{ uri: cover }} style={styles.imagePreview} />
            )}
          </View>

          {/* Champ Causes */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Les causes</Text>
            <Controller
              control={control}
              name="causes"
              rules={{ required: "Les causes sont requises." }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  maxLength={300}
                  placeholderTextColor={"#6B7280"}
                  style={styles.input}
                  placeholder="Les causes"
                  onChangeText={onChange}
                  value={value}
                  multiline
                />
              )}
            />
            {errors.causes && (
              <Text style={styles.errorText}>{errors.causes.message}</Text>
            )}
          </View>

          {/* Champ Actions */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Les actions</Text>
            <Controller
              control={control}
              name="actions"
              rules={{ required: "Les actions sont requises." }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  maxLength={300}
                  placeholderTextColor={"#6B7280"}
                  style={styles.input}
                  placeholder="Les actions"
                  onChangeText={onChange}
                  value={value}
                  multiline
                />
              )}
            />
            {errors.actions && (
              <Text style={styles.errorText}>{errors.actions.message}</Text>
            )}
          </View>

          {/* Bouton de soumission */}
          <ContainedButton
            label="Créer"
            backgroundColor="#F99527"
            onPress={async () => {
              await handleSubmit(handleSubmitCreateFlashQSE)();
            }}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  form: {
    flex: 1,
    paddingHorizontal: 20,
  },
  formContent: {
    paddingBottom: 100,
  },
  headSection: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 20,
    marginBottom: 90,
  },
  backButton: {
    width: 24,
    textAlign: "left",
  },
  titleWrapper: {
    display: "flex",
    flexDirection: "row",
    marginVertical: 15,
  },
  title: {
    color: "#262627",
    fontSize: 17,
    textAlign: "left",
  },
  input: {
    borderWidth: 2,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "auto",
    padding: 12,
    marginTop: 10,
    color: "#000000",
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginBottom: 5,
  },
  infoText: {
    fontSize: 14,
    marginVertical: 10,
    color: "#555",
  },
  inputContainer: {
    position: "relative",
    marginBottom: 20,
    width: "auto",
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
    textAlign: "left",
  },
  confirmButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    marginBottom: 200,
    backgroundColor: "#f99527",
    marginTop: 10,
  },
  confirmButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  imagePicker: {
    padding: 10,
    backgroundColor: "transparent",
    borderColor: "#E5E7EB",
    borderRadius: 8,
    borderWidth: 1,
    alignItems: "center",
    marginBottom: 10,
  },
  imagePickerText: {
    color: "#1F2937",
    fontWeight: "bold",
  },
  imagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
});
