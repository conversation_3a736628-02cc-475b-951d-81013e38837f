import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Platform } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { supabase } from '../lib/supabase';
import InputCustomized from '@/components/ui/inputs/InputCustomized';
import ContainedButton from '@/components/ui/buttons/ContainedButton';
import Toast from 'react-native-toast-message';
import { useForm } from 'react-hook-form';

export default function ResetPassword() {
  const [loading, setLoading] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const router = useRouter();
  const { token } = useLocalSearchParams();
  
  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (!isMounted) return;

    console.log("=== PAGE RÉINITIALISATION MOT DE PASSE CHARGÉE ===");
    console.log("Token reçu:", token);
    console.log("Timestamp:", new Date().toISOString());
    
    if (!token) {
      console.log("=== ERREUR: TOKEN MANQUANT ===");
      console.log("Redirection vers la page de connexion...");
      
      // Attendre que le composant soit monté avant de naviguer
      setTimeout(() => {
        Toast.show({
          type: 'error',
          text1: 'Lien de réinitialisation invalide',
          text2: 'Veuillez demander un nouveau lien de réinitialisation',
          text1Style: { color: '#1C3144' },
          visibilityTime: 4000,
        });
        
        router.replace('/login');
      }, 100);
    }
  }, [token, router, isMounted]);

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
    control,
  } = useForm({
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  });

  const onSubmit = async (data: { password: string; confirmPassword: string }) => {
    console.log("=== TENTATIVE DE RÉINITIALISATION ===");
    console.log("Timestamp:", new Date().toISOString());
    
    if (data.password !== data.confirmPassword) {
      console.log("Erreur: Les mots de passe ne correspondent pas");
      Toast.show({
        type: 'error',
        text1: 'Les mots de passe ne correspondent pas',
        text1Style: { color: '#1C3144' },
      });
      return;
    }

    setLoading(true);
    try {
      console.log("Tentative de mise à jour du mot de passe...");
      const { error } = await supabase.auth.updateUser({
        password: data.password,
      });

      if (error) {
        console.log("=== ERREUR MISE À JOUR MOT DE PASSE ===");
        console.log("Type d'erreur:", error.message);
        console.log("Code d'erreur:", error.status);
        
        Toast.show({
          type: 'error',
          text1: 'Erreur lors de la réinitialisation du mot de passe',
          text1Style: { color: '#1C3144' },
        });
        return;
      }

      console.log("=== MOT DE PASSE MIS À JOUR AVEC SUCCÈS ===");
      Toast.show({
        type: 'success',
        text1: 'Mot de passe mis à jour avec succès',
        text1Style: { color: '#1C3144' },
      });
      
      setTimeout(() => {
        router.replace('/login');
      }, 100);
    } catch (error) {
      console.log("=== ERREUR INATTENDUE ===");
      console.log("Erreur complète:", error);
      Toast.show({
        type: 'error',
        text1: 'Une erreur inattendue est survenue',
        text1Style: { color: '#1C3144' },
      });
    } finally {
      setLoading(false);
    }
  };

  // Si pas de token, ne pas afficher le formulaire
  if (!token) {
    return null;
  }

  return (
    <View style={styles.container}>
      <Toast />
      <Text style={styles.title}>Réinitialiser votre mot de passe</Text>
      
      <View style={styles.form}>
        <InputCustomized
          label="Nouveau mot de passe"
          placeholder="Entrez votre nouveau mot de passe"
          name="password"
          register={register}
          control={control}
          errors={errors}
          setError={setError}
          secureTextEntry
        />
        
        <InputCustomized
          label="Confirmer le mot de passe"
          placeholder="Confirmez votre nouveau mot de passe"
          name="confirmPassword"
          register={register}
          control={control}
          errors={errors}
          setError={setError}
          secureTextEntry
        />
        
        <ContainedButton
          label="Réinitialiser le mot de passe"
          backgroundColor="#F99527"
          onPress={handleSubmit(onSubmit)}
          disabled={loading}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 30,
    color: '#1C3144',
  },
  form: {
    width: '100%',
    maxWidth: Platform.OS === 'web' ? 400 : '100%',
    gap: 20,
  },
}); 