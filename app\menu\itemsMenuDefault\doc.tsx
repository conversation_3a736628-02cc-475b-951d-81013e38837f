/*
app/menu/itemsMenuDefault/doc.tsx

Liste des documents.

Informations pertinentes :

- On récupère la liste des documents depuis la table `documents` sur Supabase.
- Les documents appartiennent à une entreprise. Ils ont un champ `uid_company` (clé étrangère) qui contient l'id de l'entreprise à laquelle ils appartiennent.

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `Supplier` n'ont pas accès à cette page.
*/

import {
  Alert,
  FlatList,
  Keyboard,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
  ScrollView,
  Dimensions,
} from "react-native";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { useContext } from "react";
import { AppContext } from "@/state/AppContext";
import { useState, useEffect } from "react";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import FontAwesome from "@expo/vector-icons/FontAwesome";
import Octicons from "@expo/vector-icons/Octicons";
import { supabase } from "@/lib/supabase";
import React from "react";
import { useFocusEffect } from "@react-navigation/native";
import { useRouter } from "expo-router";
import Header from "@/components/common/Header";
import { WebView } from 'react-native-webview';
import Pdf from 'react-native-pdf';

export default function DocumentList() {
  const router = useRouter();
  const [documentList, setDocumentList] = useState<any>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState(""); // État pour la barre de recherche
  const [filteredDocumentList, setFilteredDocumentList] = useState<any>([]); // Liste filtrée
  const [activities, setActivities] = useState<{[key: string]: string}>({});
  const { user, companySelected } = useContext(AppContext);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  // Récupérer les activités de l'entreprise
  useEffect(() => {
    const fetchActivities = async () => {
      if (!companySelected?.id) return;

      try {
        const { data, error } = await supabase
          .from("activities")
          .select("id, activity_name")
          .eq("company_id", companySelected.id);

        if (error) {
          console.error("Erreur lors de la récupération des activités:", error);
          return;
        }

        // Créer un objet avec l'ID comme clé et le nom comme valeur
        const activitiesMap = data.reduce((acc: {[key: string]: string}, activity: { id: string, activity_name: string }) => {
          acc[activity.id] = activity.activity_name;
          return acc;
        }, {});

        setActivities(activitiesMap);
      } catch (err) {
        console.error("Erreur inattendue lors de la récupération des activités:", err);
      }
    };

    fetchActivities();
  }, [companySelected]);

  // Récupérer la liste des documents de l'entreprise
  useFocusEffect(
    React.useCallback(() => {
      const fetchDocuments = async () => {
        try {
          if (!user || !user.uid) {
            console.error("Utilisateur non connecté.");
            return;
          }

          // Vérifiez que l'utilisateur a une entreprise associée
          const companyId = companySelected?.id;
          console.log("Company ID:", companyId); // Log pour déboguer

          if (!companyId) {
            console.log("Aucune entreprise sélectionnée"); // Log pour déboguer
            setDocumentList([]); // Aucun document
            setLoading(false); // Terminer le chargement
            return;
          }

          const { data, error } = await supabase
            .from("documents")
            .select("*")
            .eq("company_id", companyId);

          console.log("Documents récupérés:", data); // Log pour déboguer
          console.log("Erreur éventuelle:", error); // Log pour déboguer

          if (error) {
            console.error(
              "Erreur lors de la récupération des documents :",
              error.message
            );
            return;
          }

          setDocumentList(data || []); // Met à jour avec les documents ou une liste vide
        } catch (err) {
          console.error("Erreur inattendue :", err);
        } finally {
          setLoading(false); // Fin du chargement
        }
      };

      fetchDocuments();
    }, [user])
  );

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Supplier"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  // Mettre à jour la liste filtrée
  useEffect(() => {
    const filteredList = documentList.filter((item: any) =>
      item.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredDocumentList(filteredList);
  }, [searchQuery, documentList]);

  return (
    <View style={styles.mainContainer}>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={{ fontWeight: "bold" }}>Créer un nouveau document :</Text>
          <TouchableOpacity
            onPress={() => router.navigate("/screens/documents/CreateDocument")}
            style={styles.addButton}
          >
            <Octicons name="diff-added" size={25} color="#F99527" />
          </TouchableOpacity>
        </View>

        <View style={styles.searchWrapper}>
          <View style={styles.searchContainer}>
            <View style={styles.searchInputWrapper}>
              <FontAwesome
                name="search"
                size={24}
                color="#3C3c4399"
                style={styles.searchIcon}
              />
              <TextInput
                style={styles.searchInput}
                placeholder="Rechercher par nom..."
                placeholderTextColor="#3C3c4399"
                value={searchQuery}
                onChangeText={(text) => setSearchQuery(text)}
              />
            </View>
          </View>
        </View>

        <View style={styles.listWrapper}>
          {loading ? (
            <Text style={styles.loadingText}>Chargement...</Text>
          ) : documentList.length === 0 ? (
            <Text style={styles.emptyText}>
              Aucun document.
            </Text>
          ) : (
            <FlatList
              data={filteredDocumentList}
              keyExtractor={(item) => item.id.toString() || Math.random().toString()}
              renderItem={({ item }) => (
                <TouchableOpacity
                  onPress={() =>
                    navigation.navigate("screens/documents/DocumentDetails", {
                      document: item,
                    })
                  }
                  style={styles.listItem}
                >
                  <View style={styles.leftElementsCard}>
                    <Text style={styles.listName}>{item.name}</Text>
                    <Text style={styles.listDescription}>
                      {item.description}
                    </Text>
                    {item.url && (
                      <View style={styles.pdfPreview}>
                        {Platform.OS === 'web' ? (
                          <iframe
                            src={`${item.url}#view=FitH`}
                            style={styles.webPdf}
                            title="PDF Preview"
                          />
                        ) : (
                          <WebView
                            source={{ uri: item.url }}
                            style={styles.webview}
                            scrollEnabled={false}
                            onError={(syntheticEvent) => {
                              const { nativeEvent } = syntheticEvent;
                              console.warn('WebView error: ', nativeEvent);
                            }}
                          />
                        )}
                      </View>
                    )}
                  </View>

                  <View style={styles.rightElementsCard}>
                    <View style={styles.activity}>
                      <Text style={styles.activityText}>
                        {activities[item.activity] || "Activité inconnue"}
                      </Text>
                    </View>
                  </View>
                </TouchableOpacity>
              )}
              contentContainerStyle={styles.listContainer}
            />
          )}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  container: {
    flex: 1,
    gap: 10,
    paddingHorizontal: 10,
    maxWidth: 800,
    width: "100%",
    alignSelf: "center",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 10,
    marginTop: 14,
  },
  searchWrapper: {
    width: '100%',
    maxWidth: Platform.OS === 'web' ? 800 : '100%',
    alignSelf: 'center',
  },
  searchContainer: {
    width: "100%",
    marginBottom: 20,
    marginTop: 10,
    paddingHorizontal: 10,
  },
  searchInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1C314414',
    borderRadius: 8,
    paddingHorizontal: 10,
    height: 60,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    padding: 10,
    fontSize: 16,
    color: '#525252',
  },
  addButton: {
    padding: 5,
  },
  listWrapper: {
    flex: 1,
    width: '100%',
    maxWidth: Platform.OS === 'web' ? 800 : '100%',
    alignSelf: 'center',
    paddingBottom: Platform.OS === 'web' ? 20 : 100,
  },
  loadingText: {
    textAlign: 'center',
    marginTop: 20,
    fontSize: 16,
    color: '#8c8c8c',
  },
  emptyText: {
    textAlign: 'center',
    marginTop: 20,
    fontSize: 16,
    color: '#8c8c8c',
  },
  listContainer: {
    paddingVertical: 10,
    paddingBottom: Platform.OS === 'web' ? 20 : 100,
  },
  listItem: {
    width: '100%',
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#CCC',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  leftElementsCard: {
    flex: 1,
    marginRight: 10,
  },
  listName: {
    fontSize: 15,
    color: '#000000',
    fontWeight: 'bold',
    marginBottom: 5,
  },
  listDescription: {
    fontSize: 14,
    color: '#525252',
  },
  rightElementsCard: {
    justifyContent: 'center',
  },
  activity: {
    backgroundColor: '#F99527',
    borderRadius: 15,
    paddingHorizontal: 10,
    paddingVertical: 8,
  },
  activityText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  pdfPreview: {
    width: '100%',
    height: 200,
    marginTop: 10,
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: '#f0f0f0',
  },
  webview: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  webPdf: {
    width: '100%',
    height: '100%',
    borderWidth: 0,
  },
});
