/*
app/info/updateIssue.tsx

Mettre à jour l'enjeu de l'entreprise.

Informations pertinentes :

- Lo<PERSON> de la validation du formulaire on envoie les données dans la table `issue`.
- Une entreprise ne peut avoir qu'un enjeu. Si une entreprise a déjà un enjeu on ne peut pas en créer un nouveau (on peut modifier le enjeu existant).

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern` et `Extern` n'ont pas accès à cette page.
*/

import { useRouter } from "expo-router";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  Alert,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Image,
  ActivityIndicator,
} from "react-native";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import TypeIssue from "@/types/typeIssue";
// import openai from "@/lib/openai.native"; // Importer OpenAI
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import TypeContext from "@/types/typeContext";
import Constants from "expo-constants";


export default function UpdateIssue() {
  const { companySelected } = useContext(AppContext);
  const router = useRouter();
  const [issueData, setIssueData] = useState<TypeIssue | null>(null);
  const [contextData, setContextData] = useState<TypeContext | null>(null);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const { user } = useContext(AppContext);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const {
    handleSubmit,
    formState: { errors },
    control,
    reset,
    setValue,
  } = useForm<TypeIssue>({
    defaultValues: {
      context: "",
    },
  });

  useEffect(() => {
    const fetchIssue = async () => {
      if (!companySelected?.id) return;

      const { data, error } = await supabase
        .from("issues")
        .select("*")
        .eq("company_id", companySelected.id)
        .single();

      if (error) {
        console.error(
          "Erreur lors de la récupération de l'enjeu :",
          error.message
        );
      } else {
        setIssueData(data);
        reset(data);
      }
    };

    const fetchContext = async () => {
      if (!companySelected?.id) return;

      const { data, error } = await supabase
        .from("contexts")
        .select("*")
        .eq("company_id", companySelected.id)
        .single();

      if (error) {
        console.error(
          "Erreur lors de la récupération du contexte :",
          error.message
        );
      } else {
        setContextData(data);
      }
    };

    fetchContext();
    fetchIssue();
  }, [companySelected, reset]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const generatePESTEL = async () => {
    const OPENAI_API_KEY = Constants.expoConfig?.extra?.NEXT_PUBLIC_OPENAI_API_KEY;

    if (!contextData) {
      alert("Aucun contexte trouvé.");
      return;
    }
    setIsGenerating(true);
  
    // Récupérer les enjeux actuels du formulaire
    const currentEnjeux = issueData?.context || "";
    const hasExistingEnjeux = currentEnjeux.trim().length > 0;
  
    const prompt = `
      Rôle : Agis en tant que consultant(e) en stratégie d'entreprise expérimenté(e).
      Objectif : Analyser en profondeur le contexte fourni ci-dessous concernant une entreprise spécifique afin d'identifier, de définir et de hiérarchiser ses principaux enjeux actuels et futurs.

      Contexte Fourni par l'Utilisateur :
      ----------------------------------------------------------------------
      - Histoire de l'entreprise : ${contextData?.history}
      - Contexte de l'entreprise : ${contextData?.context}
      - Organisation interne : ${contextData?.organisation}
      - Site web : ${contextData?.website}
      -----------------------------------------------------------------------

      ${hasExistingEnjeux ? `
      Enjeux Actuels (à améliorer et compléter) :
      ----------------------------------------------------------------------
      ${currentEnjeux}
      -----------------------------------------------------------------------
      ` : `
      Note : Aucun enjeu n'a été saisi précédemment. Tu dois donc :
      1. Effectuer une analyse complète du contexte
      2. Identifier tous les enjeux potentiels
      3. Structurer ta réponse de manière exhaustive
      `}

      Instructions Spécifiques :
      1. Analyse Initiale :
         - Examine attentivement TOUTES les informations fournies
         - Identifie les forces, faiblesses, opportunités et menaces
         - Considère les aspects PESTEL (Politique, Économique, Social, Technologique, Environnemental, Légal)
         - Analyse les interactions entre les différents éléments

      2. ${hasExistingEnjeux ? `
      Amélioration des Enjeux Existants :
         - Examine les enjeux actuels fournis
         - Identifie les points forts et les aspects à améliorer
         - Propose des améliorations pour chaque enjeu
         - Ajoute des détails manquants ou des perspectives nouvelles
      ` : `
      Identification des Enjeux :
         - Développe une analyse complète et structurée
         - Identifie les enjeux majeurs par catégorie
         - Justifie chaque enjeu identifié
         - Hiérarchise les enjeux par importance
      `}

      3. Structure de la Réponse :
         ${hasExistingEnjeux ? `
         - Commence par les enjeux existants améliorés
         - Ajoute ensuite les nouveaux enjeux identifiés
         ` : `
         - Présente les enjeux par catégorie (PESTEL)
         - Pour chaque catégorie, liste les enjeux identifiés
         `}
         - Pour chaque enjeu :
           * Titre concis et explicite
           * Description détaillée
           * Catégorie (Stratégique, Opérationnel, Financier, etc.)
           * Justification de son importance
           * Impact potentiel sur l'entreprise

      4. Synthèse et Axes d'Amélioration :
         Après avoir présenté ton analyse, ajoute une section "AXES D'AMÉLIORATION" qui doit :
         
         a) Analyser les limites de ta propre analyse :
            - Quelles informations manquent pour une analyse plus complète ?
            - Quels aspects n'ont pas pu être approfondis ?
            - Quelles hypothèses ont dû être faites ?
         
         b) Proposer des pistes concrètes pour enrichir l'analyse :
            - Quelles données supplémentaires seraient utiles ?
            - Quels domaines mériteraient une investigation plus poussée ?
            - Quelles questions clés restent sans réponse ?
         
         c) Guider l'utilisateur pour la prochaine itération :
            - Quels aspects devraient être priorisés ?
            - Quelles informations précises l'utilisateur devrait-il fournir ?
            - Comment structurer ces informations pour la prochaine analyse ?
         
         d) Évaluer la qualité de l'analyse actuelle :
            - Quels sont les points forts de l'analyse réalisée ?
            - Quelles sont ses limites principales ?
            - Comment pourrait-elle être améliorée ?

      5. Format de Sortie :
         - Une liste structurée et hiérarchisée des enjeux
         ${hasExistingEnjeux ? `
         - Pour les enjeux existants : "[AMÉLIORÉ] [Titre original]"
         - Pour les nouveaux enjeux : "[NOUVEAU] [Titre]"
         ` : `
         - Pour chaque catégorie : "[CATÉGORIE]"
         - Pour chaque enjeu : "[Titre]"
         `}
         - Synthèse finale des dynamiques globales
         - Section "AXES D'AMÉLIORATION" détaillée

      Note : Sois exhaustif dans ton analyse mais reste concis dans tes formulations. Utilise un langage professionnel et précis. La section "AXES D'AMÉLIORATION" doit être particulièrement détaillée et pratique pour guider l'utilisateur dans ses prochaines étapes.
    `;
  
    try {
      const response = await fetch("https://api.openai.com/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${OPENAI_API_KEY}`,
        },
        body: JSON.stringify({
          model: "gpt-4",
          messages: [{ role: "user", content: prompt }],
          max_tokens: 3000,
          temperature: 0.7,
        }),
      });
    
      const data = await response.json();
      
      if (data.error) {
        console.error("Erreur OpenAI :", data.error);
        alert(`Erreur API : ${data.error.message}`);
        return;
      }
    
      if (data.choices && data.choices.length > 0 && data.choices[0].message?.content) {
        const generatedText = data.choices[0].message.content.trim();
        setValue("context", generatedText);
      } else {
        alert("Erreur : La génération des enjeux a échoué.");
      }
    } catch (error) {
      console.error("Erreur OpenAI :", error);
      alert("Erreur : Problème lors de la génération des enjeux.");
    } finally {
      setIsGenerating(false);
    }
  };
  

  // Valider le formulaire
  const handleUpdateIssue = async (data: TypeIssue) => {
    try {
      setIsSubmitting(true); // 🔥 Active le chargement

      if (!issueData) {
        Alert.alert("Erreur", "Aucun enjeu existant trouvé.");
        return;
      }

      const { error } = await supabase
        .from("issues")
        .update({
          context: data.context,
        })
        .eq("company_id", companySelected?.id);

      if (error) {
        Alert.alert(
          "Échec de la mise à jour",
          error.message || "Une erreur est survenue."
        );
        return;
      }
      router.back();
      Alert.alert("Succès", "L'enjeu a été mis à jour avec succès.");
    } catch (err) {
      Alert.alert("Erreur inattendue", "Veuillez réessayer plus tard.");
      console.error("Erreur inattendue :", err);
    } finally {
      setIsSubmitting(false); // 🔥 Désactive le chargement après la requête
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      keyboardVerticalOffset={Platform.select({ ios: 0, android: 100 })}
    >
      <View style={styles.container}>
        <ScrollView style={styles.form}>
          {/* Texte d'introduction */}
          <View style={styles.introduction}>
            <View style={styles.introduction}>
              <Text style={styles.introText}>
                Définissez vos enjeux suivant le PESTEL
              </Text>
              <View style={styles.IAButtonContainer}>
                <Text style={styles.IAButtonText}>
                  Laissez-vous assister par l'IA pour générer vos enjeux
                </Text>
                <TouchableOpacity
                  style={[styles.IAButton, isGenerating && styles.IAButtonDisabled]}
                  onPress={generatePESTEL}
                  disabled={isGenerating}
                >
                  {isGenerating ? (
                    <View style={styles.loadingContainer}>
                      <ActivityIndicator size="small" color="#f99527" />
                      <Text style={styles.loadingText}>Génération en cours...</Text>
                    </View>
                  ) : (
                    <Image
                      style={styles.IAButtonImage}
                      source={require("@/assets/images/qse.png")}
                    />
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </View>

          {/* Champ Enjeu */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Enjeu(x)</Text>
            <Controller
              control={control}
              name="context"
              rules={{ required: "Les enjeux sont requis." }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  placeholderTextColor={"#6B7280"}
                  style={styles.inputXL}
                  multiline={true}
                  textAlignVertical="top"
                  placeholder="Enjeux actuels"
                  onChangeText={onChange}
                  value={value}
                />
              )}
            />
            {errors.context && (
              <Text style={styles.errorText}>{errors.context.message}</Text>
            )}
          </View>

          {/* Bouton de soumission */}
          <TouchableOpacity
            style={styles.confirmButton}
            onPress={() => {
              handleSubmit(handleUpdateIssue)();
            }}
          >
            <Text style={styles.confirmButtonText}>Enregistrer</Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    height: "100%",
    width: "100%",
    alignItems: "center",
    alignContent: "center",
    backgroundColor: "#FFFFFF",
  },
  headSection: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 30,
  },
  backButton: {
    width: 24,
    textAlign: "left",
  },
  titleWrapper: {
    display: "flex",
    flexDirection: "row",
    marginVertical: 15,
  },
  title: {
    color: "#262627",
    fontSize: 17,
    textAlign: "left",
  },
  form: {
    width: Platform.OS === 'web' ? '50%' : '100%',
    alignSelf: "center",
    paddingHorizontal: 30,
    marginTop: 20,
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      minWidth: 400,
    } : {}),
  },
  inputXL: {
    borderWidth: 2,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "100%", // S'adapte à la largeur du conteneur parent
    paddingHorizontal: 12,
    paddingVertical: 10, // Ajoute un espace en haut pour ne pas coller au bord
    height: 364, // Hauteur fixe
    color: "#000000",
    textAlignVertical: "top", // Aligne le texte en haut
  },
  input: {
    borderWidth: 2,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "auto",
    padding: 12,
    marginTop: 10,
    color: "#000000",
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginBottom: 5,
  },
  infoText: {
    fontSize: 14,
    marginVertical: 10,
    color: "#555",
  },
  inputContainer: {
    position: "relative",
    marginBottom: 20,
    width: "100%",
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
    textAlign: "left",
  },
  introduction: {
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-between",
    marginBottom: 20,
    width: "100%",
  },
  introText: {
    fontSize: 14,
    color: "#262627",
    marginBottom: 15,
  },
  IAButtonContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f8f9fa",
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#e9ecef",
  },
  IAButtonText: {
    flex: 1,
    fontSize: 14,
    color: "#262627",
    marginRight: 10,
  },
  IAButton: {
    padding: 8,
  },
  IAButtonDisabled: {
    opacity: 0.7,
  },
  IAButtonImage: {
    height: 24,
    width: 24,
  },
  confirmButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    marginBottom: 100,
    backgroundColor: "#f99527",
    marginTop: 10,
  },
  confirmButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  imagePicker: {
    width: "100%",
    padding: 10,
    backgroundColor: "#FFFFFF",
    borderColor: "#CCC",
    borderRadius: 8,
    borderWidth: 2,
    alignItems: "center",
    marginBottom: 10,
    marginTop: 10,
  },
  imagePickerText: {
    color: "#1F2937",
    fontWeight: "bold",
  },
  imagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  imageContainer: {
    width: "100%",
    justifyContent: "center",
  },
  uploadPlaceholder: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  uploadIcon: {
    borderWidth: 1,
    borderColor: "#f99527",
    borderRadius: 7,
    padding: 5,
  },
  uploadText: {
    textAlign: "center",
    fontWeight: "normal",
  },
  fullImagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  dateTimePickerContainer: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    textAlign: "left",
    marginVertical: 20,
  },
  dateTimePickerLabel: {
    color: "#063E76",
    fontSize: 14,
    fontWeight: "bold",
    backgroundColor: "#FFF",
  },
  datePicker: {
    width: "100%",
    marginTop: 10,
    marginBottom: 10,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  loadingText: {
    fontSize: 12,
    color: '#f99527',
  },
});
