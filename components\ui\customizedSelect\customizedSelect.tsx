import { <PERSON><PERSON>View, StyleSheet, Text, View } from "react-native";
import { Input, InputField } from "../input/index.native";
import { Controller } from "react-hook-form";
import { useEffect } from "react";
import {
  Select,
  SelectTrigger,
  SelectInput,
  SelectIcon,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectItem,
} from "../select/index";

type TypeSelect = {
  label: string;
  value: string;
  isDisabled?: boolean;
};

type TypesProps = {
  label?: string;
  placeholder?: string;
  typeInput?: string;
  value?: any;
  onChangeText?: any;
  register?: any;
  name: string;
  control?: any;
  errors?: any;
  setError?: any;
  isDisabled?: boolean;
  isReadOnly?: boolean;
  data: TypeSelect[];
};

const CustomizedSelect = ({
  label,
  name,
  control,
  errors,
  data,
}: TypesProps) => {
  return (
    <View style={styles.containerInput}>
      {label && <Text style={styles.label}>{label}</Text>}

      <Controller
        control={control}
        name={name}
        render={({ field: { onChange, value }, fieldState: { error } }) => (
          <View style={styles.selectContainer}>
            <Select selectedValue={value} onValueChange={onChange}>
              <SelectTrigger variant="outline" size="md" style={styles.selectTrigger}>
                <SelectInput
                  placeholder={
                    data.find((item) => item.value === value)?.label ||
                    "Choisissez une option"
                  }
                  style={styles.selectInput}
                  value={data.find((item) => item.value === value)?.label || ""}
                />
                <SelectIcon className="mr-3" />
              </SelectTrigger>

              <SelectPortal>
                <SelectBackdrop />
                <SelectContent>
                  {data?.length === 0 ? (
                    <SelectItem label={"Aucune donnée"} value={""} isDisabled />
                  ) : (
                    data?.map((item, index) => (
                      <SelectItem
                        key={index}
                        label={item.label}
                        value={item.value}
                      />
                    ))
                  )}
                </SelectContent>
              </SelectPortal>
            </Select>
          </View>
        )}
      />

      {errors[name] && (
        <Text style={styles.errorText}>Ce champ est requis</Text>
      )}
    </View>
  );
};

export default CustomizedSelect;

const styles = StyleSheet.create({
  containerInput: {
    gap: 8,
    fontSize: 14,
    width: '100%',
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginTop: 5,
  },
  label: {
    fontSize: 14,
    color: "#262627",
    marginVertical: 1,
    fontWeight: "bold",
  },
  placeholderStyle: {
    color: "gray",
    fontStyle: "italic",
  },
  selectContainer: {
    width: '100%',
    minHeight: 50,
  },
  selectTrigger: {
    width: '100%',
    height: 50,
    borderWidth: 1,
    borderColor: '#d3d3d3',
    borderRadius: 8,
    backgroundColor: '#fff',
    paddingHorizontal: 12,
  },
  selectInput: {
    color: "black",
    width: '100%',
    height: '100%',
    textAlignVertical: 'center',
    paddingVertical: 12,
    fontSize: 16,
  },
});
