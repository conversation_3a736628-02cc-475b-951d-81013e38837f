export const generateSecurePassword = (length = 12) => {
  const lowerCase = "abcdefghijklmnopqrstuvwxyz";
  const upperCase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const numbers = "0123456789";
  const specialChars = "!@#$%^&*()_+[]{}|;:,.<>?";

  const allChars = lowerCase + upperCase + numbers + specialChars;

  let password = "";

  // Assurer qu'il y a au moins un caractère de chaque type
  password += lowerCase[Math.floor(Math.random() * lowerCase.length)];
  password += upperCase[Math.floor(Math.random() * upperCase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  password += specialChars[Math.floor(Math.random() * specialChars.length)];

  // Ajouter des caractères aléatoires jusqu'à la longueur souhaitée
  for (let i = password.length; i < length; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)];
  }

  // Mélanger les caractères pour éviter les prévisibilités
  password = password
    .split("")
    .sort(() => 0.5 - Math.random())
    .join("");

  return password;
}; 