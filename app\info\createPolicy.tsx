/*
app/info/createPolicy.tsx

Créer la politique de l'entreprise.

Informations pertinentes :

- Lo<PERSON> de la validation du formulaire on envoie les données dans la table `policies`.
- Une entreprise ne peut avoir qu'une politique. Si une entreprise a déjà une politique on ne peut pas en créer un nouveau.

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern` et `Extern` n'ont pas accès à cette page.
*/

import { useRouter } from "expo-router";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  Alert,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Image,
  ActivityIndicator,
} from "react-native";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import TypePolicy from "@/types/typePolicies";
import TypeContext from "@/types/typeContext";
import TypeIssue from "@/types/typeIssue";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import openai from "@/lib/openai.native";

export default function CreatePolicy() {
  const { user, companySelected } = useContext(AppContext);
  const router = useRouter();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const [isGenerating, setIsGenerating] = useState(false);
  const [contextData, setContextData] = useState<TypeContext | null>(null);
  const [enjeux, setEnjeux] = useState<TypeIssue | null>(null);
  const {
    handleSubmit,
    formState: { errors },
    control,
    reset,
    setValue,
  } = useForm<TypePolicy>({
    defaultValues: {
      context: "",
    },
  });

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const generatePolicyWithAI = async () => {
    try {
      setIsGenerating(true);

      const { data, error } = await supabase
        .from("contexts")
        .select("history, context, organisation, website")
        .eq("company_id", companySelected?.id)
        .single();

      if (error) {
        Alert.alert("Erreur", "Impossible de récupérer les données.");
        console.error(error);
        return;
      }

      const prompt = `  
      Partant du contexte, des enjeux et de la cartographie de processus, 
      - Histoire de l'entreprise : ${data.history}
      - Contexte actuel : ${data.context}
      - Organisation interne : ${data.organisation}
      - Site web : ${data.website}
      - Enjeux identifiés à partir de l'analyse PESTEL : ${enjeux?.context}
      agis en tant que Conseiller(ère) en Stratégie et Performance Durable, expert(e) dans l'élaboration de politiques d'entreprise intégrées et orientées action.
      Objectif Fondamental :
      Rédiger une Politique d'Engagement Stratégique (ou Politique de Performance Globale et Durable) pour l'entreprise décrite ci-dessous.
      `;

      const response = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [{ role: "user", content: prompt }],
        max_tokens: 3000,
        temperature: 0.7,
      });

      if (response.choices.length > 0 && response.choices[0].message?.content) {
        const generatedText = response.choices[0].message.content.trim();
        setValue("context", generatedText);
      } else {
        Alert.alert("Erreur", "La génération de la politique a échoué.");
      }
    } catch (error) {
      console.error("Erreur OpenAI :", error);
      Alert.alert("Erreur", "Problème lors de la génération de la politique.");
    } finally {
      setIsGenerating(false);
    }
  };

  // Valider le formulaire
  const handleSubmitCreatePolicy = async (data: any) => {
    try {
      const created_at = new Date();

      // Préparation des données pour la table `policies`
      const policiesData = {
        context: data.context,
        uid_admin: user?.uid,
        created_at: created_at,
        company_id: companySelected?.id,
      };

      // Insertion dans la table
      const { error: policiesError } = await supabase
        .from("policies")
        .insert(policiesData)
        .select("id")
        .single();

      if (policiesError) {
        Alert.alert(
          "Échec de la création",
          policiesError.message || "Une erreur est survenue."
        );
        return;
      }

      // Confirmation de la création
      Alert.alert("Succès", "Politique créée avec succès.", [
        {
          text: "OK",
          onPress: () => {
            reset(); // Réinitialisation du formulaire
            router.back();
          },
        },
      ]);
    } catch (err) {
      Alert.alert("Erreur inattendue", "Veuillez réessayer plus tard.");
      console.error("Erreur inattendue :", err);
    }
  };
  

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      keyboardVerticalOffset={Platform.select({ ios: 0, android: 100 })}
    >
      <View style={styles.container}>
        <ScrollView style={styles.form}>
          {/* Texte d'introduction */}
          <View style={styles.introduction}>
            <View style={styles.introduction}>
              <Text style={styles.introText}>Votre politique d'entreprise</Text>
              <View style={styles.IAButtonContainer}>
                <Text style={styles.IAButtonText}>
                  Laissez-vous assister par l'IA pour générer votre politique
                </Text>
                <TouchableOpacity
                  style={[styles.IAButton, isGenerating && styles.IAButtonDisabled]}
                  onPress={generatePolicyWithAI}
                  disabled={isGenerating}
                >
                  {isGenerating ? (
                    <View style={styles.loadingContainer}>
                      <ActivityIndicator size="small" color="#f99527" />
                      <Text style={styles.loadingText}>Génération en cours...</Text>
                    </View>
                  ) : (
                    <Image
                      style={styles.IAButtonImage}
                      source={require("@/assets/images/qse.png")}
                    />
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </View>

          {/* Champ Enjeu */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Politique</Text>
            <Controller
              control={control}
              name="context"
              rules={{ required: "Le politique est requise." }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  placeholderTextColor={"#6B7280"}
                  style={styles.inputXL}
                  multiline={true} // Active le retour à la ligne
                  textAlignVertical="top" // Aligne le texte en haut
                  placeholder="Politique actuelle"
                  onChangeText={onChange}
                  value={value}
                />
              )}
            />
            {errors.context && (
              <Text style={styles.errorText}>{errors.context.message}</Text>
            )}
          </View>

          {/* Bouton de soumission */}
          <TouchableOpacity
            style={styles.confirmButton}
            onPress={() => {
              handleSubmit(handleSubmitCreatePolicy)();
            }}
          >
            <Text style={styles.confirmButtonText}>Créer</Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    height: "100%",
    width: "100%",
    alignItems: "center",
    alignContent: "center",
    backgroundColor: "#FFFFFF",
  },
  headSection: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 30,
  },
  backButton: {
    width: 24,
    textAlign: "left",
  },
  titleWrapper: {
    display: "flex",
    flexDirection: "row",
    marginVertical: 15,
  },
  title: {
    color: "#262627",
    fontSize: 17,
    textAlign: "left",
  },
  form: {
    width: "100%",
    alignSelf: "center",
    paddingHorizontal: 30,
    marginTop: 20,
  },
  inputXL: {
    borderWidth: 2,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "100%", // S'adapte à la largeur du conteneur parent
    paddingHorizontal: 12,
    paddingVertical: 10, // Ajoute un espace en haut pour ne pas coller au bord
    height: 364, // Hauteur fixe
    color: "#000000",
    textAlignVertical: "top", // Aligne le texte en haut
  },

  input: {
    borderWidth: 2,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "auto",
    padding: 12,
    marginTop: 10,
    color: "#000000",
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginBottom: 5,
  },
  infoText: {
    fontSize: 14,
    marginVertical: 10,
    color: "#555",
  },
  introduction: {
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-between",
    marginBottom: 20,
    width: "100%",
  },
  introText: {
    fontSize: 14,
    color: "#262627",
    marginBottom: 15,
  },
  IAButtonContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f8f9fa",
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#e9ecef",
  },
  IAButtonText: {
    flex: 1,
    fontSize: 14,
    color: "#262627",
    marginRight: 10,
  },
  IAButton: {
    padding: 8,
  },
  IAButtonImage: {
    height: 24,
    width: 24,
  },
  inputContainer: {
    position: "relative",
    marginBottom: 20,
    width: "100%",
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
    textAlign: "left",
  },
  confirmButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    marginBottom: 100,
    backgroundColor: "#f99527",
    marginTop: 10,
  },
  confirmButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  imagePicker: {
    width: "100%",
    padding: 10,
    backgroundColor: "#FFFFFF",
    borderColor: "#CCC",
    borderRadius: 8,
    borderWidth: 2,
    alignItems: "center",
    marginBottom: 10,
    marginTop: 10,
  },
  imagePickerText: {
    color: "#1F2937",
    fontWeight: "bold",
  },
  imagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  imageContainer: {
    width: "100%",
    justifyContent: "center",
  },
  uploadPlaceholder: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  uploadIcon: {
    borderWidth: 1,
    borderColor: "#f99527",
    borderRadius: 7,
    padding: 5,
  },
  uploadText: {
    textAlign: "center",
    fontWeight: "normal",
  },
  fullImagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  dateTimePickerContainer: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    textAlign: "left",
    marginVertical: 20,
  },
  dateTimePickerLabel: {
    color: "#063E76",
    fontSize: 14,
    fontWeight: "bold",
    backgroundColor: "#FFF",
  },
  datePicker: {
    width: "100%",
    marginTop: 10,
    marginBottom: 10,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  loadingText: {
    fontSize: 12,
    color: '#f99527',
  },
  IAButtonDisabled: {
    opacity: 0.7,
  },
});
