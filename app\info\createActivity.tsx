/*
app/info/createActivity.tsx

Formulaire pour créer une activité.

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern` et `Extern` n'ont pas accès à cette page.
*/

import ContainedButton from "@/components/ui/buttons/ContainedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useFocusEffect, useLocalSearchParams, useRouter } from "expo-router";
import { StyleSheet, Image, View, Text, Alert, ScrollView, Platform } from "react-native";
import { useCallback, useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import TypeProcess from "@/types/typeProcess";
import TypeActivity from "@/types/typeActivity";
import CustomizedSelect from "@/components/ui/customizedSelect/customizedSelect";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";

export default function createActivity() {
  const [loading, setLoading] = useState(false);
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [process, setProcess] = useState<TypeProcess[]>([]);
  const { user, companySelected } = useContext(AppContext);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const router = useRouter();
  const { process_id, process_name, path, pilote } = useLocalSearchParams();

  const fetchData = async () => {
    const { data: dataGetted, error } = await supabase
      .from("process")
      .select()
      .eq("uid_admin", user?.uid)
      .eq("company_id", companySelected?.id);

    if (!error) {
      setProcess(
        (dataGetted?.map((item: any) => {
          return { label: item.process_name, value: item.id };
        }) as any) || null
      );
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [])
  );

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
    control,
    reset,
  } = useForm<TypeActivity>({
    defaultValues: {
      activity_name: "",
      description: "",
      inputs: "",
      outputs: "",
      process_id: process_id as any,
    },
  });

  const inputs = [
    {
      label: "Nom",
      name: "activity_name",
      placeholder: "Ecrire le nom de l'activité",
    },
    {
      label: "Description",
      name: "description",
      placeholder: "Ecrire ton description",
      multiline: true,
    },
    {
      label: "Processus concerné",
      name: "process_id",
      placeholder: "",
      type: "Process",
      defaultValue: process_name || [],
      data: process || [],
    },

    {
      label: "Entrants",
      name: "inputs",
      placeholder: "Entrants",
      multiline: true,
    },
    {
      label: "Sortants",
      name: "outputs",
      placeholder: "Sortants",
      multiline: true,
    },
  ];

  const handleCreateActivity = async (data: TypeActivity) => {
    if (
      !data.activity_name &&
      !data.description &&
      !data.inputs &&
      !data.outputs
    ) {
      Toast.show({
        type: "error",
        text1: "Le nom et le prénom sont obligatoires",
        text1Style: { color: "#1C3144" },
      });
    }

    if (data.activity_name && data.description && data.inputs && data.outputs) {
      setLoading(true);

      const created_at = new Date();

      try {
        const { data: dataReturn, error } = await supabase
          .from("activities")
          .insert({
            uid_admin: user?.uid,
            activity_name: data.activity_name,
            description: data.description,
            inputs: data.inputs,
            outputs: data.outputs,
            process_id: process_id ? process_id : data.process_id,
            created_at: created_at,
            company_id: companySelected?.id,
          });

        if (error) {
          console.log("error__", error);
          Toast.show({
            type: "error",
            text1: "Erreur lors de l'enregistrement de l'activité",

            text1Style: { color: "#1C3144" },
          });
          setLoading(false);
          return;
        }

        Toast.show({
          type: "success",
          text1: "L'utilisateur était bien crée",
          text1Style: { color: "#1C3144" },
        });
        reset();

        path
          ? router.replace({
              pathname: path as any,
              params: {
                process_id: process_id,
                pilote: pilote,
                process_name: process_name,
              },
            })
          : router.back();

        setLoading(false);
      } catch (err) {
        setImageUri("");
        console.log("err__", err);
        setLoading(false);

        Toast.show({
          type: "error",
          text1: "Une erreur est survenue",
          text1Style: { color: "#1C3144" },
        });
      }
    }
  };

  return (
    <View style={{ flex: 1 }}>
      <View style={styles.mainContainer}>
        <ScrollView style={styles.container}>
          <View style={styles.containerContent}>
            <View style={styles.registerInfo}>
              <View style={styles.inputs}>
                {inputs.map((input, index) => {
                  if (input?.type === "Process") {
                    if (process_id) {
                      return (
                        <View key={index}>
                          <Text style={styles.label}>{input.label}</Text>
                          <View
                            style={{
                              width: "100%",
                              borderRadius: 4,
                              borderStyle: "solid",
                              borderWidth: 1,
                              borderColor: "#d3d3d3",
                              padding: 10,
                            }}
                          >
                            <Text>{process_name}</Text>
                          </View>
                        </View>
                      );
                    } else {
                      return (
                        <View key={index}>
                          <CustomizedSelect
                            name={input.name}
                            label={input.label}
                            register={register}
                            control={control}
                            errors={errors}
                            setError={setError}
                            data={input.data as any}
                          />
                        </View>
                      );
                    }
                  } else {
                    return (
                      <View key={index}>
                        <InputCustomized
                          label={input.label}
                          placeholder={input.placeholder}
                          register={register}
                          name={input.name}
                          control={control}
                          errors={errors}
                          setError={setError}
                          multiline={input.multiline}
                        />
                      </View>
                    );
                  }
                })}
              </View>
            </View>

            {imageUri && (
              <Image
                source={{ uri: imageUri }}
                style={{
                  width: 150,
                  height: 150,
                  marginVertical: 20,
                  borderRadius: 150,
                }}
              />
            )}

            <View style={styles.buttons}>
              <ContainedButton
                label="Créer"
                backgroundColor="#F99527"
                onPress={handleSubmit(handleCreateActivity)}
                disabled={loading}
              />
            </View>
          </View>
        </ScrollView>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    alignItems: 'center',
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  containerContent: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    width: "100%",
    gap: 20,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
    marginTop: 20,
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
    textAlign: "left",
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
});
