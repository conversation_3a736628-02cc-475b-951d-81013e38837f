/*
app/objectif/updateValue.tsx

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern` et `Extern` n'ont pas accès à cette page.
*/

import ContainedButton from "@/components/ui/buttons/ContainedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useRouter, useLocalSearchParams } from "expo-router";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  Alert,
} from "react-native";

import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import CustomizedSelect from "@/components/ui/customizedSelect/customizedSelect";
import TypeValue from "@/types/typeValue";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";

const inputs = [
  {
    label: "Nom de la valeur",
    name: "value_name",
    placeholder: "Nom de la valeur",
  },
];

const levels = [
  { value: 0, label: "Non maîtrisée" },
  { value: 1, label: "Partiel" },
  { value: 2, label: "Maîtrisée" },
];

export default function UpdateValue() {
  const [loading, setLoading] = useState(false);
  const { user } = useContext(AppContext);
  const router = useRouter();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const {
    uid_user,
    target,
    designation,
    measuring_indicator,
    process_id,
    level,
    created_at: created_at_params,
    company_name,
    objectif_id,
    value_id,
    value_name,
    value_level,
  } = useLocalSearchParams();

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors, touchedFields },
    control,
    reset,
    setValue,
    watch,
  } = useForm<TypeValue>({
    defaultValues: {
      value_name: "",
      objectif_id: objectif_id as any,
      level: 0,
    },
  });

  // Charger les données de la valeur existante
  useEffect(() => {
    const fetchValue = async () => {
      if (!value_id) return;
      
      const { data, error } = await supabase
        .from("values")
        .select("*")
        .eq("id", value_id)
        .single();
      
      if (error) {
        Toast.show({
          type: "error",
          text1: "Erreur lors du chargement de la valeur",
          text1Style: { color: "#1C3144" },
        });
        return;
      }
      
      if (data) {
        setValue("value_name", data.value_name);
        setValue("level", data.level || 0);
        setValue("objectif_id", data.objectif_id);
      }
    };
    
    fetchValue();
  }, [value_id]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const updateValue = async (data: TypeValue) => {
    setLoading(true);

    try {
      const { error } = await supabase
        .from("values")
        .update({
          value_name: data.value_name,
          level: data.level || 0,
        })
        .eq("id", value_id);

      if (error) {
        Toast.show({
          type: "error",
          text1: "Erreur lors de la mise à jour de la valeur",
          text1Style: { color: "#1C3144" },
        });
        setLoading(false);
        return;
      }

      Toast.show({
        type: "success",
        text1: "La valeur a été mise à jour avec succès",
        text1Style: { color: "#1C3144" },
      });

      router.back();
      setLoading(false);
    } catch (err) {
      setLoading(false);

      Toast.show({
        type: "error",
        text1: "Une erreur est survenue",
        text1Style: { color: "#1C3144" },
      });
    }
  };

  return (
    <ScrollView>
      <View style={styles.containerContent}>
        <View style={styles.registerInfo}>
          <View style={styles.inputs}>
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                gap: 10,
              }}
            >
              <View style={{ display: "flex", gap: 10 }}>
                <Text style={{ fontWeight: "bold" }}>
                  Entreprise: {company_name}
                </Text>
                <Text>Descriptif: {designation}</Text>
              </View>
            </View>
            {inputs.map((input, index) => {
              return (
                <View key={index}>
                  <InputCustomized
                    label={input.label}
                    placeholder={input.placeholder}
                    name={input.name}
                    register={register}
                    control={control}
                    errors={errors}
                    setError={setError}
                  />
                </View>
              );
            })}
            <CustomizedSelect
              name="level"
              label="Niveau d'atteinte"
              register={register}
              control={control}
              errors={errors}
              setError={setError}
              data={levels as any}
            />
          </View>
        </View>

        <View style={styles.buttons}>
          <ContainedButton
            label="Mettre à jour"
            backgroundColor="#F99527"
            onPress={handleSubmit(updateValue)}
            disabled={loading}
          />
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  containerContent: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    // padding: 20,
    gap: 20,
    marginVertical: 20,
    width: "80%",
    alignSelf: "center",
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    // backgroundColor: "yellow",
    width: "100%",
    gap: 20,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  buttonPassword: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  titleContainer: {
    flexDirection: "row",
    gap: 8,
  },
  imgIsLoadingPage: {
    height: 75,
    width: 130,
    alignSelf: "center",
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
  input: {
    backgroundColor: "white",
    borderColor: "none",
    height: 40,
    padding: 10,
    borderRadius: 4,
  },
}); 