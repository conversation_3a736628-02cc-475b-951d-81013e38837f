/*
app/objectif/createEquipment.tsx

- Quand le formulaire est validé les données sont envoyées dans la table `equipments`

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `Admin` ont accès à cette page.
*/

import React, { useContext, useEffect, useState, Fragment } from "react";
import ContainedButton from "@/components/ui/buttons/ContainedButton";
import { useRouter } from "expo-router";
import {
  StyleSheet,
  Image,
  View,
  ScrollView,
  Alert,
  TouchableOpacity,
  Text,
  Platform,
  KeyboardAvoidingView,
  TextInput,
} from "react-native";
import { Controller, useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import TypeEquipment from "@/types/typeEquipment";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import "react-native-get-random-values";
import { v4 as uuidv4 } from "uuid";
import DateTimePicker from "@react-native-community/datetimepicker";
import { DateTimePickerAndroid } from "@react-native-community/datetimepicker";
import * as ImagePicker from "expo-image-picker";
import uploadEquipmentImageToStorage from "@/lib/uploadEquipmentImageStorage";
import uploadEquipmentDocumentToStorage from "@/lib/uploadEquipmentDocumentStorage";
import { Dropdown } from "react-native-element-dropdown";
import { Octicons } from "@expo/vector-icons";
import * as DocumentPicker from "expo-document-picker";
import { WebDateTimePicker } from "@/components/ui/datepicker/WebDateTimePicker";

export default function CreateEquipment() {
  const { user, companySelected } = useContext(AppContext);
  const [companyUsers, setCompanyUsers] = useState<any>([]);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedType, setSelectedType] = useState<string>("");
  const [purchaseDate, setPurchaseDate] = useState(new Date());
  const [documentUri, setDocumentUri] = useState<string | null>(null);
  const [imageUri, setImageUri] = useState<string | null>(null);
  const router = useRouter();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
    control,
    reset,
  } = useForm<TypeEquipment>({
    defaultValues: {
      name: "",
      user_id: "",
      reference: "",
      type: "",
      cycle: "",
      price: 0,
      document: "",
      image: "",
    },
  });

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern", "Supplier"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page.",
      );
      navigation.goBack();
    }
  }, [user]);

  // Récupérer les utilisateurs de l'entreprise
  useEffect(() => {
    const fetchCompanyUsers = async () => {
      if (!companySelected?.id) return;

      const { data, error } = await supabase
        .from("company_users")
        .select("user_id, users (first_name, last_name, status)")
        .eq("company_id", companySelected.id)
        .neq("users.status", "Supplier");

      if (error) {
        console.error(
          "Erreur lors de la récupération des utilisateurs :",
          error,
        );
        return;
      }

      // Filtrer les entrées où users est null et ajouter des vérifications de sécurité
      const formattedUsers = data
        .filter((entry: any) => entry.users && entry.users.first_name && entry.users.last_name)
        .map((entry: any) => ({
          id: entry.user_id,
          full_name: `${entry.users.first_name} ${entry.users.last_name}`,
        }));

      setCompanyUsers(formattedUsers);
    };

    fetchCompanyUsers();
  }, [companySelected]);

  // Cycles
  const equipmentCycle = [
    { value: "one_mounth", label: "1 mois" },
    { value: "three_mounths", label: "3 mois" },
    { value: "six_mounths", label: "6 mois" },
    { value: "one_year", label: "1 an" },
  ];

  // Types
  const equipmentType = [
    { value: "cyclic", label: "Cyclique" },
    { value: "custom", label: "Personnalisé" },
  ];

  // Statuts
  const equipmentStatus = [
    { value: "compliant", label: "Conforme" },
    { value: "to_be_checked", label: "À vérifier" },
    { value: "under_verification", label: "En cours de vérification" },
    { value: "out_of_service", label: "Hors service" },
  ];

  // Fonction pour calculer la date de vérification en fonction du cycle
  const calculateVerificationDate = (cycle: string, date: Date) => {
    const purchaseDate = new Date(date);
    const verificationDate = new Date(purchaseDate);

    switch (cycle) {
      case "one_mounth":
        verificationDate.setMonth(verificationDate.getMonth() + 1);
        break;
      case "three_mounths":
        verificationDate.setMonth(verificationDate.getMonth() + 3);
        break;
      case "six_mounths":
        verificationDate.setMonth(verificationDate.getMonth() + 6);
        break;
      case "one_year":
        verificationDate.setFullYear(verificationDate.getFullYear() + 1);
        break;
      default:
        return null;
    }

    return verificationDate;
  };

  // Mise à jour de la date de vérification lorsque le cycle ou la date d'achat change
  useEffect(() => {
    if (selectedType === "cyclic" && purchaseDate) {
      const cycle = control._formValues.cycle;
      if (cycle) {
        const newVerificationDate = calculateVerificationDate(cycle, purchaseDate);
        if (newVerificationDate) {
          console.log("Date d'achat:", purchaseDate);
          console.log("Cycle sélectionné:", cycle);
          console.log("Nouvelle date de vérification calculée:", newVerificationDate);
          setSelectedDate(newVerificationDate);
        }
      }
    }
  }, [purchaseDate, control._formValues.cycle, selectedType]);

  // Date (iOS)
  const handleDateChange = (event: any, date: any) => {
    if (date) {
      setPurchaseDate(date);
      if (selectedType === "cyclic") {
        const cycle = control._formValues.cycle;
        if (cycle) {
          const newVerificationDate = calculateVerificationDate(cycle, date);
          if (newVerificationDate) {
            console.log("Nouvelle date de vérification après changement de date d'achat:", newVerificationDate);
            setSelectedDate(newVerificationDate);
          }
        }
      } else {
        // Vérifier si la date de vérification n'est pas antérieure à la date d'achat
        if (date < purchaseDate) {
          Toast.show({
            type: "error",
            text1: "Date invalide",
            text1Style: { color: "#1C3144" },
            text2: "La date de vérification ne peut pas être antérieure à la date d'achat.",
            text2Style: { color: "#1C3144" },
          });
          return;
        }
        setSelectedDate(date);
      }
    }
  };

  // Date (Android)
  const showDatePicker = () => {
    DateTimePickerAndroid.open({
      value: selectedType === "cyclic" ? purchaseDate : selectedDate,
      mode: "date",
      onChange: handleDateChange,
      display: "default",
      minimumDate: selectedType === "custom" ? purchaseDate : undefined,
    });
  };

  // Sélectionner un document
  const handlePickDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: "application/pdf",
        copyToCacheDirectory: true,
      });

      if (result.canceled) return;

      const selectedFile = result.assets?.[0];
      if (selectedFile && selectedFile.mimeType === "application/pdf") {
        console.log("Document PDF sélectionné:", selectedFile);
        setDocumentUri(selectedFile.uri);
      } else {
        Toast.show({
          type: "error",
          text1: "Format non supporté",
          text1Style: { color: "#1C3144" },
          text2: "Seuls les fichiers PDF sont acceptés.",
          text2Style: { color: "#1C3144" },
        });
      }
    } catch (error) {
      console.error("Erreur lors de la sélection du fichier :", error);
      Toast.show({
        type: "error",
        text1: "Erreur",
        text1Style: { color: "#1C3144" },
        text2: "Impossible de sélectionner le fichier.",
        text2Style: { color: "#1C3144" },
      });
    }
  };

  // Sélectionner une image
  const handlePickImage = async () => {
    const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (permissionResult.granted === false) {
      Toast.show({
        type: "error",
        text1: "Permissions refusées",
        text1Style: { color: "#1C3144" },
        text2: "Vous devez autoriser l'accès à vos photos.",
        text2Style: { color: "#1C3144" },
      });
      return;
    }

    const pickerResult = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!pickerResult?.canceled && pickerResult.assets && pickerResult.assets.length > 0) {
      setImageUri(pickerResult.assets[0].uri);
    }
  };

  // Validation du formulaire
  const handleCreateEquipment = async (data: TypeEquipment) => {
    if (!purchaseDate) {
      Toast.show({
        type: "error",
        text1: "Veuillez sélectionner une date d'achat",
        text1Style: { color: "#1C3144" },
      });
      return;
    }

    if (!data.name || !data.type || !data.reference || !data.price) {
      Toast.show({
        type: "error",
        text1: "Tous les champs obligatoires doivent être remplis",
        text1Style: { color: "#1C3144" },
      });
      return;
    }

    const generatedUUID = uuidv4();
    const createdAt = new Date().toISOString();

    try {
      let uploadedImageUrl = null;
      let uploadedDocumentUrl = null;

      // Upload document si il existe
      if (documentUri) {
        console.log("Upload du document PDF:", documentUri);
        const uploadDocumentResult = await uploadEquipmentDocumentToStorage(
          documentUri,
          generatedUUID
        );
        uploadedDocumentUrl = uploadDocumentResult?.url || null;
        console.log("URL du document uploadé:", uploadedDocumentUrl);
      }

      // Upload image si elle existe
      if (imageUri && imageUri.trim() !== '') {
        console.log("Upload de l'image:", imageUri);
        const uploadImageResult = await uploadEquipmentImageToStorage(
          imageUri,
          generatedUUID
        );
        uploadedImageUrl = uploadImageResult?.url || null;
        console.log("URL de l'image uploadée:", uploadedImageUrl);
      }

      // Conversion du prix en nombre
      const price = parseFloat(data.price.toString().replace(',', '.'));

      // Calculer la date de vérification en fonction du cycle
      let verificationDate = new Date(purchaseDate);
      if (selectedType === "cyclic" && data.cycle) {
        console.log("\nCalcul de la nouvelle date de vérification:");
        console.log("Date d'achat initiale:", purchaseDate.toISOString());
        console.log("Cycle sélectionné:", data.cycle);
        
        const calculatedDate = calculateVerificationDate(data.cycle, purchaseDate);
        if (calculatedDate) {
          verificationDate = calculatedDate;
          console.log("Nouvelle date de vérification calculée:", verificationDate.toISOString());
        }
      }
      
      // Préparation des données pour l'insertion
      const equipmentData = {
        uuid: generatedUUID,
        user_id: selectedType === "custom" ? data.user_id || null : null,
        company_id: companySelected?.id,
        name: data.name,
        reference: data.reference,
        type: data.type,
        status: "compliant",
        cycle: selectedType === "cyclic" ? data.cycle : null,
        price: price,
        document: uploadedDocumentUrl,
        image: uploadedImageUrl,
        purchase_date: purchaseDate.toISOString(),
        verification_date: verificationDate.toISOString(),
        created_at: createdAt,
      };

      console.log("\n=== DONNÉES FINALES À ENVOYER ===");
      console.log(JSON.stringify(equipmentData, null, 2));

      const { data: insertedData, error } = await supabase.from("equipments").insert(equipmentData).select();

      if (error) {
        console.error("Erreur lors de l'ajout de l'équipement :", error);
        Toast.show({
          type: "error",
          text1: "Erreur lors de la création de l'équipement",
          text1Style: { color: "#1C3144" },
        });
        return;
      }

      console.log("Données insérées avec succès:", insertedData);

      Toast.show({
        type: "success",
        text1: "Équipement créé avec succès",
        text1Style: { color: "#1C3144" },
      });

      Alert.alert("Équipement créé", "Équipement créé avec succès.");

      router.back();
      reset();
    } catch (err) {
      console.error("Erreur inattendue :", err);
      Toast.show({
        type: "error",
        text1: "Une erreur est survenue",
        text1Style: { color: "#1C3144" },
      });
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 0}
    >
      <ScrollView
        style={styles.form}
        contentContainerStyle={styles.formContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.container}>
          {/* Formulaire */}
          <View style={styles.form}>
            {/* Nom */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Nom</Text>
              <Controller
                control={control}
                name="name"
                rules={{ required: "Le libellé est requis." }}
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    maxLength={50}
                    placeholderTextColor={"#6B7280"}
                    style={styles.input}
                    placeholder="Nom de l'équipement"
                    onChangeText={onChange}
                    value={value}
                  />
                )}
              />
              {errors.name && (
                <Text style={styles.errorText}>{errors.name.message}</Text>
              )}
            </View>

            {/* Type */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Type EPI</Text>
              <Controller
                control={control}
                name="type"
                rules={{ required: "Veuillez sélectionner un type." }}
                render={({ field: { onChange, value } }) => (
                  <Dropdown
                    style={dropdownStyles.dropdown}
                    placeholderStyle={dropdownStyles.placeholderStyle}
                    selectedTextStyle={dropdownStyles.selectedTextStyle}
                    data={equipmentType}
                    placeholder="Sélectionner un type d'équipement"
                    value={value}
                    onChange={(item) => {
                      onChange(item.value);
                      setSelectedType(item.value);
                    }}
                    labelField="label"
                    valueField="value"
                  />
                )}
              />
              {errors.type && (
                <Text style={styles.errorText}>{errors.type.message}</Text>
              )}
            </View>

            {/* Condition : si le champ `Type = Custom` */}
            {selectedType === "custom" && (
              <Fragment>
                {/* Nom d'utilisateur */}
                <View style={styles.inputContainer}>
                  <Text style={styles.label}>Nom d'utilisateur</Text>
                  <Controller
                    control={control}
                    name="user_id"
                    render={({ field: { onChange, value } }) => (
                      <Dropdown
                        style={dropdownStyles.dropdown}
                        placeholderStyle={dropdownStyles.placeholderStyle}
                        selectedTextStyle={dropdownStyles.selectedTextStyle}
                        data={companyUsers.map((user: any) => ({
                          value: user.id,
                          label: user.full_name,
                        }))}
                        placeholder="Sélectionner un utilisateur"
                        value={value}
                        onChange={(item) => {
                          console.log("Selected user ID:", item.value); // Debug
                          onChange(item.value);
                        }}
                        labelField="label"
                        valueField="value"
                      />
                    )}
                  />
                </View>

                {/* Date de vérification */}
                {Platform.OS === "android" ? (
                  <TouchableOpacity
                    onPress={showDatePicker}
                    style={styles.dateTimePickerContainer}
                  >
                    <Text style={styles.dateTimePickerLabel}>
                      Date de vérification
                    </Text>
                    <Text style={styles.datePicker}>
                      {selectedDate.toLocaleDateString()}
                    </Text>
                  </TouchableOpacity>
                ) : (
                  <View style={styles.dateTimePickerContainer}>
                    <Text style={styles.label}>Date de vérification</Text>
                    <DateTimePicker
                      value={selectedDate}
                      mode="date"
                      display="default"
                      onChange={handleDateChange}
                      style={styles.datePicker}
                      minimumDate={purchaseDate}
                    />
                  </View>
                )}
              </Fragment>
            )}

            {/* Condition : si le champ `Type = Cyclic` */}
            {selectedType === "cyclic" && (
              <>
                {/* Cycle */}
                <View style={styles.inputContainer}>
                  <Text style={styles.label}>Cycle</Text>
                  <Controller
                    control={control}
                    name="cycle"
                    rules={{ required: "Veuillez sélectionner un cycle." }}
                    render={({ field: { onChange, value } }) => (
                      <Dropdown
                        style={dropdownStyles.dropdown}
                        placeholderStyle={dropdownStyles.placeholderStyle}
                        selectedTextStyle={dropdownStyles.selectedTextStyle}
                        data={equipmentCycle}
                        placeholder="Sélectionner un cycle"
                        value={value}
                        onChange={(item) => {
                          onChange(item.value);
                          console.log("\n=== CALCUL DE LA DATE DE VÉRIFICATION ===");
                          console.log("Cycle sélectionné:", item.value);
                          console.log("Date d'achat actuelle:", purchaseDate.toISOString());
                          
                          const newVerificationDate = calculateVerificationDate(item.value, purchaseDate);
                          if (newVerificationDate) {
                            console.log("Nouvelle date de vérification calculée:", newVerificationDate.toISOString());
                            console.log("Date de vérification (format lisible):", newVerificationDate.toLocaleDateString());
                            setSelectedDate(newVerificationDate);
                          }
                        }}
                        labelField="label"
                        valueField="value"
                      />
                    )}
                  />
                  {errors.cycle && (
                    <Text style={styles.errorText}>{errors.cycle.message}</Text>
                  )}
                </View>

                {/* Date de vérification (automatique) */}
                <View style={styles.dateTimePickerContainer}>
                  <Text style={styles.label}>Date de vérification</Text>
                  <Text style={styles.datePicker}>
                    {selectedDate.toLocaleDateString()}
                  </Text>
                </View>
              </>
            )}

            {/* Référence */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Référence</Text>
              <Controller
                control={control}
                name="reference"
                rules={{ required: "La référence est requise." }}
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    maxLength={300}
                    placeholderTextColor={"#6B7280"}
                    style={styles.input}
                    placeholder="Référence de l'équipement"
                    onChangeText={onChange}
                    value={value}
                    multiline
                  />
                )}
              />
              {errors.reference && (
                <Text style={styles.errorText}>{errors.reference.message}</Text>
              )}
            </View>

            {/* Date Picker pour Android */}
            {Platform.OS === "android" && (
              <TouchableOpacity
                onPress={showDatePicker}
                style={styles.dateTimePickerContainer}
              >
                <Text style={styles.dateTimePickerLabel}>Date d'achat</Text>
                <Text style={styles.datePicker}>
                  {purchaseDate.toLocaleDateString()}
                </Text>
              </TouchableOpacity>
            )}

            {/* Date Picker pour iOS */}
            {Platform.OS === "ios" && (
              <View style={styles.dateTimePickerContainer}>
                <Text style={styles.label}>Date d'achat</Text>
                <DateTimePicker
                  value={purchaseDate}
                  mode="date"
                  display="default"
                  onChange={(event, date) => {
                    if (date) {
                      setPurchaseDate(date);
                      if (selectedType === "cyclic") {
                        const cycle = control._formValues.cycle;
                        if (cycle) {
                          const newVerificationDate = calculateVerificationDate(cycle, date);
                          if (newVerificationDate) {
                            setSelectedDate(newVerificationDate);
                          }
                        }
                      } else if (selectedDate < date) {
                        setSelectedDate(date);
                      }
                    }
                  }}
                  style={styles.datePicker}
                />
              </View>
            )}

            {/* Date Picker pour Web */}
            {Platform.OS === "web" && (
              <View style={styles.dateTimePickerContainer}>
                <Text style={styles.label}>Date d'achat</Text>
                <WebDateTimePicker
                  value={purchaseDate}
                  onChange={(date) => {
                    if (date) {
                      setPurchaseDate(date);
                      if (selectedType === "cyclic") {
                        const cycle = control._formValues.cycle;
                        if (cycle) {
                          const newVerificationDate = calculateVerificationDate(cycle, date);
                          if (newVerificationDate) {
                            setSelectedDate(newVerificationDate);
                          }
                        }
                      } else if (selectedDate < date) {
                        setSelectedDate(date);
                      }
                    }
                  }}
                  style={styles.datePicker}
                />
              </View>
            )}

            {/* Coût */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Coût</Text>
              <Controller
                control={control}
                name="price"
                rules={{ required: "Le prix est requis." }}
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    maxLength={10}
                    placeholderTextColor={"#6B7280"}
                    style={styles.input}
                    placeholder="Coût de l'équipement"
                    onChangeText={(text) => {
                      // Permettre uniquement les chiffres et la virgule
                      const formattedText = text.replace(/[^0-9,]/g, '');
                      // S'assurer qu'il n'y a qu'une seule virgule
                      const parts = formattedText.split(',');
                      if (parts.length > 2) return;
                      // Limiter à 2 décimales après la virgule
                      if (parts[1] && parts[1].length > 2) return;
                      onChange(formattedText);
                    }}
                    value={value?.toString()}
                    keyboardType="decimal-pad"
                  />
                )}
              />
              {errors.price && (
                <Text style={styles.errorText}>{errors.price.message}</Text>
              )}
            </View>

            {/* Document */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Document</Text>
              <TouchableOpacity
                style={styles.imagePicker}
                onPress={handlePickDocument}
              >
                {documentUri ? (
                  <View style={styles.documentPreview}>
                    <Octicons name="file" size={24} color="#f99527" />
                    <Text style={styles.documentText}>Document PDF</Text>
                  </View>
                ) : (
                  <View style={styles.uploadPlaceholder}>
                    <Octicons
                      style={styles.uploadIcon}
                      name="upload"
                      size={24}
                      color="black"
                    />
                    <Text style={styles.uploadText}>Choisir un document PDF</Text>
                  </View>
                )}
              </TouchableOpacity>
            </View>

            {/* Photo */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Photo</Text>
              <TouchableOpacity
                style={styles.imagePicker}
                onPress={handlePickImage}
              >
                {imageUri ? (
                  <View style={styles.imagePreviewContainer}>
                    <Image source={{ uri: imageUri }} style={styles.imagePreview} />
                  </View>
                ) : (
                  <View style={styles.uploadPlaceholder}>
                    <Octicons
                      style={styles.uploadIcon}
                      name="upload"
                      size={24}
                      color="black"
                    />
                    <Text style={styles.uploadText}>Choisir une photo</Text>
                  </View>
                )}
              </TouchableOpacity>
            </View>

            {/* Bouton de soumission */}
            <ContainedButton
              label="Créer"
              backgroundColor="#F99527"
              onPress={async () => {
                await handleSubmit(handleCreateEquipment)();
              }}
            />
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
      width: '100%',
    } : {}),
  },
  form: {
    padding: 20,
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
      width: '100%',
    } : {}),
  },
  formContent: {
    gap: 20,
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
      width: '100%',
    } : {}),
  },
  headSection: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 30,
  },
  imageGrid: {
    flexDirection: "row",
    flexWrap: "wrap", // ✅ Permet le passage à la ligne après 2 images
    justifyContent: "flex-start", // ✅ Alignement des images
    gap: 10, // ✅ Espacement entre les images
    marginBottom: 20,
  },
  backButton: {
    width: 24,
    textAlign: "left",
  },
  titleWrapper: {
    display: "flex",
    flexDirection: "row",
    marginVertical: 15,
  },
  title: {
    color: "#262627",
    fontSize: 17,
    textAlign: "left",
  },
  input: {
    borderWidth: 2,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "auto",
    padding: 12,
    marginTop: 10,
    color: "#000000",
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginBottom: 5,
  },
  infoText: {
    fontSize: 14,
    marginVertical: 10,
    color: "#555",
  },
  inputContainer: {
    marginBottom: 20,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
    textAlign: "left",
  },
  confirmButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    marginBottom: 100,
    backgroundColor: "#f99527",
    marginTop: 10,
  },
  confirmButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  imagePicker: {
    width: "100%",
    height: 200,
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 20,
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  imagePickerText: {
    color: "#1F2937",
    fontWeight: "bold",
  },
  imagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  imageContainer: {
    width: "100%",
    justifyContent: "center",
  },
  uploadPlaceholder: {
    width: "100%",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    gap: 10,
  },
  uploadIcon: {
    borderWidth: 1,
    borderColor: "#f99527",
    borderRadius: 7,
    padding: 5,
  },
  uploadText: {
    fontSize: 14,
    color: "#262627",
  },
  fullImagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  dateTimePickerContainer: {
    width: "100%",
    marginBottom: 20,
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  dateTimePickerLabel: {
    color: "#063E76",
    fontSize: 14,
    fontWeight: "bold",
    backgroundColor: "#FFF",
  },
  datePicker: {
    width: "100%",
    marginTop: 10,
    marginBottom: 10,
  },
  documentPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 10,
  },
  documentText: {
    fontSize: 16,
    color: '#262627',
  },
  imagePreviewContainer: {
    width: '100%',
    alignItems: 'center',
  },
});

const dropdownStyles = StyleSheet.create({
  dropdown: {
    marginVertical: 16,
    height: 50,
    backgroundColor: "white",
    borderRadius: 12,
    padding: 12,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,

    elevation: 2,
  },
  icon: {
    marginRight: 5,
  },
  item: {
    padding: 17,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  textItem: {
    flex: 1,
    fontSize: 16,
  },
  placeholderStyle: {
    fontSize: 16,
  },
  selectedTextStyle: {
    fontSize: 16,
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 16,
  },
});
