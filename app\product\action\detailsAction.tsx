import { useRouter, useLocalSearchParams, useFocusEffect } from "expo-router";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  Platform,
} from "react-native";

import { useCallback, useContext, useState } from "react";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Paper from "@/components/common/paper";
import Header from "@/components/common/Header";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import TypeActionEvent from "@/types/typeActionEvent";
import PaperInfo from "@/components/common/paperInfo";
import { Octicons } from "@expo/vector-icons";

export default function DetailsAction() {
  const { user } = useContext(AppContext);
  const router = useRouter();
  const [data, setData] = useState<TypeActionEvent>();
  const { action_id, product_id } = useLocalSearchParams();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const [productDocs, setProductDocs] = useState<
    { name: string; created_at: string; id: number }[]
  >([]);

  // 🔥 Fonction de suppression de l'action
  const handleDeleteAction = async () => {
    const confirmDelete =
      Platform.OS === "web"
        ? window.confirm("Êtes-vous sûr de vouloir supprimer cette action ?")
        : await new Promise((resolve) =>
            Alert.alert(
              "Confirmation",
              "Êtes-vous sûr de vouloir supprimer cette action ?",
              [
                {
                  text: "Annuler",
                  style: "cancel",
                  onPress: () => resolve(false),
                },
                { text: "Supprimer", onPress: () => resolve(true) },
              ]
            )
          );

    if (!confirmDelete) return;

    try {
      const { error } = await supabase
        .from("event_actions")
        .delete()
        .eq("id", action_id);
      if (error) throw error;

      alert("L'action a été supprimée avec succès.");
      navigation.goBack();
    } catch (error) {
      console.error("❌ Erreur lors de la suppression :", error);
      alert("Une erreur est survenue lors de la suppression.");
    }
  };

  // 🔄 Récupération des données de l'action
  const fetchData = async () => {
    const { data: dataGetted, error } = await supabase
      .from("event_actions")
      .select("*, users:users!event_actions_administrator_fkey(*), process(*)")
      .eq("id", action_id)
      .single();

    if (!error) {
      setData(dataGetted);
    } else {
      console.error("Erreur de récupération des données :", error);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [])
  );

  function formatDate(dateString: string) {
    const date = new Date(dateString);
    return date.toLocaleDateString("fr-FR");
  }

  const dataChamps = [
    {
      label: "Libellé de l'action",
      value: data?.wording_action,
    },
    {
      label: "Date prévu",
      value: formatDate(data?.date || ""),
    },
    {
      label: "Responsable de l'action",
      value: data?.users.first_name + " " + data?.users.last_name,
    },
    {
      label: "Processus concerné",
      value: data?.process.process_name,
    },
    {
      label: "Champ d'action",
      value: data?.action_champ === "externe" ? "Externe" : "Interne",
    },
    {
      label: "type d'action",
      value:
        data?.type === "immediate" ? "Action préventive" : "Action corrective",
    },
  ];
  console.log(action_id);

  const fetchDocs = async () => {
    const { data: dataGetted, error } = await supabase
      .from("docsProduct")
      .select("*")
      .eq("product_id", product_id)
      .eq("action_id", action_id)

      .eq("type", "action");

    if (!error) {
      setProductDocs(dataGetted as any);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchDocs();
    }, [])
  );

  return (
    <ScrollView>
      <View style={styles.containerContent}>
        <TouchableOpacity style={styles.container}>
          <View style={styles.containerImgInfo}>
            <View style={styles.containerInfo}>
              <View
                style={{
                  justifyContent: "space-between",
                  flexDirection: "row",
                  // backgroundColor: "red",
                  width: "100%",
                }}
              >
                <View style={{ maxWidth: "80%", paddingBottom: 20 }}>
                  {data?.wording_action && (
                    <Text
                      style={{
                        fontWeight: "bold",
                        fontSize: 20,
                      }}
                    >
                      {data?.wording_action}
                    </Text>
                  )}
                </View>
              </View>
              {dataChamps?.map((item: any) => {
                return (
                  <View style={{ paddingBottom: 10 }}>
                    <Text
                      style={{
                        color: "#1C3144",
                        fontWeight: "bold",
                        paddingBottom: 10,
                      }}
                    >
                      {item.label}
                    </Text>

                    <Text style={{ color: "#525252" }}>{item.value}</Text>
                  </View>
                );
              })}
            </View>
          </View>
        </TouchableOpacity>

        <View style={{ gap: 10, width: "100%" }}>
          <View style={styles.header}>
            <Text style={{ fontWeight: "bold" }}>Documents techniques</Text>
            <TouchableOpacity
              onPress={() =>
                router.push({
                  pathname: "/product/action/createDoc",
                  params: {
                    product_id: product_id,
                    action_id: action_id,
                  },
                })
              }
            >
              <Octicons name="diff-added" size={25} color="#F99527" />
            </TouchableOpacity>
          </View>
          <View style={{ gap: 10 }}>
            {productDocs.length > 0 ? (
              productDocs.map((item, index) => {
                return (
                  <PaperInfo
                    title={item.name}
                    date={new Date(item.created_at)}
                    badgeShow={false}
                    onPress={() =>
                      router.push({
                        pathname: "/product/doc/detailsDoc",
                        params: { doc_id: item.id, product_id: product_id },
                      })
                    }
                  />
                );
              })
            ) : (
              <Text>Vous n'avez pas encore crée des documents</Text>
            )}
          </View>
        </View>

        {user?.status === "Admin" && (
          <View style={styles.deleteButtonContainer}>
            <TouchableOpacity
              style={styles.deleteButton}
              onPress={handleDeleteAction}
            >
              <Text style={styles.deleteButtonText}>
                Supprimer cette action
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  containerContent: {
    width: "100%",
    paddingHorizontal: 10,
  },
  deleteButtonContainer: {
    width: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    marginVertical: 30,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    // paddingHorizontal: 10,
    width: "100%",
  },
  deleteButton: {
    width: 200,
    backgroundColor: "#ff4f42",
    padding: 10,
    borderRadius: 8,
    alignItems: "center",
  },
  deleteButtonText: {
    color: "#FFFFFF",
    fontWeight: "normal",
  },
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 10,
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: "#E6E6E6",
    borderRadius: 10,
    width: "100%",
    paddingVertical: 15, // ✅ Ajout de padding pour éviter un effet trop collé
    marginVertical: 10,
    // Ombre iOS (plus douce)
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 }, // ✅ Ajusté pour iOS
    shadowOpacity: 0.1, // ✅ Réduit pour éviter un effet trop marqué
    shadowRadius: 4, // ✅ Plus petit que sur Android

    // Ombre Android (plus visible)
    elevation: 5, // ✅ Permet une ombre plus nette sur Android
  },
  containerInfo: {
    gap: 5,
    flexShrink: 1, // Permet au texte de ne pas dépasser
    // maxWidth: "90%", // Ajuste la largeur max
  },
  containerImgInfo: {
    gap: 30,
    flexDirection: "row",
    padding: 10,
    alignItems: "center",
    // width: "50%",
  },
});
