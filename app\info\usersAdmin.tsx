import { StyleSheet, ScrollView, View, TextInput, TouchableWithoutFeedback, Platform, Keyboard } from "react-native";

import { useCallback, useContext, useEffect, useState } from "react";
import { AppContext } from "@/state/AppContext";
import UsersInfosSection from "@/components/infoSection/users";
import { FontAwesome, Octicons } from "@expo/vector-icons";
import { useFocusEffect, useRouter } from "expo-router";
import typeUserByAdmin from "@/types/typeUserByAdmin";
import { supabase } from "@/lib/supabase";

export default function UsersAdmin() {
  const router = useRouter();
  const [usersAdmin, setUsersAdmin] = useState<typeUserByAdmin[] | null>(null);
  const [isLanding, setIsLanding] = useState(false);
  const { companySelected } = useContext(AppContext);
  const [searchQuery, setSearchQuery] = useState(""); // État pour la barre de recherche
  const [filteredDataList, setFilteredDataList] = useState<any>([]); // Liste filtrée

  useEffect(() => {
    const filteredList = usersAdmin?.filter((item) =>
      `${item.first_name} ${item.last_name}`
        .toLowerCase()
        .includes(searchQuery.toLowerCase())
    );
    setFilteredDataList(filteredList);
  }, [searchQuery, usersAdmin]);

  const { user } = useContext(AppContext);

  const fetchDataUsers = async () => {
    setIsLanding(true);
  
    // 🔥 Récupérer les utilisateurs de l'entreprise via `company_users`
    const { data, error } = await supabase
      .from("company_users")
      .select(`
        user_id,
        users: user_id (
          id, uid, uid_admin, created_at, first_name, last_name, profil_picture, is_active, email, profil, status
        )
      `)
      .eq("company_id", companySelected?.id); // ✅ Filtrer par l'entreprise actuelle
  
    if (error) {
      console.error("Erreur récupération utilisateurs :", error);
      setIsLanding(false);
      return;
    }
  
    // ✅ Extraire les utilisateurs et aplatir le tableau
    const usersList = data.map((entry) => entry.users).flat(); 
  
    setUsersAdmin(usersList); // ✅ Maintenant c'est bien un `typeUserByAdmin[]`
    setIsLanding(false);
  };
  
  

  useFocusEffect(
    useCallback(() => {
      fetchDataUsers();
    }, [])
  );

  return (
    <TouchableWithoutFeedback 
        onPress={Platform.OS !== "web" ? Keyboard.dismiss : undefined} 
        accessible={false}
      >
      <View style={{ flex: 1 }}>
        {/* Barre de recherche - partie fixe */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputWrapper}>
            <FontAwesome
              name="search"
              size={24}
              color="#3C3c4399"
              style={styles.searchIcon}
            />
            <TextInput
              style={styles.searchInput}
              placeholder="Rechercher par nom..."
              placeholderTextColor="#3C3c4399"
              value={searchQuery}
              onChangeText={(text) => setSearchQuery(text)}
            />
          </View>
        </View>

        {/* Liste des utilisateurs - partie défilante */}
        <ScrollView style={{ paddingBottom: 250 }}>
          <View style={{ marginBottom: 250 }}>
            <UsersInfosSection
              icon={
                <Octicons
                  name="diff-added"
                  size={24}
                  color="#F99527"
                  onPress={() => router.navigate("/info/createUserByAdmin")}
                />
              }
              data={filteredDataList as any}
              isLanding={isLanding}
            />
          </View>
        </ScrollView>
      </View>
    </TouchableWithoutFeedback>
  );
}

const styles = StyleSheet.create({
  searchContainer: {
    width: "100%",
    marginBottom: 20,
    marginTop: 10,
    paddingHorizontal: 10,
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  searchInputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1C314414",
    borderRadius: 8,
    paddingHorizontal: 10,
    height: 60,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    padding: 10,
    fontSize: 16,
    color: "#000000",
  },
});
