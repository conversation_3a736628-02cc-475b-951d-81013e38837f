/*
app/objectif/assignEquipment.tsx

- Quand le formulaire est validé les données sont envoyées dans la table `equipments`

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `Admin` ont accès à cette page.
*/

import React, { useContext, useEffect, useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  Platform,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Controller, useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import { Dropdown } from "react-native-element-dropdown";
import DateTimePicker from "@react-native-community/datetimepicker";
import { DateTimePickerAndroid } from "@react-native-community/datetimepicker";
import ContainedButton from "@/components/ui/buttons/ContainedButton";
import { WebDateTimePicker } from "@/components/ui/datepicker/WebDateTimePicker";

export default function AssignEquipment() {
  const { user, companySelected } = useContext(AppContext);
  const params = useLocalSearchParams();
  const router = useRouter();
  const [companyUsers, setCompanyUsers] = useState<any>([]);
  const [deliveryDate, setDeliveryDate] = useState<Date>(new Date());
  const [verificationDate, setVerificationDate] = useState<Date>(new Date());
  const [dateError, setDateError] = useState<string | null>(null);
  const [equipment, setEquipment] = useState<any>(null);

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm({
    defaultValues: {
      user_id: "",
    },
  });

  // Récupérer les informations de l'équipement
  useEffect(() => {
    const fetchEquipmentDetails = async () => {
      if (!params.uuid) return;

      const { data, error } = await supabase
        .from("equipments")
        .select("*, users(first_name, last_name)")
        .eq("uuid", params.uuid)
        .single();

      if (error) {
        console.error("Erreur lors de la récupération des informations:", error);
        return;
      }

      setEquipment(data);

      // Mettre à jour les dates si elles existent
      if (data.delivery_date) {
        setDeliveryDate(new Date(data.delivery_date));
      }
      if (data.verification_date) {
        setVerificationDate(new Date(data.verification_date));
      }

      // Mettre à jour l'utilisateur si assigné
      if (data.user_id) {
        setValue("user_id", data.user_id);
      }
    };

    fetchEquipmentDetails();
  }, [params.uuid]);

  // Récupérer les utilisateurs de l'entreprise
  useEffect(() => {
    const fetchCompanyUsers = async () => {
      if (!companySelected?.id) return;

      const { data, error } = await supabase
        .from("company_users")
        .select("user_id, users (first_name, last_name, status)")
        .eq("company_id", companySelected.id)
        .neq("users.status", "Supplier");

      if (error) {
        console.error("Erreur lors de la récupération des utilisateurs :", error);
        return;
      }

      const formattedUsers = data
        .filter((entry: any) => entry.users && entry.users.first_name && entry.users.last_name)
        .map((entry: any) => ({
          value: entry.user_id,
          label: `${entry.users.first_name} ${entry.users.last_name}`,
        }));

      setCompanyUsers(formattedUsers);
    };

    fetchCompanyUsers();
  }, [companySelected]);

  // Date (iOS)
  const handleDeliveryDateChange = (event: any, date: any) => {
    if (date) {
      const newDate = new Date(date);
      setDeliveryDate(newDate);
      // Vérifier si la date de vérification est antérieure à la nouvelle date de remise
      if (verificationDate < newDate) {
        setDateError("La date de vérification doit être supérieure à la date de remise");
      } else {
        setDateError(null);
      }
    }
  };

  const handleVerificationDateChange = (event: any, date: any) => {
    if (date) {
      const newDate = new Date(date);
      setVerificationDate(newDate);
      // Vérifier si la nouvelle date de vérification est antérieure à la date de remise
      if (newDate < deliveryDate) {
        setDateError("La date de vérification doit être supérieure à la date de remise");
      } else {
        setDateError(null);
      }
    }
  };

  // Date (Android)
  const showDeliveryDatePicker = () => {
    DateTimePickerAndroid.open({
      value: deliveryDate,
      mode: "date",
      onChange: handleDeliveryDateChange,
      display: "default",
    });
  };

  const showVerificationDatePicker = () => {
    DateTimePickerAndroid.open({
      value: verificationDate,
      mode: "date",
      onChange: handleVerificationDateChange,
      display: "default",
      minimumDate: deliveryDate,
    });
  };

  const handleAssignEquipment = async (data: any) => {
    // Vérifier une dernière fois avant l'envoi
    if (verificationDate < deliveryDate) {
      setDateError("La date de vérification doit être supérieure à la date de remise");
      Toast.show({
        type: "error",
        text1: "Date de vérification invalide",
        text1Style: { color: "#1C3144" },
        text2: "La date de vérification doit être supérieure à la date de remise",
        text2Style: { color: "#1C3144" },
      });
      return;
    }

    try {
      const { error } = await supabase
        .from("equipments")
        .update({
          user_id: data.user_id,
          delivery_date: deliveryDate.toISOString(),
          verification_date: verificationDate.toISOString(),
        })
        .eq("uuid", params.uuid);

      if (error) throw error;

      Toast.show({
        type: "success",
        text1: "Équipement assigné avec succès",
        text1Style: { color: "#1C3144" },
      });

      // Ajouter un délai avant la redirection
      setTimeout(() => {
        router.back();
      }, 1000);

    } catch (error) {
      console.error("Erreur lors de l'assignation :", error);
      Toast.show({
        type: "error",
        text1: "Erreur lors de l'assignation",
        text1Style: { color: "#1C3144" },
      });
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.form}>
        {/* Sélection de l'utilisateur */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Utilisateur</Text>
          <Controller
            control={control}
            name="user_id"
            rules={{ required: "L'utilisateur est requis" }}
            render={({ field: { onChange, value } }) => (
              <Dropdown
                style={dropdownStyles.dropdown}
                placeholderStyle={dropdownStyles.placeholderStyle}
                selectedTextStyle={dropdownStyles.selectedTextStyle}
                data={companyUsers}
                placeholder="Sélectionner un utilisateur"
                value={value}
                onChange={(item) => onChange(item.value)}
                labelField="label"
                valueField="value"
              />
            )}
          />
          {errors.user_id && (
            <Text style={styles.errorText}>{errors.user_id.message as string}</Text>
          )}
        </View>

        {/* Date de remise */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Date de remise</Text>
          {Platform.OS === "android" ? (
            <TouchableOpacity
              onPress={showDeliveryDatePicker}
              style={styles.dateButton}
            >
              <Text style={styles.dateText}>
                {deliveryDate.toLocaleDateString()}
              </Text>
            </TouchableOpacity>
          ) : Platform.OS === "ios" ? (
            <View style={styles.dateTimePickerContainer}>
              <DateTimePicker
                value={deliveryDate}
                mode="date"
                display="default"
                onChange={handleDeliveryDateChange}
                style={styles.datePicker}
              />
            </View>
          ) : (
            <View style={styles.dateTimePickerContainer}>
              <WebDateTimePicker
                value={deliveryDate}
                onChange={handleDeliveryDateChange}
                style={styles.datePicker}
              />
            </View>
          )}
        </View>

        {/* Date de vérification */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Date de vérification</Text>
          {Platform.OS === "android" ? (
            <TouchableOpacity
              onPress={showVerificationDatePicker}
              style={styles.dateButton}
            >
              <Text style={styles.dateText}>
                {verificationDate.toLocaleDateString()}
              </Text>
            </TouchableOpacity>
          ) : Platform.OS === "ios" ? (
            <View style={styles.dateTimePickerContainer}>
              <DateTimePicker
                value={verificationDate}
                mode="date"
                display="default"
                onChange={handleVerificationDateChange}
                minimumDate={deliveryDate}
                style={styles.datePicker}
              />
            </View>
          ) : (
            <View style={styles.dateTimePickerContainer}>
              <WebDateTimePicker
                value={verificationDate}
                onChange={handleVerificationDateChange}
                style={styles.datePicker}
              />
            </View>
          )}
          {dateError && <Text style={styles.errorText}>{dateError}</Text>}
        </View>

        {/* Bouton de soumission */}
        <ContainedButton
          label="Assigner"
          backgroundColor="#F99527"
          onPress={handleSubmit(handleAssignEquipment)}
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
      width: '100%',
    } : {}),
  },
  form: {
    padding: 20,
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
      width: '100%',
    } : {}),
  },
  inputContainer: {
    marginBottom: 20,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: "#1C3144",
  },
  dateButton: {
    width: "100%",
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 8,
    padding: 12,
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  dateText: {
    fontSize: 16,
    color: "#1C3144",
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginTop: 4,
  },
  dateTimePickerContainer: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    textAlign: "left",
    marginVertical: 20,
  },
  datePicker: {
    width: "100%",
    marginTop: 10,
    marginBottom: 10,
  },
});

const dropdownStyles = StyleSheet.create({
  dropdown: {
    width: "100%",
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 8,
    padding: 12,
    backgroundColor: "white",
    marginTop: 8,
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  placeholderStyle: {
    fontSize: 16,
    color: "#666",
  },
  selectedTextStyle: {
    fontSize: 16,
    color: "#1C3144",
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 16,
  },
});
