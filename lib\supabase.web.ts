import { createClient } from "@supabase/supabase-js";

// Vérifie si on est dans un environnement navigateur
const isBrowser = typeof window !== 'undefined';

// Définition d'un adaptateur de stockage
const localStorageAdapter = {
  getItem: async (key: string) =>
    isBrowser ? localStorage.getItem(key) : null,
  setItem: async (key: string, value: string) =>
    isBrowser ? localStorage.setItem(key, value) : undefined,
  removeItem: async (key: string) =>
    isBrowser ? localStorage.removeItem(key) : undefined,
};

const supabaseUrl = "https://kclvkwjjrbvjgbpehmpz.supabase.co";
const supabaseAnonKey ="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtjbHZrd2pqcmJ2amdicGVobXB6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzUyMjYxNDgsImV4cCI6MjA1MDgwMjE0OH0.X3xQmFl_D4D3hG9y5lWK4mBzuZ5dH7AbNBDoSX6_QQg";

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    storage: isBrowser ? window.localStorage : undefined,
  },
});



