/*
app/event/detailsEvent.tsx

Page de détail d'un événement/modifier un événement (en ajoutant des actions).

Informations pertinentes :

- On récupère les actions associées à cet événement depuis la table `event_actions`.
- Les actions sont regroupées en deux catégories :
  -> Actions immédiates (`type === immediate`) : actions à mettre en place rapidement.
  2. **Actions correctives** (`type === corrective`) : actions pour traiter les causes du problème.
- L'utilisateur connecté ne voit que les actions qu'il a créées (`uid_user`).

----- Affichage des actions -----

Le formulaire de création d'une action pour un événement permet d'ajouter un type (colonne `type`).
En fonction du type nous aurons les conditions suivantes :

- Actions immédiates et/ou de confinement :
   -> `type` : `immediate`
- Actions traitant les causes du problèmes :
   -> `type` : `corrective`
- Nous avons une liste qui affiche les action immédiate et une liste qui affiche les actions correctif
*/

import ContainedButton from "@/components/ui/buttons/ContainedButton";
import { useRouter, useLocalSearchParams, useFocusEffect } from "expo-router";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  FlatList,
  TouchableOpacity,
  Platform,
  Alert,
} from "react-native";
import { useCallback, useContext, useState } from "react";
import { AppContext } from "@/state/AppContext";
import PaperInfo from "@/components/common/paperInfo";
import moment from "moment";
import { supabase } from "@/lib/supabase";
import Header from "@/components/common/Header";
import Paper from "@/components/common/paper";
import TypeActionEvent from "@/types/typeActionEvent";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";

const typesEvent = [
  { value: "supplier", label: "Non conformité - Fournisseur" },
  { value: "audit_report", label: "Constat d'audit (code CO)", ActionImmediate: "OUI" },
  { value: "non_compliant", label: "Audit - Non-conformité (code NC)", ActionImmediate: "OUI" },
  { value: "sensitive_point", label: "Audit - Point sensible (code PS)", ActionImmediate: "OUI" },
  { value: "dangerous_situation", label: "Situation dangereuse (code SD)", ActionImmediate: "OUI" },
  { value: "work_accident", label: "Accident du travail (code AT)", ActionImmediate: "OUI" },
  { value: "near_miss", label: "Presqu'accident (code PAT)", ActionImmediate: "OUI" },
  { value: "product_defect", label: "Malfaçon produit ou service (code NQ)", ActionImmediate: "OUI" },
  { value: "environmental_danger", label: "Danger environnemental (code DE)", ActionImmediate: "OUI" },
  { value: "environmental_accident", label: "Accident environnemental (code AE)", ActionImmediate: "OUI" },
  { value: "customer_complaint", label: "Réclamation client (code REC)", ActionImmediate: "OUI" },
  { value: "risk", label: "Risque (code RI)", ActionImmediate: "NON" },
  { value: "opportunity", label: "Opportunité (code OPP)", ActionImmediate: "NON" },
  { value: "objective", label: "Objectif (code OBJ)", ActionImmediate: "NON" },
  { value: "interested_party", label: "Partie intéressée pertinente (code PIP)", ActionImmediate: "NON" },
  { value: "audit_report", label: "Constat d'audit (code CO)", ActionImmediate: "OUI" },
  { label: "Matériel - Défaut (code MAT)", value: "material_defect" },
];

// Fonction pour récupérer les détails d'une action
const fetchActionDetails = async (actionId: string) => {
  try {
    console.log("🔍 Tentative de récupération de l'action avec l'ID:", actionId);
    
    // Récupérer l'action avec toutes ses relations
    const { data: actionData, error: actionError } = await supabase
      .from("actions")
      .select(`
        *,
        action_administrators (
          user_id,
          users (
            first_name,
            last_name,
            uid
          )
        ),
        action_processes (
          process_id,
          process (
            process_name
          )
        ),
        action_events (
          event_id,
          events (
            wording
          )
        )
      `)
      .eq("id", actionId)
      .single();

    if (actionError) {
      console.error("❌ Erreur lors de la récupération de l'action :", actionError.message);
      console.log("🔍 Détails de l'erreur:", actionError);
      return null;
    }

    // Formater les données pour correspondre à la structure attendue
    const formattedData = {
      ...actionData,
      administrator: actionData.action_administrators?.[0]?.users || null,
      process: actionData.action_processes?.[0]?.process || null,
      event: actionData.action_events?.[0]?.events || null
    };

    console.log("✅ Données de l'action récupérées:", formattedData);
    return formattedData;
  } catch (error) {
    console.error("❌ Erreur inattendue lors de la récupération de l'action :", error);
    return null;
  }
};

export default function detailsEvent() {
  const { user } = useContext(AppContext);
  const [eventActions, setEventActions] = useState<{
    immediateActions: TypeActionEvent[];
    correctiveActions: TypeActionEvent[];
  }>({
    immediateActions: [],
    correctiveActions: [],
  });
  const [eventType, setEventType] = useState<string>("");
  const router = useRouter();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const { date, wording, idEvent, causes, title, typeEvent, description } =
    useLocalSearchParams();

  // Récupérer les informations de l'événement
  const fetchData = async () => {
    try {
      // Récupérer le type d'événement si non présent dans les paramètres
      if (!typeEvent && idEvent) {
        const { data: eventData, error: eventError } = await supabase
          .from("events")
          .select("type")
          .eq("id", idEvent)
          .single();

        if (eventError) {
          console.error(
            "Erreur lors de la récupération du type d'événement :",
            eventError.message
          );
        } else if (eventData) {
          setEventType(eventData.type);
        }
      }

      // Récupérer les actions associées à l'événement depuis `event_actions`
      const { data: actionsFromEventActions, error: eventActionsError } =
        await supabase
          .from("event_actions")
          .select("*, process(*), users:users!event_actions_uid_user_fkey(*)")
          .eq("event_id", idEvent);

      if (eventActionsError) {
        console.error(
          "Erreur lors de la récupération des actions (event_actions) :",
          eventActionsError.message
        );
      }

      // Filtrer les actions
      if (actionsFromEventActions) {
        // Séparer les actions en deux groupes
        const immediateActions = actionsFromEventActions.filter(
          (action: TypeActionEvent) => action.type === "immediate"
        );
        const correctiveActions = actionsFromEventActions.filter(
          (action: TypeActionEvent) => action.type === "corrective"
        );

        // Mettre à jour l'état avec les actions récupérées
        setEventActions({ immediateActions, correctiveActions });
      }
    } catch (err) {
      console.error(
        "Erreur inattendue lors de la récupération des actions :",
        err
      );
    }
  };

  // Exécuter fetchData à chaque focus de l'écran
  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [])
  );

  // Fonction pour formater une date en format `JJ/MM/AAAA`
  function formatDate(dateString: string) {
    const date = new Date(dateString);
    return date.toLocaleDateString("fr-FR");
  }

  // Fonction pour supprimer un événement
  const handleDeleteEvent = async () => {
    const confirmDelete =
      Platform.OS === "web"
        ? window.confirm("Êtes-vous sûr de vouloir supprimer cet événement et ses actions associées ?")
        : await new Promise((resolve) =>
            Alert.alert(
              "Confirmation",
              "Êtes-vous sûr de vouloir supprimer cet événement et ses actions associées ?",
              [
                { text: "Annuler", style: "cancel", onPress: () => resolve(false) },
                { text: "Oui, supprimer", onPress: () => resolve(true) },
              ]
            )
          );

    if (!confirmDelete) return;

    try {
      // 1. Supprimer toutes les actions associées
      const { error: actionsError } = await supabase
        .from("event_actions")
        .delete()
        .eq("event_id", idEvent);

      if (actionsError) throw actionsError;

      console.log(`✅ Toutes les actions liées à l'événement ${idEvent} ont été supprimées.`);

      // 2. Supprimer l'événement lui-même
      const { error: eventError } = await supabase
        .from("events")
        .delete()
        .eq("id", idEvent);

      if (eventError) throw eventError;

      alert("L'événement et ses actions associées ont été supprimés avec succès.");
      router.back();
    } catch (error) {
      console.error("❌ Erreur lors de la suppression :", error);
      alert("Une erreur est survenue lors de la suppression.");
    }
  };

  return (
    <View style={styles.mainContainer}>
      <Header title="Détail de l'événement" />
  
      <View style={styles.container}>
        <View style={styles.content}>
          <View style={styles.registerInfo}>
            <View style={{ width: "80%" }}>
              <Paper
                data={[
                  {
                    label: typesEvent.find((event) => event.value === (typeEvent || eventType))?.label || "Type inconnu",
                    value: wording || title
                  },
                  {
                    label: "Description",
                    value: description || "Aucune description"
                  },
                  {
                    label: "Date",
                    value: formatDate(date as string)
                  }
                ]}
              />
            </View>
    
            <View style={styles.buttons}>
              <ContainedButton
                label="Créer une action"
                backgroundColor="#F99527"
                onPress={() =>
                  router.push({
                    pathname: "/event/createActionEvent",
                    params: {
                      idEvent: idEvent,
                      eventName: wording || title,
                      causes: causes,
                      date: date,
                      description: description,
                    },
                  })
                }
              />
            </View>
    
            {eventActions.immediateActions.length > 0 && (
              <View style={{ width: "80%", marginBottom: 10 }}>
                <Text style={styles.sectionTitle}>
                  Actions immédiates et/ou de confinement
                </Text>
    
                {eventActions.immediateActions.map((item) => (
                  <TouchableOpacity
                    key={item.id.toString()}
                    onPress={async () => {
                      console.log("🖱️ Clic sur l'action immédiate:", item);
                      const actionDetails = await fetchActionDetails(item.action_id.toString());
                      if (actionDetails) {
                        console.log("✅ Navigation vers les détails de l'action avec:", actionDetails);
                        navigation.navigate("screens/actions/ActionDetails", {
                          action: actionDetails
                        });
                      }
                    }}
                    style={styles.listItem}
                  >
                    <View style={styles.elementsTop}>
                      <Text style={styles.listName}>{item.wording_action}</Text>
                      <Text style={styles.date}>
                        {moment(item.date).format("DD/MM/YYYY")}
                      </Text>
                    </View>
    
                    <View style={styles.elementsBottom}>
                      <Text style={styles.texts}>
                        Responsable :{" "}
                        {item.users
                          ? `${item.users?.first_name} ${item.users?.last_name}`
                          : "Non attribué"}
                      </Text>
                      <Text style={styles.texts}>
                        Processus :{" "}
                        {item.process ? item.process.process_name : "Non défini"}
                      </Text>
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            )}
    
            {eventActions.correctiveActions.length > 0 && (
              <View style={{ width: "80%", marginTop: 10 }}>
                <Text style={styles.sectionTitle}>
                  Actions traitant les causes du problème
                </Text>
    
                {eventActions.correctiveActions.map((item) => (
                  <TouchableOpacity
                    key={item.id.toString()}
                    onPress={async () => {
                      console.log("🖱️ Clic sur l'action corrective:", item);
                      const actionDetails = await fetchActionDetails(item.action_id.toString());
                      if (actionDetails) {
                        console.log("✅ Navigation vers les détails de l'action avec:", actionDetails);
                        navigation.navigate("screens/actions/ActionDetails", {
                          action: actionDetails
                        });
                      }
                    }}
                    style={styles.listItem}
                  >
                    <View style={styles.elementsTop}>
                      <Text style={styles.listName}>{item.wording_action}</Text>
                      <Text style={styles.date}>
                        {moment(item.date).format("DD/MM/YYYY")}
                      </Text>
                    </View>
    
                    <View style={styles.elementsBottom}>
                      <Text style={styles.texts}>
                        Responsable :{" "}
                        {item.users
                          ? `${item.users?.first_name} ${item.users?.last_name}`
                          : "Non attribué"}
                      </Text>
                      <Text style={styles.texts}>
                        Processus :{" "}
                        {item.process ? item.process.process_name : "Non défini"}
                      </Text>
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            )}
            
            {user?.status === "Admin" && (
              <View style={styles.deleteButtonContainer}>
                <TouchableOpacity style={styles.deleteButton} onPress={handleDeleteEvent}>
                  <Text style={styles.deleteButtonText}>Supprimer cet événement</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      </View>
    </View>
  );  
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  content: {
    gap: 20,
    padding: 20,
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    width: "100%",
    gap: 20,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "80%",
  },
  sectionTitle: {
    fontWeight: "bold",
    fontSize: 16,
    marginBottom: 10,
  },
  listItem: {
    width: "100%",
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    display: "flex",
    justifyContent: "space-between",
    flexDirection: "column",
    gap: 10,
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  elementsTop: {
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-between",
  },
  elementsBottom: {
    display: "flex",
    flexDirection: "column",
  },
  listName: {
    fontSize: 15,
    color: "#262627",
    fontStyle: "normal",
    flexWrap: "wrap",
    fontWeight: "bold",
  },
  date: {
    fontSize: 14,
    color: "#525252",
    fontStyle: "normal",
    flexWrap: "wrap",
    fontWeight: "normal",
  },
  texts: {
    fontSize: 14,
    color: "#525252",
    fontStyle: "normal",
    flexWrap: "wrap",
    fontWeight: "normal",
  },
  deleteButtonContainer: {
    width: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 30,
  },
  deleteButton: {
    width: 200,
    backgroundColor: "#ff4f42",
    padding: 10,
    borderRadius: 8,
    alignItems: "center",
  },
  deleteButtonText: {
    color: "#FFFFFF",
    fontWeight: "normal",
  },
});
