/*
app/info/updateIssue.tsx

Mettre à jour l'enjeu de l'entreprise.

Informations pertinentes :

- Lo<PERSON> de la validation du formulaire on envoie les données dans la table `issue`.
- Une entreprise ne peut avoir qu'un enjeu. Si une entreprise a déjà un enjeu on ne peut pas en créer un nouveau (on peut modifier le enjeu existant).

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern` et `Extern` n'ont pas accès à cette page.
*/

import { useRouter } from "expo-router";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  Alert,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Image,
  ActivityIndicator,
  Animated,
} from "react-native";
import { useContext, useEffect, useState, useRef } from "react";
import { Controller, useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import TypeIssue from "@/types/typeIssue";
import openai from "@/lib/openai.native"; // Importer OpenAI
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import TypeContext from "@/types/typeContext";
import * as Progress from 'react-native-progress';

export default function UpdateIssue() {
  const { companySelected } = useContext(AppContext);
  const router = useRouter();
  const [issueData, setIssueData] = useState<TypeIssue | null>(null);
  const [contextData, setContextData] = useState<TypeContext | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const { user } = useContext(AppContext);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const {
    handleSubmit,
    formState: { errors },
    control,
    reset,
    setValue,
  } = useForm<TypeIssue>({
    defaultValues: {
      context: "",
    },
  });

  // Fonction pour l'animation de clignement
  const startBlinking = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 0.3,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  // Arrêter l'animation
  const stopBlinking = () => {
    fadeAnim.setValue(1);
  };

  useEffect(() => {
    if (isGenerating) {
      startBlinking();
    } else {
      stopBlinking();
    }
  }, [isGenerating]);

  useEffect(() => {
    const fetchIssue = async () => {
      if (!companySelected?.id) return;

      const { data, error } = await supabase
        .from("issues")
        .select("*")
        .eq("company_id", companySelected.id)
        .single();

      if (error) {
        console.error(
          "Erreur lors de la récupération de l'enjeu :",
          error.message
        );
      } else {
        setIssueData(data);
        reset(data);
      }
    };

    const fetchContext = async () => {
      if (!companySelected?.id) return;

      const { data, error } = await supabase
        .from("contexts")
        .select("*")
        .eq("company_id", companySelected.id)
        .single();

      if (error) {
        console.error(
          "Erreur lors de la récupération du contexte :",
          error.message
        );
      } else {
        setContextData(data);
      }
    };

    fetchContext();
    fetchIssue();
  }, [companySelected, reset]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const generatePESTEL = async () => {
    if (!contextData) {
      Alert.alert("Erreur", "Aucun contexte trouvé.");
      return;
    }
    setIsGenerating(true);
    setProgress(0);

    // Fonction pour progresser doucement vers une valeur cible
    const progressToTarget = async (target: number, duration: number) => {
      const startTime = Date.now();
      const startProgress = progress;
      const updateInterval = 50; // ms

      return new Promise<void>((resolve) => {
        const interval = setInterval(() => {
          const elapsed = Date.now() - startTime;
          if (elapsed >= duration) {
            setProgress(target);
            clearInterval(interval);
            resolve();
          } else {
            const newProgress = startProgress + (target - startProgress) * (elapsed / duration);
            setProgress(newProgress);
          }
        }, updateInterval);
      });
    };

    await progressToTarget(0.2, 1000); // Progresser vers 20% en 1 seconde
    
    const prompt = `
      Rôle : Agis en tant que consultant(e) en stratégie d'entreprise expérimenté(e).
Objectif : Analyser en profondeur le contexte fourni ci-dessous concernant une entreprise spécifique afin d'identifier, de définir et de hiérarchiser ses principaux enjeux actuels et futurs. Les enjeux doivent couvrir les défis majeurs, les opportunités critiques, les risques significatifs et les impératifs stratégiques.
Contexte Fourni par l'Utilisateur :
----------------------------------------------------------------------
      - Histoire de l'entreprise : ${contextData?.history}
      - Contexte de l'entreprise : ${contextData?.context}
      - Organisation interne : ${contextData?.organisation}
      - Site web : ${contextData?.website}
-----------------------------------------------------------------------
Ce contexte devrait idéalement inclure :
- Description de l'entreprise (secteur d'activité, taille, mission, vision, valeurs)
- Produits/Services principaux
- Marché(s) cible(s) et segments de clientèle
- Paysage concurrentiel (principaux concurrents, positionnement)
- Performance récente (financière, opérationnelle - points clés)
- Forces et faiblesses internes connues (ex: ressources, compétences, culture, technologie)
- Opportunités et menaces externes identifiées (ex: tendances marché, régulation, technologie, économie)
- Objectifs stratégiques ou problèmes spécifiques mentionnés par l'entreprise
- Attentes des parties prenantes clés (si connues) ]
## Instructions pour l'Analyse et la Réponse :
Analyse Holistique : Examine attentivement TOUTES les informations fournies dans le contexte. Ne te limite pas aux problèmes explicitement mentionnés. Considère les interactions entre les différents éléments (ex: comment une faiblesse interne peut exacerber une menace externe).
Identification des Enjeux : Distingue clairement les enjeux principaux. Un enjeu est un point critique (défi, opportunité, risque, nécessité) dont la gestion aura un impact significatif sur la performance, la pérennité ou l'atteinte des objectifs de l'entreprise.
Définition Claire : Pour chaque enjeu identifié :
Donne-lui un titre concis et explicite.
Décris précisément en quoi il consiste pour cette entreprise spécifique, en te basant directement sur les éléments du contexte fourni. Explique pourquoi c'est un enjeu majeur.
Catégorise l'enjeu (si pertinent, ex: Stratégique, Opérationnel, Financier, Marché, RH, Technologique, Réglementaire, RSE...).
Hiérarchisation : Présente les enjeux par ordre d'importance ou d'urgence perçue, en justifiant brièvement ce classement si possible (ou en les regroupant par catégorie). Commence par les plus critiques.
Synthèse : Conclus par une très brève synthèse soulignant 1 ou 2 dynamiques globales qui ressortent de l'analyse des enjeux (ex: une forte pression concurrentielle couplée à un retard technologique, une opportunité de marché majeure limitée par des capacités internes, etc.).
Clarté et Précision : Utilise un langage clair, professionnel et précis. Évite les généralités non fondées sur le contexte.
Format de Sortie Attendu :
Une liste structurée (numérotée ou à puces) des enjeux principaux, avec pour chacun son titre, sa description détaillée liée au contexte, et sa catégorie. La hiérarchisation doit être évidente dans l'ordre de présentation.
Note Importante : Si le contexte fourni semble manquer d'informations cruciales pour identifier certains enjeux potentiels, n'hésite pas à le signaler et à indiquer quel type d'information supplémentaire serait utile.
  `;

    await progressToTarget(0.4, 1000); // Progresser vers 40% en 1 seconde

    try {
      await progressToTarget(0.6, 1000); // Progresser vers 60% en 1 seconde
      const response = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [{ role: "user", content: prompt }],
        max_tokens: 3000,
        temperature: 1,
      });

      await progressToTarget(0.8, 1000); // Progresser vers 80% en 1 seconde
      if (response.choices.length > 0 && response.choices[0].message?.content) {
        const generatedText = response.choices[0].message.content.trim();
        setValue("context", generatedText);
        await progressToTarget(1, 1000); // Progresser vers 100% en 1 seconde
        // Attendre un court instant pour que l'utilisateur voie la progression complète
        await new Promise(resolve => setTimeout(resolve, 500));
      } else {
        Alert.alert("Erreur", "La génération des enjeux a échoué.");
      }
    } catch (error) {
      console.error("Erreur OpenAI :", error);
      Alert.alert("Erreur", "Problème lors de la génération des enjeux.");
    } finally {
      setIsGenerating(false);
      setProgress(0);
    }
  };

  // Valider le formulaire
  const handleUpdateIssue = async (data: TypeIssue) => {
    try {
      setIsSubmitting(true);

      if (!issueData) {
        Alert.alert("Erreur", "Aucun enjeu existant trouvé.");
        return;
      }

      const { error } = await supabase
        .from("issues")
        .update({
          context: data.context,
        })
        .eq("company_id", companySelected?.id);

      if (error) {
        Alert.alert(
          "Échec de la mise à jour",
          error.message || "Une erreur est survenue."
        );
        return;
      }

      Alert.alert("Succès", "L'enjeu a été mis à jour avec succès.", [
        {
          text: "OK",
          onPress: () => {
            router.back();
          },
        },
      ]);
    } catch (err) {
      Alert.alert("Erreur inattendue", "Veuillez réessayer plus tard.");
      console.error("Erreur inattendue :", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      keyboardVerticalOffset={Platform.select({ ios: 0, android: 100 })}
    >
      <View style={styles.container}>
        <ScrollView style={styles.form}>
          {/* Texte d'introduction */}
          <View style={styles.introduction}>
            <View style={styles.introduction}>
              <Text style={styles.introText}>
                Définissez vos enjeux suivant le PESTEL
              </Text>
              <View style={styles.IAButtonContainer}>
                <Text style={styles.IAButtonText}>
                  Laissez-vous assister par l'IA pour générer vos enjeux
                </Text>
                <TouchableOpacity
                  style={[styles.IAButton, isGenerating && styles.IAButtonDisabled]}
                  onPress={generatePESTEL}
                  disabled={isGenerating}
                >
                  {isGenerating ? (
                    <View style={styles.loadingContainer}>
                      <Progress.Circle
                        size={24}
                        progress={progress}
                        color="#f99527"
                        thickness={3}
                        showsText={false}
                      />
                      {progress < 1 && (
                        <Animated.Text style={[styles.loadingText, { opacity: fadeAnim }]}>
                          Génération en cours...
                        </Animated.Text>
                      )}
                    </View>
                  ) : (
                    <Image
                      style={styles.IAButtonImage}
                      source={require("@/assets/images/qse.png")}
                    />
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </View>

          {/* Champ Enjeu */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Enjeu(x)</Text>
            <Controller
              control={control}
              name="context"
              rules={{ required: "Les enjeux sont requis." }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  placeholderTextColor={"#6B7280"}
                  style={styles.inputXL}
                  multiline={true}
                  textAlignVertical="top"
                  placeholder="Enjeux actuels"
                  onChangeText={onChange}
                  value={value}
                />
              )}
            />
            {errors.context && (
              <Text style={styles.errorText}>{errors.context.message}</Text>
            )}
          </View>

          {/* Bouton de soumission */}
          <TouchableOpacity
            style={styles.confirmButton}
            onPress={() => {
              handleSubmit(handleUpdateIssue)();
            }}
          >
            <Text style={styles.confirmButtonText}>Enregistrer</Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    height: "100%",
    width: "100%",
    alignItems: "center",
    alignContent: "center",
    backgroundColor: "#FFFFFF",
  },
  headSection: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 30,
  },
  backButton: {
    width: 24,
    textAlign: "left",
  },
  titleWrapper: {
    display: "flex",
    flexDirection: "row",
    marginVertical: 15,
  },
  title: {
    color: "#262627",
    fontSize: 17,
    textAlign: "left",
  },
  form: {
    width: "100%",
    alignSelf: "center",
    paddingHorizontal: 30,
    marginTop: 20,
  },
  inputXL: {
    borderWidth: 2,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "100%", // S'adapte à la largeur du conteneur parent
    paddingHorizontal: 12,
    paddingVertical: 10, // Ajoute un espace en haut pour ne pas coller au bord
    height: 364, // Hauteur fixe
    color: "#000000",
    textAlignVertical: "top", // Aligne le texte en haut
  },
  input: {
    borderWidth: 2,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "auto",
    padding: 12,
    marginTop: 10,
    color: "#000000",
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginBottom: 5,
  },
  infoText: {
    fontSize: 14,
    marginVertical: 10,
    color: "#555",
  },
  inputContainer: {
    position: "relative",
    marginBottom: 20,
    width: "100%",
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
    textAlign: "left",
  },
  introduction: {
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-between",
    marginBottom: 20,
    width: "100%",
  },
  introText: {
    fontSize: 14,
    color: "#262627",
    marginBottom: 15,
  },
  IAButtonContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f8f9fa",
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#e9ecef",
  },
  IAButtonText: {
    flex: 1,
    fontSize: 14,
    color: "#262627",
    marginRight: 10,
  },
  IAButton: {
    padding: 8,
  },
  IAButtonImage: {
    height: 24,
    width: 24,
  },
  confirmButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    marginBottom: 100,
    backgroundColor: "#f99527",
    marginTop: 10,
  },
  confirmButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  imagePicker: {
    width: "100%",
    padding: 10,
    backgroundColor: "#FFFFFF",
    borderColor: "#CCC",
    borderRadius: 8,
    borderWidth: 2,
    alignItems: "center",
    marginBottom: 10,
    marginTop: 10,
  },
  imagePickerText: {
    color: "#1F2937",
    fontWeight: "bold",
  },
  imagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  imageContainer: {
    width: "100%",
    justifyContent: "center",
  },
  uploadPlaceholder: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  uploadIcon: {
    borderWidth: 1,
    borderColor: "#f99527",
    borderRadius: 7,
    padding: 5,
  },
  uploadText: {
    textAlign: "center",
    fontWeight: "normal",
  },
  fullImagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  dateTimePickerContainer: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    textAlign: "left",
    marginVertical: 20,
  },
  dateTimePickerLabel: {
    color: "#063E76",
    fontSize: 14,
    fontWeight: "bold",
    backgroundColor: "#FFF",
  },
  datePicker: {
    width: "100%",
    marginTop: 10,
    marginBottom: 10,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  loadingText: {
    fontSize: 12,
    color: '#f99527',
  },
  IAButtonDisabled: {
    opacity: 0.7,
  },
});
