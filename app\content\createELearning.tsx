/*
app/eLearning/createELearning.tsx

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern` et `Extern` n'ont pas accès à cette page.
*/

import React from 'react';
import ContainedButton from "@/components/ui/buttons/ContainedButton";
import OutlinedButton from "@/components/ui/buttons/OutlinedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useRouter } from "expo-router";
import {
  StyleSheet,
  Image,
  View,
  ScrollView,
  Alert,
  Linking,
  Dimensions,
  TouchableOpacity,
  Platform,
} from "react-native";

import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import CustomizedSelectMultiple from "@/components/ui/customizedSelect/customizedSelectMultiple";
import { useNavigation } from "expo-router";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { ParamListBase } from "@react-navigation/native";
import TypeELearning from "@/types/typeELearning";
import InputFileCustomized from "@/components/ui/inputs/InputFileCustomized";
import * as ImagePicker from "expo-image-picker";
import * as DocumentPicker from "expo-document-picker";
import uploadPhoto from "@/lib/uploadPictureStorage";
// import { WebView } from "react-native-webview";
import { useVideoPlayer, VideoView } from "expo-video";
import CustomizedSelect from "@/components/ui/customizedSelect/customizedSelect";
import { Text } from "react-native";
import WebView from "react-native-webview";
import { Spinner } from "@/components/ui/spinner";
import colors from "tailwindcss/colors";
import { Button, ButtonText } from "@/components/ui/button";
import Header from "@/components/common/Header";

const fileTypes = [
  { value: "video", label: "Vidéo" },
  { value: "pdf", label: "Pdf" },
];

const roleOptions = [
  { label: "Auditeur", value: "Auditeur" },
  { label: "Fournisseur", value: "Fournisseur" },
  { label: "Client", value: "Client" },
  { label: "Collaborateur", value: "Collaborateur" },
  { label: "Responsable QSE", value: "Responsable QSE" },
  { label: "Pilote de processus", value: "Pilote de Processus" },
  { label: "Responsable QSE Adjoint", value: "Responsable QSE Adjoint" },
  { label: "Responsable Achat", value: "Responsable achat" },
  { label: "Responsable Maintenance", value: "Responsable maintenance" },
];

export default function CreateELearning() {
  const { user, companySelected } = useContext(AppContext);

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors, touchedFields },
    control,
    reset,
    watch,
  } = useForm<TypeELearning>({
    defaultValues: {
      created_at: "",
      uid_user: user?.uid,
      title: "",
      description: "",
      file_type: "",
      url_mignature: "",
      url_video: "",
      url_media: "",
      company_id: 0,
      type_profil: "",
    },
  });
  const [loading, setLoading] = useState(false);
  const [loadingSearchPdf, setLoadingLoadingSearchPdf] = useState(false);

  const [imageUri, setImageUri] = useState("");
  const [videoUri, setVideoUri] = useState("");
  const [pdfUri, setPdfUri] = useState("");
  const [pdfUrl, setPdfUrl] = useState("");
  const { width } = Dimensions.get("window");

  const [currentFile_type, setCurrentFile_type] = useState(
    watch("file_type") || ""
  );
  const [selectedValues, setSelectedValues] = useState<string[]>([]);

  const toggleSelection = (value: string) => {
    setSelectedValues((prevSelected) =>
      prevSelected.includes(value)
        ? prevSelected.filter((item) => item !== value)
        : [...prevSelected, value]
    );
  };

  const [users, setUsers] = useState([]);

  const router = useRouter();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  const pickImage = async () => {
    const permissionResult =
      await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (!permissionResult.granted) {
      Toast.show({
        type: "error",
        text1: "Permissions refusées",
        text1Style: { color: "#1C3144" },
        text2: "Vous devez autoriser l'accès à vos photos.",
      });
      return;
    }

    const pickerResult = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ["images"],
      allowsEditing: true,
      // aspect: [500, 500],
      quality: 1,
    });

    if (!pickerResult.canceled && pickerResult.assets.length > 0) {
      const selectedImageUri = pickerResult.assets[0].uri;
      setImageUri(selectedImageUri); // ✅ Mise à jour du state (même si asynchrone)

      // 📌 Déclencher immédiatement la mise à jour du profil avec l'image sélectionnée
    }
  };

  const pickVideo = async () => {
    const permissionResult =
      await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (!permissionResult.granted) {
      Toast.show({
        type: "error",
        text1: "Permissions refusées",
        text1Style: { color: "#1C3144" },
        text2: "Vous devez autoriser l'accès à vos photos.",
      });
      return;
    }

    const pickerResult = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ["videos"],
      allowsEditing: true,
      // aspect: [500, 500],
      quality: 1,
    });

    if (!pickerResult.canceled && pickerResult.assets.length > 0) {
      const selectedImageUri = pickerResult.assets[0].uri;
      setVideoUri(selectedImageUri); // ✅ Mise à jour du state (même si asynchrone)

      // 📌 Déclencher immédiatement la mise à jour du profil avec l'image sélectionnée
    }
  };

  const testPDFAndThumbnail = async (pdfUrl: string, thumbnailUrl: string) => {
    console.log('=== TEST PDF ET MINIATURE ===');
    console.log('1. Vérification du PDF:');
    console.log('URL du PDF:', pdfUrl);
    
    try {
      const pdfResponse = await fetch(pdfUrl);
      if (!pdfResponse.ok) {
        throw new Error(`PDF non accessible: ${pdfResponse.status}`);
      }
      console.log('✅ PDF accessible et valide');
    } catch (error) {
      console.error('❌ Erreur avec le PDF:', error);
    }

    console.log('\n2. Vérification de la miniature:');
    console.log('URL de la miniature:', thumbnailUrl);
    
    try {
      const thumbnailResponse = await fetch(thumbnailUrl);
      if (!thumbnailResponse.ok) {
        throw new Error(`Miniature non accessible: ${thumbnailResponse.status}`);
      }
      console.log('✅ Miniature accessible et valide');
    } catch (error) {
      console.error('❌ Erreur avec la miniature:', error);
    }
    console.log('=== FIN DU TEST ===\n');
  };

  const pickPDF = async () => {
    const result = await DocumentPicker.getDocumentAsync({
      type: "application/pdf",
    });

    if (result.canceled) return;
    setLoadingLoadingSearchPdf(true);
    
    try {
      console.log('\n=== DÉBUT DU PROCESSUS PDF ===');
      // Upload du PDF
      const response = await uploadPhoto(
        result?.assets[0].uri,
        user?.uid as string,
        "pdf",
        "cartos",
        "application/pdf"
      );
      
      if (response?.url) {
        setPdfUri(result?.assets[0].uri);
        setPdfUrl(response.url);
        console.log('1. PDF uploadé avec succès:', response.url);
        
        // Extraire le chemin du fichier de l'URL
        const filePath = response.url.split('/public/')[1];
        console.log('2. Chemin du fichier extrait:', filePath);
        
        // Vérifier si le fichier existe avant de créer l'URL signée
        const { data: fileExists, error: fileCheckError } = await supabase
          .storage
          .from('pdf')
          .list('cartos');

        if (fileCheckError) {
          console.error('❌ Erreur lors de la vérification du fichier:', fileCheckError);
          return;
        }

        const fileName = filePath.split('/').pop();
        const fileFound = fileExists?.some((file: { name: string }) => file.name === fileName);

        if (!fileFound) {
          console.error('❌ Fichier non trouvé dans le bucket');
          return;
        }

        console.log('3. Fichier vérifié dans le bucket');
        
        // Utiliser le service de conversion de PDF en image
        console.log('Préparation de la conversion');
        let imageData;
        
        try {
          // Extraire le chemin du fichier
          const filePath = `cartos/${fileName}`;
          console.log('4. Chemin du fichier:', filePath);
          
          // Préparer la requête avec le chemin du fichier
          const requestBody = {
            filePath: filePath,
            bucket: 'pdf',
            options: {
              format: 'jpeg',
              quality: 0.8,
              page: 1,
              width: 800,
              height: 600
            }
          };
          
          console.log('Corps de la requête:', JSON.stringify(requestBody, null, 2));
          
          const { data: initialImageData, error: imageError } = await supabase.functions.invoke('pdf-to-image', {
            body: requestBody,
            headers: {
              'Content-Type': 'application/json'
            }
          });

          if (imageError) {
            console.error('❌ Erreur lors de la conversion en image:', imageError);
            console.error('Détails de l\'erreur:', {
              message: imageError.message,
              name: imageError.name,
              status: imageError.status,
              stack: imageError.stack
            });
            
            // Essayer avec l'URL publique comme fallback
            console.log('Tentative avec l\'URL publique...');
            
            const fallbackRequestBody = {
              pdfUrl: response.url,
              options: {
                format: 'jpeg',
                quality: 0.8,
                page: 1,
                width: 800,
                height: 600
              }
            };
            
            console.log('Corps de la requête de secours:', JSON.stringify(fallbackRequestBody, null, 2));
            
            const { data: fallbackImageData, error: fallbackError } = await supabase.functions.invoke('pdf-to-image', {
              body: fallbackRequestBody,
              headers: {
                'Content-Type': 'application/json'
              }
            });

            if (fallbackError) {
              console.error('❌ Échec également avec l\'URL publique:', fallbackError);
              Toast.show({
                type: "error",
                text1: "Erreur lors de la génération de la miniature",
                text2: "Veuillez réessayer ou contacter le support",
                text1Style: { color: "#1C3144" },
                text2Style: { color: "#1C3144" },
              });
              return;
            }

            // Utiliser les données de l'URL publique si elle fonctionne
            imageData = fallbackImageData;
          } else {
            imageData = initialImageData;
          }

          console.log('5. Données de l\'image reçues:', imageData);

          if (!imageData || !imageData.imageUrl) {
            console.error('❌ Pas d\'URL d\'image retournée par la fonction');
            return;
          }

          // Upload de l'image de couverture
          const responseImg = await uploadPhoto(
            imageData.imageUrl,
            user?.uid as string,
            "images",
            "mignatures",
            "image/jpeg"
          );

          if (responseImg?.url) {
            setImageUri(responseImg.url);
            console.log('6. Miniature générée et enregistrée:', responseImg.url);
            
            // Test des URLs avant l'envoi
            await testPDFAndThumbnail(response.url, responseImg.url);
          } else {
            console.error('❌ Erreur lors de l\'upload de la miniature');
            Toast.show({
              type: "error",
              text1: "Erreur lors de la génération de la miniature",
              text1Style: { color: "#1C3144" },
            });
          }
        } catch (error) {
          console.error('❌ Erreur lors de la conversion:', error);
          Toast.show({
            type: "error",
            text1: "Erreur lors de la conversion du PDF en image",
            text1Style: { color: "#1C3144" },
          });
        }
      }
    } catch (error) {
      console.error('❌ Erreur lors de l\'upload:', error);
      Toast.show({
        type: "error",
        text1: "Erreur lors de l'upload du PDF",
        text1Style: { color: "#1C3144" },
      });
    } finally {
      setLoadingLoadingSearchPdf(false);
    }
  };

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const inputs = [
    {
      label: "Titre",
      name: "title",
      placeholder: "Ecrire le titre du contenu",
      type: "text",
    },
    {
      label: "Description",
      name: "description",
      placeholder: "Description",
      type: "text",
      multiline: true,
    },
    {
      label: "Type de fichier",
      name: "file_type",
      placeholder: "Type de fichier",
      type: "Select",
      data: fileTypes,
    },
    {
      label: "URL Youtube",
      name: "url_video",
      placeholder: "https://youtube.com/...",
      type: "text",
    },
    {
      label: "Type d'utilisateur",
      name: "type_profil",
      placeholder: "Type d'utilisateur",
      type: "Select",
      data: roleOptions,
      multiple: true,
    },
  ];

  const createELearning = async (data: TypeELearning) => {
    setLoading(true);
    console.log('\n=== CRÉATION ELEARNING ===');
    console.log('Données à envoyer:', {
      ...data,
      url_mignature: imageUri,
      url_media: pdfUrl
    });

    const created_at = new Date();
    const selectedObject = Object.fromEntries(
      selectedValues.map((value) => [value, true])
    );

    try {
      // Si c'est une vidéo et qu'une URL Youtube est fournie, on ne fait pas d'upload
      if (data.file_type === "video" && data.url_video && data.url_video.trim() !== "") {
        const { error } = await supabase.from("elearnings").insert({
          created_at: created_at,
          uid_user: user?.uid,
          title: data.title,
          description: data.description,
          file_type: data.file_type,
          url_video: data.url_video,
          url_media: data.url_video,
          company_id: companySelected?.id,
          type_profil: selectedValues.length > 0 ? selectedObject : null,
          isShown: false,
          isClick: false,
        });

        if (error) {
          console.log("error__", error);
          Toast.show({
            type: "error",
            text1: "Erreur lors d'enregistrement de la eLearning",
            text1Style: { color: "#1C3144" },
          });
          setLoading(false);
          return;
        }

        Toast.show({
          type: "success",
          text1: "La ELearning était bien crée",
          text1Style: { color: "#1C3144" },
        });

        router.navigate("/content/elearning");
        reset();
        setLoading(false);
        return;
      }

      // Si c'est un PDF, on utilise les URLs déjà générées
      if (data.file_type === "pdf") {
        if (!pdfUrl) {
          console.error('❌ URL du PDF manquante');
          Toast.show({
            type: "error",
            text1: "Veuillez sélectionner un PDF",
            text1Style: { color: "#1C3144" },
          });
          setLoading(false);
          return;
        }

        if (!imageUri) {
          console.error('❌ URL de la miniature manquante');
          Toast.show({
            type: "error",
            text1: "La miniature n'a pas été générée",
            text1Style: { color: "#1C3144" },
          });
          setLoading(false);
          return;
        }

        const { error } = await supabase.from("elearnings").insert({
          created_at: created_at,
          uid_user: user?.uid,
          title: data.title,
          description: data.description,
          file_type: data.file_type,
          url_mignature: imageUri,
          url_media: pdfUrl,
          company_id: companySelected?.id,
          type_profil: selectedValues.length > 0 ? selectedObject : null,
          isShown: false,
          isClick: false,
        });

        if (error) {
          console.error('❌ Erreur lors de l\'insertion:', error);
          Toast.show({
            type: "error",
            text1: "Erreur lors d'enregistrement de la eLearning",
            text1Style: { color: "#1C3144" },
          });
          setLoading(false);
          return;
        }

        console.log('✅ ELearning créée avec succès');
        Toast.show({
          type: "success",
          text1: "La ELearning était bien crée",
          text1Style: { color: "#1C3144" },
        });

        router.navigate("/content/elearning");
        reset();
        setLoading(false);
        setPdfUri("");
        setPdfUrl("");
        setImageUri("");
        return;
      }

      // Si c'est une vidéo locale
      if (data.file_type === "video") {
        if (!videoUri || !imageUri) {
          Toast.show({
            type: "error",
            text1: "Veuillez sélectionner une vidéo et une miniature",
            text1Style: { color: "#1C3144" },
          });
          setLoading(false);
          return;
        }

        const responseVideo = await uploadPhoto(
          videoUri,
          user?.uid as string,
          "videos",
          "cartos",
          "video/mp4"
        );

        const responseImg = await uploadPhoto(
          imageUri,
          user?.uid as string,
          "images",
          "mignatures",
          "image/jpeg"
        );

        const { error } = await supabase.from("elearnings").insert({
          created_at: created_at,
          uid_user: user?.uid,
          title: data.title,
          description: data.description,
          file_type: data.file_type,
          url_mignature: responseImg?.url,
          url_media: responseVideo?.url,
          company_id: companySelected?.id,
          type_profil: selectedValues.length > 0 ? selectedObject : null,
          isShown: false,
          isClick: false,
        });

        if (error) {
          console.log("error__", error);
          Toast.show({
            type: "error",
            text1: "Erreur lors d'enregistrement de la eLearning",
            text1Style: { color: "#1C3144" },
          });
          setLoading(false);
          return;
        }

        Toast.show({
          type: "success",
          text1: "La ELearning était bien crée",
          text1Style: { color: "#1C3144" },
        });

        router.navigate("/content/elearning");
        reset();
        setLoading(false);
        setVideoUri("");
        setImageUri("");
      }
    } catch (err) {
      console.error('❌ Erreur générale:', err);
      setLoading(false);
      Toast.show({
        type: "error",
        text1: "Une erreur est survenue",
        text1Style: { color: "#1C3144" },
      });
    }
  };

  const player = useVideoPlayer(videoUri, (player) => {
    player.loop = false;
    player.play();
  });

  useEffect(() => {
    if (watch("file_type") !== currentFile_type) {
      reset({
        title: watch("title"),
        description: watch("description"),
        type_profil: watch("type_profil"),
        file_type: watch("file_type"),
      });
      setCurrentFile_type(watch("file_type"));
      setPdfUri("");
      setVideoUri("");
      setImageUri("");
    }
  }, [watch("file_type")]);

  console.log("pdfUrl", pdfUrl);
  console.log("videoUri", videoUri);

  return (
    <View style={{ flex: 1 }}>
      <Header
        title="Créer un E-Learning"
        onPressFlesh={() => router.back()}
      />
      <ScrollView>
        <View style={styles.containerContent}>
          <View style={styles.registerInfo}>
            <View style={styles.inputs}>
              {inputs.map((input, index) => {
                if (input?.type === "Select") {
                  return (
                    <View key={index}>
                      {input.multiple === true ? (
                        <View>
                          <Text style={styles.label}>Type d'utilisateur</Text>
                          {selectedValues.length === 0 && (
                            <Text style={[styles.label, { color: colors.red[500] }]}>
                              * Vous devez sélectionner au moins un type d'utilisateur
                            </Text>
                          )}
                          <View
                            style={{
                              display: "flex",
                              flexDirection: "row",
                              flexWrap: "wrap",
                              gap: 10,
                            }}
                          >
                            {roleOptions.map((role) => (
                              <TouchableOpacity
                                key={role.value}
                                style={[
                                  styles.roleButton,
                                  selectedValues.includes(role.value) &&
                                    styles.roleButtonSelected,
                                ]}
                                onPress={() => toggleSelection(role.value)}
                              >
                                <Text
                                  style={
                                    selectedValues.includes(role.value)
                                      ? styles.roleTextSelected
                                      : styles.roleText
                                  }
                                >
                                  {role.label}
                                </Text>
                              </TouchableOpacity>
                            ))}
                          </View>
                        </View>
                      ) : (
                        <CustomizedSelect
                          name={input.name}
                          label={input.label}
                          register={register}
                          control={control}
                          errors={errors}
                          setError={setError}
                          data={input.data as any}
                        />
                      )}
                    </View>
                  );
                } else {
                  return (
                    <View key={index}>
                      {!(
                        input.name === "url_video" &&
                        (watch("file_type") !== "video" || videoUri)
                      ) && (
                        <InputCustomized
                          label={input.label}
                          placeholder={input.placeholder}
                          register={register}
                          name={input.name}
                          control={control}
                          errors={errors}
                          setError={setError}
                          multiline={input.multiline}
                          isRequired={input.name === "url_video" && false}
                        />
                      )}
                    </View>
                  );
                }
              })}
            </View>
          </View>

          {watch("file_type") === "video" && watch("file_type") !== "" && (
            <>
              {!watch("url_video") && !videoUri && (
                <Text style={[styles.label, { color: colors.red[500] }]}>
                  * Vous devez soit renseigner une URL Youtube, soit télécharger une vidéo
                </Text>
              )}
              {!imageUri && videoUri && !watch("url_video") && (
                <Text style={[styles.label, { color: colors.red[500] }]}>
                  * Une miniature est requise pour la vidéo
                </Text>
              )}
              {!imageUri && !watch("url_video") && (
                <InputFileCustomized
                  label={"Miniature"}
                  placeholder={"Choisir la photo"}
                  onPress={pickImage}
                />
              )}
              {imageUri && !watch("url_video") ? (
                <View style={{ display: "flex" }}>
                  <Text style={styles.label}>Miniature</Text>
                  <TouchableOpacity onPress={pickImage}>
                    <Image
                      source={{ uri: imageUri }}
                      style={{
                        width: "100%",
                        height: 200,
                        marginTop: 10,
                        alignSelf: "center",
                      }}
                    />
                  </TouchableOpacity>
                </View>
              ) : null}
            </>
          )}

          {watch("file_type") !== "" && (
            <>
              {(videoUri || pdfUrl) && (
                <Text style={styles.label}>
                  {watch("file_type") === "video" ? "Vidéo" : "Fichier"}
                </Text>
              )}

              {!videoUri && !pdfUrl && !watch("url_video") && (
                <InputFileCustomized
                  label={watch("file_type") === "video" ? "Vidéo" : "Fichier"}
                  placeholder={
                    watch("file_type") === "video"
                      ? "Choisir la vidéo"
                      : "Choisir le fichier"
                  }
                  onPress={watch("file_type") === "video" ? pickVideo : pickPDF}
                />
              )}

              {watch("file_type") === "video" && videoUri && !watch("url_video") ? (
                <View>
                  <Button
                    size="xs"
                    variant="outline"
                    action="primary"
                    onPress={pickVideo}
                    style={{ width: 150, marginBottom: 10 }}
                  >
                    <ButtonText>Modifier la vidéo</ButtonText>
                  </Button>

                  <VideoView
                    style={{ width: "100%", height: 200 }}
                    player={player}
                  />
                </View>
              ) : null}

              {watch("file_type") === "pdf" && pdfUri && pdfUrl ? (
                <View style={{ width: width * 0.9, alignSelf: "center" }}>
                  <Button
                    size="xs"
                    variant="outline"
                    action="primary"
                    onPress={pickPDF}
                    style={{ width: 150, marginBottom: 10 }}
                  >
                    <ButtonText>Modifier le PDF</ButtonText>
                  </Button>
                  {Platform.OS === 'web' ? (
                    <iframe
                      src={pdfUrl}
                      style={{
                        width: '100%',
                        height: 480,
                        marginTop: 10,
                        border: 'none'
                      }}
                    />
                  ) : (
                    <WebView
                      source={{ uri: pdfUrl }}
                      style={{
                        flex: 1,
                        height: 480,
                        marginTop: 10,
                      }}
                      injectedJavaScript={`
                        const meta = document.createElement('meta'); 
                        meta.name = 'viewport'; 
                        meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
                        document.getElementsByTagName('head')[0].appendChild(meta);
                        document.documentElement.style.touchAction = 'none'; 
                        document.documentElement.style.msTouchAction = 'none';
                      `}
                    />
                  )}
                </View>
              ) : null}
              {loadingSearchPdf && (
                <View>
                  <Spinner size="small" color={colors.gray[500]} />
                </View>
              )}
            </>
          )}

          <View style={styles.buttons}>
            <ContainedButton
              label="Créer"
              backgroundColor="#F99527"
              onPress={handleSubmit(createELearning)}
              disabled={loading}
            />
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  containerContent: {
    flexDirection: "column",
    // alignItems: "center",
    alignContent: "center",
    // padding: 20,
    gap: 20,
    marginVertical: 20,
    width: "80%",
    alignSelf: "center",
    marginBottom: 200,
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    // backgroundColor: "yellow",
    width: "100%",
    gap: 20,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  buttonPassword: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  titleContainer: {
    flexDirection: "row",
    gap: 8,
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
  input: {
    backgroundColor: "white",
    borderColor: "none",
    height: 40,
    padding: 10,
    borderRadius: 4,
  },
  roleButton: {
    paddingHorizontal: 10,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "black",
  },
  roleButtonSelected: {
    backgroundColor: "#F99527",
    borderColor: "#F99527",
  },
  roleText: {
    color: "black",
  },
  roleTextSelected: {
    color: "white",
  },
  label: {
    fontSize: 14,
    color: "#262627",
    marginVertical: 1,
    fontWeight: "bold",
  },
});
