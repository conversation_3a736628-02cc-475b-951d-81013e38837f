/*
app/event/updateEvent.tsx

Page pour modifier un événement existant.

Informations pertinentes :

- Récupération des détails de l'événement sélectionné depuis la table `events`
- Préremplissage du formulaire avec les données existantes de l'événement

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern`, `Supplier` et `Extern` n'ont pas accès à cette page.
*/

import { useRouter } from "expo-router";
import {
  StyleSheet,
  Image,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Platform,
  TextInput,
  Alert,
  KeyboardAvoidingView,
  ActivityIndicator,
} from "react-native";
import DateTimePicker from "@react-native-community/datetimepicker";
import { DateTimePickerAndroid } from "@react-native-community/datetimepicker";
import * as ImagePicker from "expo-image-picker";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import TypeProcess from "@/types/typeProcess";
import { Dropdown } from "react-native-element-dropdown";
import { AntDesign, Octicons } from "@expo/vector-icons";
import typeUserByAdmin from "@/types/typeUserByAdmin";
import uploadMultipleImages from "@/lib/uploadMultipleImagesStorage";
import { useLocalSearchParams } from "expo-router";
import ContainedButton from "@/components/ui/buttons/ContainedButton";
// import openai from "@/lib/openai"; // OpenAI déjà configuré
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import Constants from "expo-constants";
import TypeContext from "@/types/typeContext";

// Types d'événement (dropdown)
const typesEvent = [
  // { label: "Constat d'audit (code CO)", value: "audit_report" },
  { label: "Non conformité - Fournisseur", value: "supplier" },
  { value: "audit_report", label: "Constat d'audit (code CO)", ActionImmediate: "OUI" },
  { label: "Matériel - Défaut (code MAT)", value: "material_defect" },

  { label: "Audit - Non-conformité (code NC)", value: "non_compliant" },
  { label: "Audit - Point sensible (code PS)", value: "sensitive_point" },
  { label: "Situation dangereuse (code SD)", value: "dangerous_situation" },
  { label: "Accident du travail (code AT)", value: "work_accident" },
  { label: "Presqu'accident (code PAT)", value: "near_miss" },
  { label: "Malfaçon produit ou service (code NQ)", value: "product_defect" },
  { label: "Danger environnemental (code DE)", value: "environmental_danger" },
  {
    label: "Accident environnemental (code AE)",
    value: "environmental_accident",
  },
  { label: "Réclamation client (code REC)", value: "customer_complaint" },
  { label: "Risque (code RI)", value: "risk" },
  { label: "Opportunité (code OPP)", value: "opportunity" },
  { label: "Objectif (code OBJ)", value: "objective" },
  {
    label: "Partie intéressée pertinente (code PIP)",
    value: "interested_party",
  },
];

export default function UpdateEvent() {
  const [loading, setLoading] = useState(false);
  const [imageUris, setImageUris] = useState<string[]>([]);
  const { user, companySelected } = useContext(AppContext);
  const [process, setProcess] = useState<TypeProcess[]>([]);
  const [usersAdmin, setUsersAdmin] = useState<typeUserByAdmin[]>([]);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [products, setProducts] = useState<{ label: string; value: number }[]>([]);
  const [contextData, setContextData] = useState<TypeContext | null>(null);
  const params = useLocalSearchParams();
  const [eventData, setEventData] = useState<any>(null);
  const router = useRouter();
  const [loadingIA, setLoadingIA] = useState(false); // Pour afficher un loader
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
    control,
    reset,
    setValue,
    watch
  } = useForm({
    defaultValues: {
      uid_admin: "",
      created_at: "",
      type: "",
      administrator: "",
      process: "",
      file: "",
      causes: "",
      wording: "",
      description: "",
      product_id: ""
    },
  });

  // Surveiller le type d'événement sélectionné
  const selectedType = watch("type");

  // Récupérer les produits de l'entreprise
  useEffect(() => {
    const fetchProducts = async () => {
      if (companySelected?.id) {
        const { data, error } = await supabase
          .from("products")
          .select("id, name")
          .eq("company_id", companySelected.id);

        if (!error && data) {
          const formattedProducts = data.map((product: { id: number; name: string }) => ({
            label: product.name,
            value: product.id
          }));
          setProducts(formattedProducts);
        }
      }
    };

    if (selectedType === "material_defect") {
      fetchProducts();
    }
  }, [selectedType, companySelected?.id]);

  // Récupérer les informations nécessaires
  useEffect(() => {
    const fetchData = async () => {
      try {
        const companyId = companySelected?.id;

        // Récupérer les processus de l'entreprise
        const { data: processData, error: processError } = await supabase
          .from("process")
          .select("id, process_name")
          .eq("company_id", companyId);

        if (processError) {
          console.error(
            "Erreur lors de la récupération des processus :",
            processError.message
          );
        } else {
          const formattedProcess: any = processData.map((process: any) => ({
            label: process.process_name,
            value: process.id,
          }));
          setProcess(formattedProcess);
        }

        // Récupérer les utilisateurs admin de l'entreprise
        const { data: usersData, error: usersError } = await supabase
          .from("users")
          .select()
          .or(`uid_admin.eq.${user?.uid}, uid.eq.${user?.uid}`);

        if (usersError) {
          console.error(
            "Erreur lors de la récupération des utilisateurs :",
            usersError.message
          );
          return;
        }

        // Formatage des utilisateurs admins
        const formattedUsers: any = usersData.map((item: any) => ({
          label: `${item.first_name} ${item.last_name}`,
          value: item.uid,
        }));

        // Récupérer les fournisseurs de l'entreprise
        const { data: suppliersData, error: suppliersError } = await supabase
          .from("company_users")
          .select(`
            user_id,
            users (
              uid,
              first_name,
              last_name,
              profil
            )
          `)
          .eq("company_id", companyId);

        if (suppliersError) {
          console.error(
            "Erreur lors de la récupération des fournisseurs :",
            suppliersError.message
          );
          return;
        }

        // Formatage des fournisseurs
        const formattedSuppliers: any = suppliersData
          .filter((item: any) => item.users?.profil === "Fournisseur")
          .map((item: any) => ({
            label: `${item.users.first_name} ${item.users.last_name} (Fournisseur)`,
            value: item.users.uid,
            type: "supplier"
          }));

        // Fusionner la liste des admins et celle des fournisseurs AVANT de mettre à jour `setUsersAdmin`
        setUsersAdmin([...formattedUsers, ...formattedSuppliers]);
      } catch (err) {
        console.error("Erreur inattendue lors de la récupération :", err);
      } finally {
        console.log("Fin du processus de récupération.");
        
      }
    };

    fetchData();

    /*
    // Vérifier si `params.file` est défini et sous forme de chaîne JSON
    if (params.file) {
      try {
        const parsedFiles =
          typeof params.file === "string"
            ? JSON.parse(params.file)
            : params.file;

        if (Array.isArray(parsedFiles)) {
          setImageUris(parsedFiles);
        } else {
          console.warn("Format de fichier invalide :", params.file);
        }
      } catch (error) {
        console.error("Erreur lors du parsing des fichiers :", error);
      }
    }
    */
  }, [user]);

  // Récupération complète des données de l'événement
  useEffect(() => {
    if (params.idEvent) {
      const fetchEventDetails = async () => {
        const { data, error } = await supabase
          .from("events")
          .select("*")
          .eq("id", params.idEvent)
          .single();

        if (error) {
          console.error(
            "Erreur lors de la récupération de l'événement :",
            error.message
          );
        } else {
          setEventData(data);
          reset({
            uid_admin: data.uid_admin || "",
            created_at: data.created_at || "",
            type: data.type || "",
            administrator: data.administrator || "",
            process: data.process || "",
            file: Array.isArray(data.file) ? data.file : [],
            causes: data.causes || "",
            wording: data.wording || "", 
            description: data.description || "",
            product_id: data.product_id || ""
          });
          if (data.date) setSelectedDate(new Date(data.date));
          if (data.file)
            setImageUris(Array.isArray(data.file) ? data.file : []);
        }
      };

      fetchEventDetails();
    }
  }, [params.idEvent, reset]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern", "Supplier"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  // Récupération du contexte de l'entreprise
  useEffect(() => {
    const fetchContext = async () => {
      if (!companySelected?.id) return;

      const { data, error } = await supabase
        .from("contexts")
        .select("*")
        .eq("company_id", companySelected.id)
        .single();

      if (error) {
        console.error(
          "Erreur lors de la récupération du contexte :",
          error.message
        );
      } else {
        setContextData(data);
      }
    };

    fetchContext();
  }, [companySelected]);

  // Date (iOS)
  const handleDateChange = (event: any, date: any) => {
    if (date) {
      setSelectedDate(date); // Mettre à jour avec la date sélectionnée
    }
  };

  // Date (Android)
  const showDatePicker = () => {
    DateTimePickerAndroid.open({
      value: selectedDate,
      mode: "date",
      onChange: handleDateChange,
      display: "default",
    });
  };

  // Choix de plusieurs images
  const handleSelectImages = async () => {
    const permissionResult =
      await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (permissionResult.granted === false) {
      Toast.show({
        type: "error",
        text1: "Permissions refusées",
        text1Style: { color: "#1C3144" },
        text2: "Vous devez autoriser l'accès à vos photos.",
        text2Style: { color: "#1C3144" },
      });
      return;
    }

    const pickerResult = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsMultipleSelection: true,
      quality: 1,
    });

    if (!pickerResult.canceled && pickerResult.assets) {
      const uris = pickerResult.assets.map((asset) => asset.uri);
      setImageUris(uris);
    }
  };

  // Validation du formulaire
  const handleSubmitUpdateEvent = async (data: any) => {
    try {
      let uploadedUrls: string[] = [];
  
      if (imageUris.length > 0) {
        for (const uri of imageUris) {
          const uploadResult = await uploadMultipleImages(uri, user?.uid || "");
          if (uploadResult?.url) uploadedUrls.push(uploadResult.url);
        }
      }
  
      const updatedEvent = {
        file: uploadedUrls.length > 0 ? uploadedUrls : eventData?.file,
        type: data?.type ?? eventData?.type,
        date: selectedDate ?? eventData?.date,
        administrator: data.administrator,
        process: data?.process ?? eventData?.process,
        causes: data?.causes ?? eventData?.causes,
        wording: data?.wording ?? eventData?.wording,
        description: data?.description ?? eventData?.description,
        product_id: data?.type === "material_defect" ? data?.product_id : null
      };
  
      // Mise à jour de l'événement
      const { error: eventError } = await supabase
        .from("events")
        .update(updatedEvent)
        .eq("id", params.idEvent);
  
      if (eventError) {
        console.error("Erreur lors de la mise à jour dans `events` :", eventError);
        if (Platform.OS === 'web') {
          alert("Échec de la mise à jour : " + eventError.message);
        } else {
          Alert.alert("Échec de la mise à jour", eventError.message || "Une erreur est survenue.");
        }
        return;
      }
  
      // Vérifier si l'administrateur est valide dans `administrators`
      const { data: adminCheck } = await supabase
        .from("administrators")
        .select("administrator_uuid")
        .eq("administrator_uuid", data.administrator)
        .single();
  
      if (!adminCheck) {
        console.warn("L'administrateur sélectionné n'existe pas dans `administrators`.");
        if (Platform.OS === 'web') {
          alert("Erreur : L'administrateur sélectionné n'existe pas.");
        } else {
          Alert.alert("Erreur", "L'administrateur sélectionné n'existe pas.");
        }
        return;
      }
  
      // Mise à jour de l'administrateur dans `event_administrators`
      await supabase
        .from("event_administrators")
        .delete()
        .eq("event_id", params.idEvent);
  
      const { error: adminError } = await supabase.from("event_administrators").insert({
        event_id: params.idEvent,
        administrator_uuid: data.administrator,
        administrator_type: data.administratorType || "user",
      });
  
      if (adminError) {
        console.error("Erreur lors de l'insertion dans `event_administrators` :", adminError);
        if (Platform.OS === 'web') {
          alert("Erreur : Impossible d'ajouter l'administrateur à l'événement.");
        } else {
          Alert.alert("Erreur", "Impossible d'ajouter l'administrateur à l'événement.");
        }
        return;
      }
  
      if (Platform.OS === 'web') {
        alert("L'événement a été mis à jour avec succès.");
        router.replace("/event/eventsCompany");
      } else {
        Alert.alert("Succès", "L'événement a été mis à jour avec succès.", [
          {
            text: "OK",
            onPress: () => {
              setTimeout(() => {
                reset();
                router.replace("/event/eventsCompany");
              }, 1000);
            },
          },
        ]);
      }
    } catch (err) {
      console.error("Erreur inattendue :", err);
      if (Platform.OS === 'web') {
        alert("Erreur inattendue : Veuillez réessayer plus tard.");
      } else {
        Alert.alert("Erreur inattendue", "Veuillez réessayer plus tard.");
      }
    }
  };

  const generateCauseAnalysis = async () => {
    const OPENAI_API_KEY = Constants.expoConfig?.extra?.NEXT_PUBLIC_OPENAI_API_KEY;
  
    if (!OPENAI_API_KEY) {
      alert("Erreur : Clé API OpenAI introuvable.");
      return;
    }
  
    if (!eventData?.description) {
      alert("Erreur : Aucune description d'événement trouvée.");
      return;
    }
  
    setLoadingIA(true);
  
    const prompt = `  
En te basant sur la description de l'événement : ${eventData.description} et sur le contexte de l'entreprise suivant :
- Histoire de l'entreprise : ${contextData?.history}
- Contexte de l'entreprise : ${contextData?.context}
- Organisation interne : ${contextData?.organisation}
- Site web : ${contextData?.website}

identifie 3 causes racines probables à l'aide de la méthode des "5 pourquoi". 
Pour chaque cause, explique l'analyse que tu as faite des "5 pourquoi" (sans les 
répéter), de manière lisible et fluide. Ton analyse ne doit pas être catégorique et
doit rester au "conditionnel", sans exagération excessive (pourrait, pourrait, 
pourrait...). Propose ensuite une action corrective pour chaque cause identifiée, 
en expliquant comment elle permet de traiter efficacement cette cause racine.
Affiche les résultats dans le format suivant :
Cause 1 (la plus probable) : [texte en 150 mots maximum sans bullet de 
présentation expliquant la cause et le chemin de déduction]
Action corrective associée à cause 1 : [texte en 100 mots maximum sans bullet 
de présentation décrivant l'action et sa pertinence]
Cause 2 : ...
Action corrective associée à cause 2 : ...
Cause 3 : ...
Action corrective associée à cause 3 : ...
Sois rigoureux, factuel et clair. Ne répète pas le même raisonnement pour les 3 
causes. Si possible, fais varier les types de causes (humaine, organisationnelle, 
matérielle, etc.).
    `;
  
    try {
      const response = await fetch("https://api.openai.com/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${OPENAI_API_KEY}`,
        },
        body: JSON.stringify({
          model: "gpt-4", // Ou "gpt-3.5-turbo"
          messages: [{ role: "user", content: prompt }],
          max_tokens: 500,
          temperature: 0.7,
        }),
      });
  
      const data = await response.json();
  
      console.log("🔍 Réponse OpenAI :", data); // DEBUG
  
      if (data.error) {
        console.error("❌ Erreur OpenAI :", data.error);
        alert(`Erreur API : ${data.error.message}`);
        return;
      }
  
      if (data.choices && data.choices.length > 0 && data.choices[0].message?.content) {
        const generatedText = data.choices[0].message.content.trim();
        setValue("causes", generatedText); // ✅ Met à jour le champ causes
      } else {
        alert("Erreur : L'analyse des causes a échoué.");
      }
    } catch (error) {
      console.error("Erreur OpenAI :", error);
      alert("Erreur : Impossible de générer l'analyse des causes.");
    } finally {
      setLoadingIA(false); // ❌ Désactive le chargement après la requête
    }
  };
  
  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 0}
    >
      <ScrollView 
        style={styles.form}
        contentContainerStyle={[styles.formContent, { maxWidth: 800, width: "100%", alignSelf: "center" }]}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.container}>
          {/* Formulaire */}
          <View style={styles.form}>
            {/* Type */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Type d'événement</Text>
              <Controller
                control={control}
                name="type"
                rules={{ required: "Veuillez sélectionner un événement." }}
                render={({ field: { onChange, value } }) => (
                  <Dropdown
                    style={dropdownStyles.dropdown}
                    placeholderStyle={dropdownStyles.placeholderStyle}
                    selectedTextStyle={dropdownStyles.selectedTextStyle}
                    data={typesEvent}
                    placeholder="Sélectionner un événement"
                    value={value}
                    onChange={(item) => {
                      onChange(item.value);
                    }}
                    labelField="label"
                    valueField="value"
                  />
                )}
              />
              {errors.type && (
                <Text style={styles.errorText}>{errors.type.message}</Text>
              )}
            </View>

            {/* Sélection du produit pour les défauts matériels */}
            {selectedType === "material_defect" && (
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Produit concerné</Text>
                <Controller
                  control={control}
                  name="product_id"
                  rules={{ required: "Veuillez sélectionner un produit." }}
                  render={({ field: { onChange, value } }) => (
                    <Dropdown
                      style={dropdownStyles.dropdown}
                      placeholderStyle={dropdownStyles.placeholderStyle}
                      selectedTextStyle={dropdownStyles.selectedTextStyle}
                      data={products}
                      placeholder="Sélectionner un produit"
                      value={value}
                      onChange={(item) => {
                        onChange(item.value);
                      }}
                      labelField="label"
                      valueField="value"
                    />
                  )}
                />
                {errors.product_id && (
                  <Text style={styles.errorText}>{errors.product_id.message}</Text>
                )}
              </View>
            )}

            {/* Champ Nom */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Libellé</Text>
              <Controller
                control={control}
                name="wording"
                rules={{ required: "Le libellé est requis." }}
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    maxLength={20}
                    placeholderTextColor={"#6B7280"}
                    style={styles.input}
                    placeholder="Libellé"
                    onChangeText={onChange}
                    value={value}
                  />
                )}
              />
              {errors.wording && (
                <Text style={styles.errorText}>{errors.wording.message}</Text>
              )}
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>Description</Text>
              <Controller
                control={control}
                name="description"
                rules={{ required: "Le libellé est requis." }}
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    maxLength={300}
                    placeholderTextColor={"#6B7280"}
                    style={styles.input}
                    placeholder="Description"
                    onChangeText={onChange}
                    value={value}
                    multiline
                  />
                )}
              />
              {errors.wording && (
                <Text style={styles.errorText}>{errors.wording.message}</Text>
              )}
            </View>

            {/* Date */}
            {/* Date Picker pour Android */}
            {Platform.OS === "android" && (
              <TouchableOpacity
                onPress={showDatePicker}
                style={styles.dateTimePickerContainer}
              >
                <Text style={styles.dateTimePickerLabel}>Date</Text>
                <Text style={styles.datePicker}>
                  {selectedDate.toLocaleDateString()}
                </Text>
              </TouchableOpacity>
            )}

            {/* Date Picker pour iOS */}
            {Platform.OS === "ios" && (
              <View style={styles.dateTimePickerContainer}>
                <Text style={styles.label}>Date</Text>
                <DateTimePicker
                  value={selectedDate}
                  mode="date"
                  display="default"
                  onChange={handleDateChange}
                  style={styles.datePicker}
                />
              </View>
            )}

            {/* Champ Responsable/Administrateur */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Responsable supposé</Text>
              <Controller
                control={control}
                name="administrator"
                rules={{ required: "Veuillez sélectionner un administrateur." }}
                render={({ field: { onChange, value } }) => (
                  <Dropdown
                    style={dropdownStyles.dropdown}
                    placeholderStyle={dropdownStyles.placeholderStyle}
                    selectedTextStyle={dropdownStyles.selectedTextStyle}
                    data={usersAdmin}
                    placeholder={
                      loading
                        ? "Chargement du responsable..."
                        : "Choix du responsable"
                    }
                    searchPlaceholder="Sélectionner un administrateur"
                    value={value}
                    onChange={(item) => {
                      onChange(item.value);
                    }}
                    labelField="label"
                    valueField="value"
                  />
                )}
              />
              {errors.administrator && (
                <Text style={styles.errorText}>
                  {errors.administrator.message}
                </Text>
              )}
            </View>

            {/* Champ Processus */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Processus concerné</Text>
              <Controller
                control={control}
                name="process"
                rules={{ required: "Le responsable est requis." }}
                render={({ field: { onChange, value } }) => (
                  <Dropdown
                    style={dropdownStyles.dropdown}
                    placeholderStyle={dropdownStyles.placeholderStyle}
                    selectedTextStyle={dropdownStyles.selectedTextStyle}
                    inputSearchStyle={dropdownStyles.inputSearchStyle}
                    iconStyle={dropdownStyles.iconStyle}
                    data={process}
                    search
                    maxHeight={300}
                    labelField="label"
                    valueField="value"
                    placeholder={
                      loading
                        ? "Chargement des processus..."
                        : "Choix du processus"
                    }
                    searchPlaceholder="Choix du processus"
                    value={value}
                    onChange={(item) => {
                      onChange(item.value);
                    }}
                    renderLeftIcon={() => (
                      <AntDesign
                        style={dropdownStyles.icon}
                        color="black"
                        name="Safety"
                        size={20}
                      />
                    )}
                  />
                )}
              />
            </View>

            {/* Champ Photo */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Fichier</Text>

              {/* ✅ Cache le bouton si une image est déjà sélectionnée */}
              {imageUris.length === 0 && (
                <TouchableOpacity
                  style={styles.imagePicker}
                  onPress={handleSelectImages}
                >
                  <View
                    style={{
                      width: "100%",
                      display: "flex",
                      flexDirection: "column",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <Octicons
                      style={{
                        borderWidth: 1,
                        borderColor: "#f99527",
                        borderRadius: 7,
                        padding: 5,
                        textAlign: "center",
                      }}
                      name="upload"
                      size={24}
                      color="black"
                    />
                    <Text style={{ textAlign: "center", fontWeight: "normal" }}>
                      Choisir le fichier
                    </Text>
                  </View>
                </TouchableOpacity>
              )}

              {/* ✅ Affichage des images sélectionnées en 2 colonnes */}
              {imageUris.length > 0 && (
                <View style={styles.imageGrid}>
                  {imageUris.map((uri, index) => (
                    <Image
                      key={index}
                      source={{ uri }}
                      style={styles.imagePreview}
                    />
                  ))}
                </View>
              )}
            </View>
            <View>

            {/* Champ Causes */}
            <View style={styles.introduction}>
                          <Text style={{ fontSize: 14 }}>Analyse des causes</Text>
              
                          {/* Bouton IA */}
                          <TouchableOpacity onPress={generateCauseAnalysis} disabled={loadingIA}>
                            {loadingIA ? (
                              <ActivityIndicator size="small" color="#f99527" />
                            ) : (
                              <Image style={styles.IAButtonImage} source={require("@/assets/images/qse.png")} />
                            )}
                          </TouchableOpacity>
                        </View>

              <Controller
                control={control}
                name="causes"
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    maxLength={500}
                    placeholderTextColor={"#6B7280"}
                    style={styles.input}
                    placeholder="Libellé"
                    onChangeText={onChange}
                    value={value}
                    multiline
                  />
                )}
              />
              {errors.causes && (
                <Text style={styles.errorText}>{errors.causes.message}</Text>
              )}
            </View>

            <ContainedButton
              label="Modifier"
              backgroundColor="#F99527"
              onPress={async () => {
                await handleSubmit(handleSubmitUpdateEvent)(); // ✅ Gère l'attente de la soumission
              }}
            />
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  headSection: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 30,
  },
  backButton: {
    width: 24,
    textAlign: "left",
  },
  titleWrapper: {
    display: "flex",
    flexDirection: "row",
    marginVertical: 15,
  },
  introduction: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 20,
    width: "100%",
  },
  IAButtonImage: {
    height: 24,
    width: 24,
  },
  title: {
    color: "#262627",
    fontSize: 17,
    textAlign: "left",
  },
  form: {
    flex: 1,
  },
  formContent: {
    paddingHorizontal: 30,
    paddingBottom: 50,
  },
  input: {
    borderWidth: 2,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "auto",
    padding: 12,
    marginTop: 10,
    color: "#000000",
    marginBottom: 10,
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginBottom: 5,
  },
  infoText: {
    fontSize: 14,
    marginVertical: 10,
    color: "#555",
  },
  inputContainer: {
    position: "relative",
    marginBottom: 20,
    width: "100%",
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
    textAlign: "left",
  },
  confirmButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    marginBottom: 100,
    backgroundColor: "#f99527",
    marginTop: 10,
  },
  confirmButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  imagePicker: {
    width: "100%",
    padding: 10,
    backgroundColor: "#FFFFFF",
    borderColor: "#CCC",
    borderRadius: 8,
    borderWidth: 2,
    alignItems: "center",
    marginBottom: 10,
    marginTop: 10,
  },
  imageGrid: {
    flexDirection: "row",
    flexWrap: "wrap", // ✅ Permet le passage à la ligne après 2 images
    justifyContent: "flex-start", // ✅ Alignement des images
    gap: 10, // ✅ Espacement entre les images
  },
  imagePickerText: {
    color: "#1F2937",
    fontWeight: "bold",
  },
  imagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  imageContainer: {
    width: "100%",
    justifyContent: "center",
  },
  uploadPlaceholder: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  uploadIcon: {
    borderWidth: 1,
    borderColor: "#f99527",
    borderRadius: 7,
    padding: 5,
  },
  uploadText: {
    textAlign: "center",
    fontWeight: "normal",
  },
  fullImagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  dateTimePickerContainer: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    textAlign: "left",
    marginVertical: 20,
  },
  dateTimePickerLabel: {
    color: "#063E76",
    fontSize: 14,
    fontWeight: "bold",
    backgroundColor: "#FFF",
  },
  datePicker: {
    width: "100%",
    marginTop: 10,
    marginBottom: 10,
  },
});

// Style du dropdown
const dropdownStyles = StyleSheet.create({
  dropdown: {
    marginVertical: 16,
    height: 50,
    backgroundColor: "white",
    borderRadius: 12,
    padding: 12,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,

    elevation: 2,
  },
  icon: {
    marginRight: 5,
  },
  item: {
    padding: 17,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  textItem: {
    flex: 1,
    fontSize: 16,
  },
  placeholderStyle: {
    fontSize: 16,
  },
  selectedTextStyle: {
    fontSize: 16,
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 16,
  },
});
