/*
app/screens/notifications/FlashQSEList.tsx

Liste des flash QSE.

Informations pertinentes :

- on récupère la liste des Flash QSE depuis la table `flash_qse` sur Supabase.
- Les Flash QSE appartiennent à une entreprise. Ils ont un champ `uid_company` (clé étrangère) qui contient l'id de l'entreprise à laquelle ils appartiennent. 
*/

import {
  FlatList,
  Image,
  Keyboard,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { useContext } from "react";
import { AppContext } from "@/state/AppContext";
import { useState, useEffect } from "react";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AntDesign } from "@expo/vector-icons";
import Octicons from "@expo/vector-icons/Octicons";
import { supabase } from "@/lib/supabase";
import FontAwesome from "@expo/vector-icons/FontAwesome";
import HeaderV2 from "@/components/common/HeaderV2.native";
import React from "react";
import { useFocusEffect } from "@react-navigation/native";
import { useRouter } from "expo-router";

export default function FlashQSEList() {
  const router = useRouter();
  const [flashQSEList, setFlashQSEList] = useState<any>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState(""); // État pour la barre de recherche
  const [filteredFlashQSEList, setFilteredFlashQSEList] = useState<any>([]); // Liste filtrée
  const { user } = useContext(AppContext);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  // Récupérer la liste des Flash QSE de l'entreprise
  useFocusEffect(
    React.useCallback(() => {
      const fetchFlashQSEs = async () => {
        try {
          if (!user || !user.uid || !user.companies?.[0]?.id) {
            setFlashQSEList([]); // Aucun Flash QSE
            return;
          }

          const { data, error } = await supabase
            .from("flash_qse")
            .select("*")
            .eq("uid_company", user.companies[0].id)
            .order("created_at", { ascending: false });

          if (error) {
            console.error(
              "Erreur lors de la récupération des Flash QSE :",
              error.message
            );
            return;
          }

          setFlashQSEList(data || []); // Stocke un tableau vide si `data` est null
        } catch (err) {
          console.error("Erreur inattendue :", err);
          setFlashQSEList([]); // Définit un tableau vide en cas d'erreur
        } finally {
          setLoading(false);
        }
      };

      fetchFlashQSEs();
    }, [user]) // Recharge les données à chaque focus ou changement d'utilisateur
  );

  // Mettre à jour la liste filtrée
  useEffect(() => {
    const filteredList = flashQSEList.filter((item: any) =>
      item.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredFlashQSEList(filteredList);
  }, [searchQuery, flashQSEList]);

  return (
    <View style={styles.mainContainer}>
      <HeaderV2 />
      <View style={styles.container}>
          {/* En-tête */}
          <View style={styles.headSection}>
            {/* Bouton de retour */}
            <TouchableOpacity 
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <AntDesign
                name="arrowleft"
                size={30}
                color="black"
              />
            </TouchableOpacity>

            {/* Titre */}
            <View style={styles.titleWrapper}>
              <Text style={styles.title}>Flashs QSE</Text>
            </View>
          </View>

          {/* Header */}
          <View style={styles.header}>
            <Text style={{ fontWeight: "bold" }}>Créer un Flash QSE :</Text>
            <TouchableOpacity
              onPress={() =>
                navigation.navigate("screens/notifications/CreateFlashQSE")
              }
            >
              <Octicons name="diff-added" size={25} color="#F99527" />
            </TouchableOpacity>
          </View>

          {/* Barre de recherche */}
          <View style={styles.searchContainer}>
            <View style={styles.searchInputWrapper}>
              <FontAwesome
                name="search"
                size={24}
                color="#3C3c4399"
                style={styles.searchIcon}
              />
              <TextInput
                style={styles.searchInput}
                placeholder="Rechercher par nom..."
                placeholderTextColor="#3C3c4399"
                value={searchQuery}
                onChangeText={(text) => setSearchQuery(text)}
              />
            </View>
          </View>

          {/* Liste des Flash QSE */}
          {loading ? (
            <Text>Chargement...</Text>
          ) : flashQSEList.length === 0 ? (
            <Text
              style={{ textAlign: "center", marginTop: 20, color: "#8c8c8c" }}
            >
              Aucun Flash.
            </Text>
          ) : (
            <FlatList
              data={filteredFlashQSEList}
              keyExtractor={(item) => item.id.toString()|| Math.random().toString()}
              renderItem={({ item }) => (
                <TouchableOpacity
                  onPress={() => {
                    if (Platform.OS === 'web') {
                      // Sur le web, mettre à jour l'URL avec l'ID
                      window.history.pushState({}, '', `?id=${item.id}`);
                    }
                    navigation.navigate(
                      "screens/notifications/FlashQSEDetails",
                      { flashQSE: item }
                    );
                  }}
                  style={styles.listItem}
                >
                  <View style={styles.leftElementsCard}>
                    {/* Image */}
                    <Image
                      source={{ uri: item.cover }}
                      style={styles.qseCardImage}
                    />
                  </View>

                  {/* Textes */}
                  <View style={styles.rightElementsCard}>
                    <Text style={styles.listTitle}>{item.name}</Text>
                    <Text style={styles.listDescription2} numberOfLines={2}>
                      {item.context}
                    </Text>
                  </View>
                </TouchableOpacity>
              )}
              contentContainerStyle={styles.listContainer}
            />
          )}
        </View>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
    paddingHorizontal: 20,
  },
  headSection: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    position: "relative",
    marginTop: 20,
    marginBottom: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    position: "absolute",
    left: 0,
    zIndex: 1,
  },
  titleWrapper: {
    display: "flex",
    flexDirection: "row",
    flex: 1,
    justifyContent: "center",
  },
  title: {
    color: "#262627",
    fontSize: 20,
    textAlign: "center",
    fontWeight: "bold",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 10,
    marginTop: 14,
  },
  listContainer: {
    width: "100%",
    paddingVertical: 10,
  },
  listItem: {
    width: "100%",
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    display: "flex",
    justifyContent: "flex-start",
    flexDirection: "row",
    gap: 10,
    borderWidth: 1,
    borderColor: "#CCC",
  },
  listTitle: {
    fontSize: 16,
    color: "#1C3144",
    fontWeight: "bold",
  },
  listDescription: {
    fontSize: 14,
    color: "#1c3144",
    fontStyle: "normal",
    flexWrap: "wrap",
  },
  listDescription2: {
    // width: "100%",
    fontSize: 14,
    color: "#1c3144",
    fontStyle: "normal",
    flexWrap: "wrap",
  },
  leftElementsCard: {
    // width: "100%",
    backgroundColor: "#FFFFFF",
  },
  qseCardImage: {
    width: 90,
    height: 90,
    borderRadius: 7,
  },
  rightElementsCard: {
    width: "70%",
    backgroundColor: "#FFFFFF",
    borderRadius: 7,
    justifyContent: "space-between",
    flexDirection: "column",
    rowGap: 5,
  },
  searchContainer: {
    width: "100%",
    marginBottom: 20,
    marginTop: 10,
    paddingHorizontal: 10,
  },
  searchInputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1C314414",
    borderRadius: 8,
    paddingHorizontal: 10,
    height: 60,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    padding: 10,
    fontSize: 16,
    color: "#000000",
  },
});
