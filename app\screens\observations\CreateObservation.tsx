/*
app/screens/observations/CreateObservation.tsx

Formulaire pour créer un constat.

Informations pertinentes :

- Un constat est reliée à l'entreprise de l'utilisateur connecté qui la crée
- Quand le formulaire est validé les données sont envoyées dans -> la table `observations`, la table de jointure `audit_observations`
- Les contats ont les types suivants :
   -> `non_compliant` -> Constat de non-conformité
   -> `sensitive_point` -> Point sensible
   -> `progress` -> Piste de progrès
   -> `note` -> Note
- On récupère les processus de l'entreprise à afficher dans le dropdown depuis -> la table `process`

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User` et `Extern` n'ont pas accès à cette page.
*/

import React, { useContext, useEffect, useState } from "react";
import {
  Alert,
  StyleSheet,
  View,
  TextInput,
  Text,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import { supabase } from "../../../lib/supabase.native";
import { Controller, useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import {
  ParamListBase,
  RouteProp,
  useNavigation,
  useRoute,
} from "@react-navigation/native";
import { AntDesign } from "@expo/vector-icons";
import { Dropdown } from "react-native-element-dropdown";
import { useRouter } from "expo-router";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";

// Choix du dropdown (audits)
const observationTypes = [
  { label: "Point sensible", value: "sensitive_point" },
  { label: "Non conforme", value: "non_compliant" },
  { label: "Piste de progrès", value: "progress" },
  { label: "Note", value: "note" },
];

// Données de navigation
type AuditDetailsRouteParams = {
  audit: {
    id: number;
    name: string;
    type: string;
    field: string;
    date: string;
  };
};

export default function CreateObservation() {
  const route = useRoute<RouteProp<{ params: AuditDetailsRouteParams }, "params">>();
  const { audit } = route.params;
  const [process, setProcess] = useState([]);
  const router = useRouter();
  const { user, companySelected } = useContext(AppContext);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const [isRequirementRiskVisible, setIsRequirementRiskVisible] = useState(true);

  const {
    handleSubmit,
    formState: { errors },
    control,
    reset,
    watch
  } = useForm({
    defaultValues: {
      name: "",
      date: "",
      type: "",
      requirements: "",
      risk: "",
      process: "",
      uid_company: "",
    },
    mode: "onBlur",
    criteriaMode: "all",
  });

  // Récupérer les informations de l'entreprise (ici les risques et processus)
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Récupérer les processus de l'entreprise
        const { data: processData, error: processError } = await supabase
          .from("process")
          .select("id, process_name")
          .eq("company_id", companySelected?.id);

        if (processError) {
          console.error(
            "Erreur lors de la récupération des processus :",
            processError.message
          );
        } else {
          const formattedProcess: any = processData.map((process: any) => ({
            label: process.process_name,
            value: process.id,
          }));
          setProcess(formattedProcess);
        }
      } catch (err) {
        console.error("Erreur inattendue lors de la récupération :", err);
      }
    };

    fetchData();
  }, [user]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  useEffect(() => {
    if (["sensitive_point", "non_compliant"].includes(watch("type"))) {
      setIsRequirementRiskVisible(false);
    } else {
      setIsRequirementRiskVisible(true);
    }
  }, [watch("type")]);

  // Valider le formulaire
  const handleSubmitCreateObservation = async (data: any) => {
    try {
      setIsSubmitting(true);

      // Préparation des données pour la table `observations`
      const observationData = {
        name: data?.name,
        requirements: data?.requirements,
        type: data?.type,
        risk: data?.risk,
        process_id: data?.process,
        company_id: companySelected?.id,
        created_at: new Date(),
      };

      // Insertion dans la table `observations`
      const { data: createdObservation, error: observationError } =
        await supabase
          .from("observations")
          .insert(observationData)
          .select("id, name, type, company_id")
          .single();

      if (observationError) {
        Alert.alert(
          "Échec de la création",
          observationError.message || "Une erreur est survenue."
        );
        return;
      }

      // Insertion dans la table de jointure `audit_observations`
      const { error: auditObservationError } = await supabase
        .from("audit_observations")
        .insert({
          audit_id: audit?.id,
          observation_id: createdObservation.id,
          created_at: new Date(),
        });

      if (auditObservationError) {
        Alert.alert(
          "Erreur de liaison",
          auditObservationError.message ||
            "Échec de l'association avec l'audit."
        );
        return;
      }

      // Confirmation de la création
      Alert.alert("Succès", `Constat ${data?.name} créé avec succès.`, [
        {
          text: "OK",
          onPress: () => {
            reset();
            // console.log("Audit envoyé à AuditDetails :", audit);
            // router.push({
            //   pathname: "/screens/audits/AuditDetails",
            //   params: {
            //     id: audit.id,
            //     name: audit.name,
            //     type: audit.type,
            //     field: audit.field,
            //     date: audit.date,
            //   },
            // });
            router.back();
          },
        },
      ]);
    } catch (err) {
      Alert.alert("Erreur inattendue", "Veuillez réessayer plus tard.");
      console.error("Erreur inattendue :", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.select({ ios: 0, android: 100 })}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
          <ScrollView style={styles.form}>
            {/* Champ Type */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Types de constat</Text>
              <Controller
                control={control}
                name="type"
                rules={{ required: "Le type est requis." }}
                render={({ field: { onChange, value } }) => (
                  <Dropdown
                    style={dropdownStyles.dropdown}
                    placeholderStyle={dropdownStyles.placeholderStyle}
                    selectedTextStyle={dropdownStyles.selectedTextStyle}
                    inputSearchStyle={dropdownStyles.inputSearchStyle}
                    iconStyle={dropdownStyles.iconStyle}
                    data={observationTypes}
                    search
                    maxHeight={300}
                    labelField="label"
                    valueField="value"
                    placeholder={"Choix du type"}
                    searchPlaceholder="Rechercher un type"
                    value={value}
                    onChange={(item) => {
                      onChange(item.value);
                    }}
                    renderLeftIcon={() => (
                      <AntDesign
                        style={dropdownStyles.icon}
                        color="black"
                        name="Safety"
                        size={20}
                      />
                    )}
                  />
                )}
              />
            </View>

            {/* Champ Éxigence */}
            {isRequirementRiskVisible && (
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Exigence concernée</Text>
              <Controller
                control={control}
                name="requirements"
                rules={{ required: "L'exigence est requise." }}
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    placeholderTextColor={"#6B7280"}
                    style={styles.input}
                    placeholder="Exigence"
                    onChangeText={onChange}
                    value={value}
                    multiline
                  />
                )}
              />
              {errors.requirements && (
                <Text style={styles.errorText}>
                  {errors.requirements.message}
                </Text>
              )}
            </View>
            )}

            {/* Champ Nom */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Libellé du constat</Text>
              <Controller
                control={control}
                name="name"
                rules={{ required: "L'exigence est requise." }}
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    maxLength={150}
                    placeholderTextColor={"#6B7280"}
                    style={styles.input}
                    placeholder="Libellé du constat"
                    onChangeText={onChange}
                    value={value}
                    multiline
                  />
                )}
              />
              {errors.name && (
                <Text style={styles.errorText}>{errors.name.message}</Text>
              )}
            </View>

            {/* Champ Risque */}
            {isRequirementRiskVisible && (
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Risque</Text>
              <Controller
                control={control}
                name="risk"
                rules={{ required: "Le risque est requis." }}
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    placeholderTextColor={"#6B7280"}
                    style={styles.input}
                    placeholder="Risque..."
                    onChangeText={onChange}
                    value={value}
                    multiline
                  />
                )}
              />
              {errors.risk && (
                <Text style={styles.errorText}>{errors.risk.message}</Text>
              )}
            </View>
            )}

            {/* Champ Processus */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Processus concerné</Text>
              <Controller
                control={control}
                name="process"
                rules={{ required: "Le processus est requis." }}
                render={({ field: { onChange, value } }) => (
                  <Dropdown
                    style={dropdownStyles.dropdown}
                    placeholderStyle={dropdownStyles.placeholderStyle}
                    selectedTextStyle={dropdownStyles.selectedTextStyle}
                    inputSearchStyle={dropdownStyles.inputSearchStyle}
                    iconStyle={dropdownStyles.iconStyle}
                    data={process}
                    search
                    maxHeight={300}
                    labelField="label"
                    valueField="value"
                    placeholder={"Choix du processus"}
                    searchPlaceholder="Rechercher un processus"
                    value={value}
                    onChange={(item) => {
                      onChange(item.value);
                    }}
                    renderLeftIcon={() => (
                      <AntDesign
                        style={dropdownStyles.icon}
                        color="black"
                        name="Safety"
                        size={20}
                      />
                    )}
                  />
                )}
              />
            </View>

            {/* Bouton de soumission */}
            <TouchableOpacity
              style={styles.confirmButton}
              onPress={handleSubmit(handleSubmitCreateObservation)}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Text style={styles.confirmButtonText}>Créer</Text>
              )}
            </TouchableOpacity>
          </ScrollView>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    height: "100%",
    width: "100%",
    alignItems: "center",
    alignContent: "center",
    backgroundColor: "#FFFFFF",
    paddingBottom: 50,
  },
  headSection: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 30,
  },
  backButton: {
    width: 24,
    textAlign: "left",
  },
  titleWrapper: {
    display: "flex",
    flexDirection: "row",
    marginVertical: 15,
  },
  title: {
    color: "#262627",
    fontSize: 17,
    textAlign: "left",
  },
  form: {
    width: "100%",
    alignSelf: "center",
    paddingHorizontal: 30,
    marginTop: 50,
    marginBottom: 100,
  },
  input: {
    borderWidth: 2,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "auto",
    padding: 12,
    marginTop: 10,
    color: "#000000",
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginBottom: 5,
  },
  infoText: {
    fontSize: 14,
    marginVertical: 10,
    color: "#555",
  },
  inputContainer: {
    position: "relative",
    marginBottom: 20,
    width: "100%",
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
    textAlign: "left",
  },
  confirmButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    marginBottom: 200,
    backgroundColor: "#f99527",
    marginTop: 10,
  },
  confirmButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  imagePicker: {
    width: "100%",
    padding: 10,
    backgroundColor: "#FFFFFF",
    borderColor: "#CCC",
    borderRadius: 8,
    borderWidth: 2,
    alignItems: "center",
    marginBottom: 10,
    marginTop: 10,
  },
  imagePickerText: {
    color: "#1F2937",
    fontWeight: "bold",
  },
  imagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  imageContainer: {
    width: "100%",
    justifyContent: "center",
  },
  uploadPlaceholder: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  uploadIcon: {
    borderWidth: 1,
    borderColor: "#f99527",
    borderRadius: 7,
    padding: 5,
  },
  uploadText: {
    textAlign: "center",
    fontWeight: "normal",
  },
  fullImagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  dateTimePickerContainer: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    textAlign: "left",
    marginVertical: 20,
  },
  dateTimePickerLabel: {
    color: "#063E76",
    fontSize: 14,
    fontWeight: "bold",
    backgroundColor: "#FFF",
  },
  datePicker: {
    width: "100%",
    marginTop: 10,
    marginBottom: 10,
  },
});

// Style du dropdown
const dropdownStyles = StyleSheet.create({
  dropdown: {
    marginVertical: 16,
    height: 50,
    backgroundColor: "white",
    borderRadius: 12,
    padding: 12,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,

    elevation: 2,
  },
  icon: {
    marginRight: 5,
  },
  item: {
    padding: 17,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  textItem: {
    flex: 1,
    fontSize: 16,
  },
  placeholderStyle: {
    fontSize: 16,
  },
  selectedTextStyle: {
    fontSize: 16,
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 16,
  },
});
