/*
app/objectif/updateObjectif.tsx

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern` et `Extern` n'ont pas accès à cette page.
*/

import ContainedButton from "@/components/ui/buttons/ContainedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useRouter, useLocalSearchParams } from "expo-router";
import {
  StyleSheet,
  Image,
  View,
  Text,
  ScrollView,
  Alert,
  Platform,
} from "react-native";
import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import CustomizedSelect from "@/components/ui/customizedSelect/customizedSelect";
import TypeProcess from "@/types/typeProcess";
import TypeObjectif from "@/types/typeObjectif";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";

const levels = [
  { value: 0, label: "Non maîtrisée" },
  { value: 1, label: "Partiel" },
  { value: 2, label: "Maîtrisée" },
];

export default function UpdateObjectifs() {
  const [loading, setLoading] = useState(false);
  const [imageUri, setImageUri] = useState<string | null>(null);
  const { user, companySelected } = useContext(AppContext);
  const [process, setProcess] = useState<TypeProcess[]>([]);
  const [objectif, setObjectif] = useState<TypeObjectif>();
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const {
    register,
    handleSubmit,
    setError,
    formState: { errors, touchedFields },
    control,
    reset,
    watch,
    setValue,
  } = useForm<TypeObjectif>({ defaultValues: { level: 0 } });
  const { created_at, company_name, objectif_id, designation, value_latest, target, measuring_indicator } =
    useLocalSearchParams();

  useEffect(() => {
    setIsLoading(true);
    const fetchUsersAdmin = async () => {
      const { data: dataGetted, error } = await supabase
        .from("process")
        .select()
        .eq("uid_admin", user?.uid);

      setProcess(dataGetted as any);

      setIsLoading(false);
    };
    fetchUsersAdmin();
  }, []);

  useEffect(() => {
    const fetchProcesses = async () => {
      const { data } = await supabase
        .from("process")
        .select()
        .eq("uid_admin", user?.uid)
        .eq("company_id", companySelected?.id);

      setProcess(
        data?.map((item) => ({ label: item.process_name, value: item.id })) ||
          ([] as any)
      );
    };

    const fetchObjectif = async () => {
      const { data } = await supabase
        .from("objectifs")
        .select("*, values(*)")
        .eq("id", objectif_id)
        .single();

      if (data) {
        // Trier les valeurs par date_value
        const valuesWithDate = data.values?.filter((v: any) => v.date_value) || [];
        const valuesWithoutDate = data.values?.filter((v: any) => !v.date_value) || [];
        
        // Trier les valeurs avec date_value par date décroissante
        const sortedValuesWithDate = valuesWithDate.sort((a: any, b: any) => 
          new Date(b.date_value).getTime() - new Date(a.date_value).getTime()
        );
        
        // Trier les valeurs sans date_value par created_at décroissant
        const sortedValuesWithoutDate = valuesWithoutDate.sort((a: any, b: any) => 
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
        
        // Combiner les deux listes triées
        const allSortedValues = [...sortedValuesWithDate, ...sortedValuesWithoutDate];
        
        // Mettre à jour l'objectif avec les valeurs triées
        const updatedObjectif = {
          ...data,
          values: allSortedValues
        };

        reset(updatedObjectif);
        setObjectif(updatedObjectif);
        setValue("process_id", data.process_id);
        setValue("level", data.level || 0);
        setValue("target", data.target);
        setValue("measuring_indicator", data.measuring_indicator);
        setValue("designation", data.designation);
      }
    };

    fetchProcesses();
    fetchObjectif();
  }, [objectif_id]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const updateObjectif = async (data: TypeObjectif) => {
    if (
      !data.designation &&
      !data.target &&
      !data.measuring_indicator &&
      !data.process_id &&
      !data.process.process_name
    ) {
      Toast.show({
        type: "error",
        text1: "Le nom et le prénom sont obligatoires",
        text1Style: { color: "#1C3144" },
      });
    }

    if (
      data.designation &&
      data.target &&
      data.measuring_indicator &&
      data.process_id
    ) {
      setLoading(true);

      try {
        const { error } = await supabase
          .from("objectifs")
          .update({
            target: data.target,
            designation: data.designation,
            measuring_indicator: data.measuring_indicator,
            process_id: data.process_id,
            level: data.level,
          })
          .eq("id", objectif_id);

        if (error) {
          console.log("error__", error);
          Toast.show({
            type: "error",
            text1: "Erreur lors d'enregistrement d'utilisateur",

            text1Style: { color: "#1C3144" },
          });
          setLoading(false);
          return;
        }

        Toast.show({
          type: "success",
          text1: "L'objectif était bien crée",
          text1Style: { color: "#1C3144" },
        });

        router.back();

        reset();
        setLoading(false);
      } catch (err) {
        setImageUri("");
        setLoading(false);

        Toast.show({
          type: "error",
          text1: "Une erreur est survenue",
          text1Style: { color: "#1C3144" },
          // text2: "This is some something 👋",
        });
      }
    }
  };

  function formatDate(dateString: string) {
    const date = new Date(dateString);
    return date.toLocaleDateString("fr-FR"); // Format français : jour/mois/année
  }

  return (
    <View style={styles.mainContainer}>
      <ScrollView style={styles.container}>
        <View style={styles.containerContent}>
          <View style={styles.registerInfo}>
            <View style={styles.inputs}>
              <View style={styles.infoContainer}>
                <View style={styles.infoText}>
                  <Text style={styles.infoLabel}>Entreprise:</Text>
                  <Text>{company_name}</Text>
                  <Text style={styles.infoLabel}>Descriptif:</Text>
                  <Text>{designation}</Text>
                  <Text style={styles.infoLabel}>Dernière valeur:</Text>
                  <Text>{objectif?.values?.[0]?.value_name || "Aucune valeur"}</Text>
                </View>
                <View>
                  <Text>{formatDate(created_at as string)}</Text>
                </View>
              </View>
              <InputCustomized
                label="Cible"
                placeholder="Modifier la cible"
                name="target"
                register={register}
                control={control}
                errors={errors}
                setError={setError}
              />
              <CustomizedSelect
                name="process_id"
                label="Processus"
                register={register}
                control={control}
                errors={errors}
                setError={setError}
                data={
                  process.map((item) => {
                    return { label: item.process_name, value: item.id };
                  }) as any
                }
              />
              <CustomizedSelect
                name="level"
                label="Niveau d'atteinte"
                register={register}
                control={control}
                errors={errors}
                setError={setError}
                data={
                  levels.map((item) => {
                    return { label: item.label, value: item.value };
                  }) as any
                }
              />
            </View>
          </View>

          {imageUri && (
            <Image
              source={{ uri: imageUri }}
              style={styles.image}
            />
          )}

          <View style={styles.buttons}>
            <ContainedButton
              label="Enregistrer"
              backgroundColor="#F99527"
              onPress={handleSubmit(updateObjectif)}
              disabled={loading}
            />
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  containerContent: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    width: "100%",
    gap: 20,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
    marginTop: 20,
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
  infoContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 10,
  },
  infoText: {
    display: "flex",
    gap: 10,
  },
  infoLabel: {
    fontWeight: "bold",
  },
  image: {
    width: 150,
    height: 150,
    marginVertical: 20,
    borderRadius: 150,
    alignSelf: 'center',
  },
});
