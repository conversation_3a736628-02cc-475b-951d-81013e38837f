import React, { useContext, useEffect, useState } from "react";
import ContainedButton from "@/components/ui/buttons/ContainedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useLocalSearchPara<PERSON>, useRouter } from "expo-router";
import {
  StyleSheet,
  Image,
  View,
  ScrollView,
  Alert,
  TouchableOpacity,
  Text,
  Platform,
} from "react-native";

import { useForm, Controller } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import CustomizedSelect from "@/components/ui/customizedSelect/customizedSelect";
import TypeProcess from "@/types/typeProcess";
import TypeProduct from "@/types/typeProduct";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import InputFileCustomized from "@/components/ui/inputs/InputFileCustomized";
import * as ImagePicker from "expo-image-picker";
import uploadPhoto from "@/lib/uploadPictureStorage";
import TypeProdut from "@/types/typeProduct";

type FormProduct = {
  name: string;
  description?: string;
  image?: string;
  status?: boolean;
};

const optionsStatus = [
  { label: "En service", value: true },
  { label: "Hors service", value: false },
];

export default function UpdateProduct() {
  const [loading, setLoading] = useState(false);
  const [imageUri, setImageUri] = useState<string | null>(null);
  const { user } = useContext(AppContext);
  const [product, setProduct] = useState<TypeProdut | null>(null);

  const router = useRouter();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  const { product_id, name, description, image, status } = useLocalSearchParams();
  console.log("Received params:", { product_id, name, description, image, status });
  
  const numericProductId = product_id ? (typeof product_id === 'string' ? product_id : product_id.toString()) : null;
  console.log("Processed product ID:", numericProductId);

  useEffect(() => {
    if (!numericProductId) {
      console.error("No valid product ID provided");
      Toast.show({
        type: "error",
        text1: "ID du produit invalide",
        text1Style: { color: "#1C3144" },
      });
      router.back();
      return;
    }

    const fetchProduct = async () => {
      try {
        console.log("Fetching product with ID:", numericProductId);
        const { data, error } = await supabase
          .from("products")
          .select("*")
          .eq("id", numericProductId)
          .single();

        if (error) {
          console.error("Error fetching product:", error);
          throw error;
        }
        
        console.log("Fetched product data:", data);
        setProduct(data);
      } catch (error) {
        console.error("Error in fetchProduct:", error);
      }
    };

    fetchProduct();
  }, [numericProductId]);

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors, touchedFields },
    control,
    reset,
  } = useForm<FormProduct>({
    defaultValues: {
      name: name as string,
      description: description as string,
      image: image as string,
      status: status === "true" ? true : false,
    },
  });

  useEffect(() => {
    if (product) {
      reset({
        name: product.name,
        description: product.description,
        image: product.image,
        status: product.status,
      });
    }
  }, [product, reset]);

  const inputs = [
    {
      label: "Nom",
      name: "name",
      placeholder: "Nom du matériel",
    },
    {
      label: "Description",
      name: "description",
      placeholder: "Description",
      type: "text",
      multiline: true,
    },
    {
      label: "Photo",
      name: "image",
      placeholder: "Choisir la photo",
      type: "img",
    },
    {
      label: "Status",
      name: "status",
      placeholder: "Status",
      type: "Select",
      data: optionsStatus,
    },
  ];
  const updateProduct = async (data: FormProduct) => {
    console.log("Starting updateProduct with data:", data);
    console.log("Product ID:", numericProductId);
    
    if (!numericProductId) {
      console.error("Invalid product ID");
      Toast.show({
        type: "error",
        text1: "ID du produit invalide",
        text1Style: { color: "#1C3144" },
      });
      return;
    }

    if (!data.name) {
      console.log("Error: Name is required");
      Toast.show({
        type: "error",
        text1: "Veuillez remplir le nom du produit",
        text1Style: { color: "#1C3144" },
      });
      return;
    }

    setLoading(true);

    try {
      let imageUrl = image as string;
      console.log("Current image URL:", imageUrl);
      
      if (imageUri) {
        console.log("New image selected, uploading...");
        const responseImg = await uploadPhoto(
          imageUri,
          user?.uid as string,
          "images",
          "products",
          "image/jpeg"
        );
        console.log("Upload response:", responseImg);
        
        if (!responseImg?.url) {
          console.error("Image upload failed:", responseImg);
          throw new Error("Erreur lors de l'upload de l'image");
        }
        imageUrl = responseImg.url;
        console.log("New image URL:", imageUrl);
      }

      const updateData = {
        name: data.name,
        description: data.description || "",
        image: imageUrl,
        status: data.status,
      };

      console.log("Preparing to update product with data:", {
        ...updateData,
        product_id: numericProductId,
        user_id: user?.uid
      });

      const { error } = await supabase
        .from("products")
        .update(updateData)
        .eq("id", numericProductId);

      if (error) {
        console.error("Supabase update error:", {
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        });
        Toast.show({
          type: "error",
          text1: "Erreur lors de l'enregistrement du produit",
          text2: error.message,
          text1Style: { color: "#1C3144" },
        });
        setLoading(false);
        return;
      }

      console.log("Product updated successfully");
      Toast.show({
        type: "success",
        text1: "Le produit a été modifié avec succès",
        text1Style: { color: "#1C3144" },
      });

      router.back();
      reset();
    } catch (err) {
      console.error("Unexpected error in updateProduct:", {
        error: err,
        stack: err instanceof Error ? err.stack : undefined
      });
      Toast.show({
        type: "error",
        text1: "Une erreur est survenue",
        text2: err instanceof Error ? err.message : "Erreur inconnue",
        text1Style: { color: "#1C3144" },
      });
    } finally {
      setLoading(false);
    }
  };

  const pickImage = async () => {
    const permissionResult =
      await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (!permissionResult.granted) {
      Toast.show({
        type: "error",
        text1: "Permissions refusées",
        text1Style: { color: "#1C3144" },
        text2: "Vous devez autoriser l'accès à vos photos.",
      });
      return;
    }

    const pickerResult = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ["images"],
      allowsEditing: true,
      quality: 1,
    });

    if (!pickerResult.canceled && pickerResult.assets.length > 0) {
      const selectedImageUri = pickerResult.assets[0].uri;
      setImageUri(selectedImageUri); // ✅ Mise à jour du state (même si asynchrone)

      // 📌 Déclencher immédiatement la mise à jour du profil avec l'image sélectionnée
    }
  };

  return (
    <ScrollView>
      <View style={styles.containerContent}>
        <View style={styles.registerInfo}>
          <View style={styles.inputs}>
            {inputs.map((input, index) => {
              if (input?.type === "Select") {
                return (
                  <View key={index}>
                    <CustomizedSelect
                      name={input.name}
                      label={input.label}
                      register={register}
                      control={control}
                      errors={errors}
                      setError={setError}
                      data={input.data as any}
                    />
                  </View>
                );
              }
              if (input?.type === "img") {
                return (
                  <View key={index}>
                    {!imageUri && !image ? (
                      <InputFileCustomized
                        label={"Photo"}
                        placeholder={"Choisir la photo"}
                        onPress={pickImage}
                      />
                    ) : (
                      <View style={{ display: "flex" }}>
                        <Text style={styles.label}>Photo</Text>
                        <TouchableOpacity onPress={pickImage}>
                          <Image
                            source={{ uri: imageUri || (image as string) }}
                            style={{
                              width: "100%",
                              height: 200,
                              marginTop: 10,
                              alignSelf: "center",
                            }}
                          />
                        </TouchableOpacity>
                      </View>
                    )}
                  </View>
                );
              } else {
                return (
                  <View key={index}>
                    <InputCustomized
                      label={input.label}
                      placeholder={input.placeholder}
                      register={register}
                      name={input.name}
                      control={control}
                      errors={errors}
                      setError={setError}
                      multiline={input.multiline}
                    />
                  </View>
                );
              }
            })}
          </View>
        </View>

        <View style={styles.buttons}>
          <ContainedButton
            label="Modifier"
            backgroundColor="#F99527"
            onPress={handleSubmit(updateProduct)}
            disabled={loading}
          />
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  containerContent: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    marginVertical: 20,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {
      paddingHorizontal: 20,
    }),
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    width: "100%",
    gap: 20,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  buttonPassword: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  titleContainer: {
    flexDirection: "row",
    gap: 8,
  },
  imgIsLoadingPage: {
    height: 75,
    width: 130,
    alignSelf: "center",
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
  input: {
    backgroundColor: "white",
    borderColor: "none",
    height: 40,
    padding: 10,
    borderRadius: 4,
  },
  label: {
    fontSize: 14,
    color: "#262627",
    marginVertical: 1,
    fontWeight: "bold",
  },
});
