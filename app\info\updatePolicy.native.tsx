/*
app/info/updatePolicy.tsx

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern` et `Extern` n'ont pas accès à cette page.
*/

import { useRouter } from "expo-router";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  Alert,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Image,
  Animated,
} from "react-native";
import { useContext, useEffect, useState, useRef } from "react";
import { Controller, useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import openai from "@/lib/openai.native"; // Importer OpenAI
import TypePolicy from "@/types/typePolicies";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import TypeIssue from "@/types/typeIssue";
import * as Progress from 'react-native-progress';

export default function UpdatePolicy() {
  const { user, companySelected } = useContext(AppContext);
  const router = useRouter();
  const [loadingIA, setLoadingIA] = useState(false);
  const [progress, setProgress] = useState(0);
  const [policiesData, setPoliciesData] = useState<TypePolicy | null>(null);
  const [enjeux, setEnjeux] = useState<TypeIssue | null>(null);
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  const {
    handleSubmit,
    formState: { errors },
    control,
    reset,
    setValue,
  } = useForm<TypePolicy>({
    defaultValues: {
      context: "",
    },
  });

  // Fonction pour l'animation de clignement
  const startBlinking = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 0.3,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  // Arrêter l'animation
  const stopBlinking = () => {
    fadeAnim.setValue(1);
  };

  useEffect(() => {
    if (loadingIA) {
      startBlinking();
    } else {
      stopBlinking();
    }
  }, [loadingIA]);

  // 🔄 Récupérer la politique existante
  useEffect(() => {
    const fetchContext = async () => {
      if (!companySelected?.id) return;

      const { data, error } = await supabase
        .from("policies")
        .select("*")
        .eq("company_id", companySelected.id)
        .single();

      if (error) {
        // Si aucune politique n'existe, on ne fait rien et on laisse l'utilisateur en créer une
        if (error.code === 'PGRST116') {
          console.log("Aucune politique existante, mode création activé");
          return;
        }
        console.error(
          "Erreur lors de la récupération de la politique :",
          error.message
        );
      } else {
        setPoliciesData(data);
        reset(data);
      }
    };

    const fetchEnjeu = async () => {
      if (!companySelected?.id) return;

      const { data, error } = await supabase
        .from("issues")
        .select("*")
        .eq("company_id", companySelected.id)
        .single();

      if (error) {
        console.error(
          "Erreur lors de la récupération des enjeux :",
          error.message
        );
      } else {
        setEnjeux(data);
      }
    };

    fetchEnjeu();
    fetchContext();
  }, [companySelected, reset]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  // 🔍 Fonction pour récupérer les données de l'entreprise et générer un texte
  const generatePolicyWithAI = async () => {
    try {
      setLoadingIA(true);
      setProgress(0);

      // Fonction pour progresser doucement vers une valeur cible
      const progressToTarget = async (target: number, duration: number) => {
        const startTime = Date.now();
        const startProgress = progress;
        const updateInterval = 50; // ms

        return new Promise<void>((resolve) => {
          const interval = setInterval(() => {
            const elapsed = Date.now() - startTime;
            if (elapsed >= duration) {
              setProgress(target);
              clearInterval(interval);
              resolve();
            } else {
              const newProgress = startProgress + (target - startProgress) * (elapsed / duration);
              setProgress(newProgress);
            }
          }, updateInterval);
        });
      };

      // Étape 1 : Récupérer les données de l'entreprise depuis Supabase
      await progressToTarget(0.2, 1000); // Progresser vers 20% en 1 seconde
      const { data, error } = await supabase
        .from("contexts")
        .select("history, context, organisation, website")
        .eq("company_id", companySelected?.id)
        .single();

      if (error) {
        Alert.alert("Erreur", "Impossible de récupérer les données.");
        console.error(error);
        setLoadingIA(false);
        return;
      }

      // Récupérer la politique existante si elle existe
      const currentPolicy = policiesData?.context || "";

      // Étape 2 : Envoyer ces données à OpenAI pour générer du texte
      await progressToTarget(0.4, 1000); // Progresser vers 40% en 1 seconde
      const prompt = `  
      Partant du contexte, des enjeux et de la cartographie de processus, 
              - Histoire de l'entreprise : ${data.history}
              - Contexte actuel : ${data.context}
              - Organisation interne : ${data.organisation}
              - Site web : ${data.website}
              - Enjeux identifiés à partir de l'analyse PESTEL : ${enjeux?.context}
              ${currentPolicy ? `
              - Politique existante (à améliorer et enrichir) :
              ${currentPolicy}
              ` : ''}
      agis en tant 
      que Conseiller(ère) en Stratégie et Performance Durable, expert(e) dans 
      l'élaboration de politiques d'entreprise intégrées et orientées action.
      Objectif Fondamental :
      ${currentPolicy ? `
      Améliorer et enrichir la politique d'engagement stratégique existante en :
      - Conservant les éléments pertinents et bien formulés
      - Renforçant les aspects qui manquent de précision
      - Ajoutant de nouvelles dimensions stratégiques si nécessaire
      - Assurant une meilleure intégration des principes de performance globale
      ` : `
      Rédiger une Politique d'Engagement Stratégique (ou Politique de 
      Performance Globale et Durable) pour l'entreprise décrite ci-dessous. Cette 
      politique ne doit PAS mentionner explicitement les acronymes "QHSE", 
      "QSE", etc., mais doit intégrer organiquement les principes de qualité, de 
      satisfaction client, de santé-sécurité au travail, de protection de l'environnement
      (incluant la performance énergétique) et de conformité, au sein même des 
      ambitions et des axes stratégiques de l'entreprise.
      `}
      Le Cœur de la Mission :
      La politique doit établir le lien logique et visible entre :
      1. Les Enjeux majeurs (internes/externes) identifiés pour l'entreprise.
      2. Les Axes Stratégiques définis par l'entreprise pour répondre à ces 
      enjeux (que tu identifieras ou affineras).
      3. L'orientation pour la déclinaison en Objectifs spécifiques au niveau 
      des Processus clés.
      Cette politique doit être inspirante, authentique, non-scolaire, et refléter 
      une vision unifiée de la performance.
      Instructions Précises pour la Génération de la Politique :
      1. Analyse Intégrée : Absorbe profondément le [contexte], les [enjeux], 
      les [axes stratégiques] (s'ils sont fournis) et les [processus].
      ${currentPolicy ? `
      2. Analyse de la Politique Existante :
         - Identifier les points forts à conserver
         - Repérer les aspects à améliorer
         - Déterminer les éléments manquants
         - Évaluer la cohérence avec les enjeux actuels
      ` : `
      2. Définition des Axes Stratégiques :
         - Proposer 3 à 5 Axes Stratégiques clairs et mobilisateurs
         - S'assurer qu'ils répondent directement aux enjeux
         - Intégrer naturellement les dimensions de performance
         - Éviter les titres génériques
      `}
      3. Rédaction de la Politique (Structure Impérative) :
      oTitre : ${currentPolicy ? 'Conserver ou améliorer le titre existant' : 'Proposer un titre engageant et représentatif'}
      oIntroduction Synthétique (4-5 lignes max) :
      Ancrer la politique dans les [enjeux] les plus critiques et 
      la [mission/vision] de l'entreprise.
      Présenter l'ambition générale et introduire l'idée que la 
      performance est globale et repose sur des axes stratégiques 
      clairs.
      oCorps de la Politique : Les Axes Stratégiques et Engagements
      Associés :
      Pour chaque Axe Stratégique :
      Énoncer clairement l'Axe Stratégique.
      Décrire en 2-4 phrases l'engagement principal de 
      l'entreprise pour cet axe, en le reliant 
      aux [enjeux] pertinents.
      Illustrer par quelques engagements concrets (2-3 
      points clés) qui montrent comment cet axe se traduit 
      en actions/principes.
      Mentionner explicitement que ces axes stratégiques 
      constituent le cadre pour la définition et le suivi d'objectifs 
      spécifiques au sein de nos [processus clés].
      oConclusion Engageante (quelques lignes) :
      Réaffirmer l'engagement indéfectible de la 
      Direction envers cette vision stratégique.
      Souligner que la réussite dépend de l'appropriation et de 
      la contribution active de chaque membre de l'entreprise.
      Insister sur la dynamique d'amélioration continue comme
      moteur de progrès permanent sur tous les axes.
      4. Ton et Style :
      oUtiliser un langage positif, dynamique, orienté action et 
      spécifique à l'entreprise (utiliser sa terminologie si possible).
      oÉviter absolument le jargon QHSE académique et les listes à 
      puces génériques.
      oLa politique doit respirer l'authenticité et la stratégie, pas la 
      conformité administrative.
      Format de Sortie Attendu :
      Un document texte unique et fluide représentant la politique d'engagement 
      stratégique de l'entreprise.
      Titre Engageant
      Introduction Synthétique
      Développement structuré par Axes Stratégiques (avec engagements 
      associés)
      Mention du lien avec les objectifs par processus
      Conclusion Mobilisatrice
      Note Fondamentale :
      L'objectif n'est PAS de produire un document listant des exigences QHSE 
      déguisées. L'objectif est de créer une véritable déclaration 
      stratégique qui intègre naturellement la performance sous toutes ses formes
      (Q, S, E, Énergie, etc.) comme leviers essentiels pour atteindre les ambitions 
      de l'entreprise face à ses enjeux. La politique doit être la colonne 
      vertébrale reliant la vision aux actions opérationnelles via les axes stratégiques
      et les objectifs processus.
      `;

      await progressToTarget(0.6, 1000); // Progresser vers 60% en 1 seconde
      const response = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [{ role: "user", content: prompt }],
        max_tokens: 3000,
        temperature: 0.7,
      });

      await progressToTarget(0.8, 1000); // Progresser vers 80% en 1 seconde
      if (response.choices.length > 0 && response.choices[0].message?.content) {
        const generatedText = response.choices[0].message.content.trim();
        setValue("context", generatedText);
        await progressToTarget(1, 1000); // Progresser vers 100% en 1 seconde
        // Attendre un court instant pour que l'utilisateur voie la progression complète
        await new Promise(resolve => setTimeout(resolve, 500));
        setLoadingIA(false);
      } else {
        Alert.alert("Erreur", "La génération de texte a échoué.");
        setLoadingIA(false);
      }
    } catch (err) {
      console.error("Erreur avec OpenAI :", err);
      Alert.alert("Erreur", "Impossible de générer la politique.");
      setLoadingIA(false);
    } finally {
      setProgress(0);
    }
  };

  // 🔄 Fonction pour mettre à jour ou créer la politique
  const handleUpdatePolicy = async (data: TypePolicy) => {
    try {
      if (!companySelected?.id) {
        Alert.alert("Erreur", "Aucune entreprise sélectionnée.");
        return;
      }

      // Vérifier si une politique existe déjà
      const { data: existingPolicy } = await supabase
        .from("policies")
        .select("id")
        .eq("company_id", companySelected.id)
        .single();

      if (existingPolicy) {
        // Mise à jour de la politique existante
        const { error } = await supabase
          .from("policies")
          .update({ context: data.context })
          .eq("company_id", companySelected.id);

        if (error) {
          Alert.alert("Échec", error.message || "Une erreur est survenue.");
          return;
        }
        Alert.alert("Succès", "La politique a été mise à jour.");
      } else {
        // Création d'une nouvelle politique
        const { error } = await supabase
          .from("policies")
          .insert({
            context: data.context,
            company_id: companySelected.id,
            uid_admin: user?.uid,
            created_at: new Date()
          });

        if (error) {
          Alert.alert("Échec", error.message || "Une erreur est survenue.");
          return;
        }
        Alert.alert("Succès", "La politique a été créée.");
      }

      router.back();
    } catch (err) {
      console.error("Erreur inattendue :", err);
      Alert.alert("Erreur", "Veuillez réessayer plus tard.");
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
    >
      <View style={styles.container}>
        <ScrollView style={styles.form}>
          {/* Texte d'introduction */}
          <View style={styles.introduction}>
            <View style={styles.introduction}>
              <Text style={styles.introText}>Votre politique d'entreprise</Text>
              <View style={styles.IAButtonContainer}>
                <Text style={styles.IAButtonText}>
                  Laissez-vous assister par l'IA pour générer votre politique
                </Text>
                <TouchableOpacity
                  style={[styles.IAButton, loadingIA && styles.IAButtonDisabled]}
                  onPress={generatePolicyWithAI}
                  disabled={loadingIA}
                >
                  {loadingIA ? (
                    <View style={styles.loadingContainer}>
                      <Progress.Circle
                        size={24}
                        progress={progress}
                        color="#f99527"
                        thickness={3}
                        showsText={false}
                      />
                      {progress < 1 && (
                        <Animated.Text style={[styles.loadingText, { opacity: fadeAnim }]}>
                          Génération en cours...
                        </Animated.Text>
                      )}
                    </View>
                  ) : (
                    <Image
                      style={styles.IAButtonImage}
                      source={require("@/assets/images/qse.png")}
                    />
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </View>

          {/* Champ Politique */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Politique</Text>
            <Controller
              control={control}
              name="context"
              rules={{ required: "Le politique est requis." }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  placeholderTextColor={"#6B7280"}
                  style={styles.inputXL}
                  multiline={true}
                  textAlignVertical="top"
                  placeholder="Politique actuelle"
                  onChangeText={onChange}
                  value={value}
                />
              )}
            />
            {errors.context && (
              <Text style={styles.errorText}>{errors.context.message}</Text>
            )}
          </View>

          {/* Bouton Enregistrer */}
          <TouchableOpacity
            style={styles.confirmButton}
            onPress={handleSubmit(handleUpdatePolicy)}
          >
            <Text style={styles.confirmButtonText}>
              {policiesData ? "Enregistrer" : "Créer"}
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    height: "100%",
    width: "100%",
    alignItems: "center",
    alignContent: "center",
    backgroundColor: "#FFFFFF",
  },
  headSection: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 30,
  },
  backButton: {
    width: 24,
    textAlign: "left",
  },
  titleWrapper: {
    display: "flex",
    flexDirection: "row",
    marginVertical: 15,
  },
  title: {
    color: "#262627",
    fontSize: 17,
    textAlign: "left",
  },
  form: {
    width: "100%",
    alignSelf: "center",
    paddingHorizontal: 30,
    marginTop: 20,
  },
  inputXL: {
    borderWidth: 2,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "100%", // S'adapte à la largeur du conteneur parent
    paddingHorizontal: 12,
    paddingVertical: 10, // Ajoute un espace en haut pour ne pas coller au bord
    height: 364, // Hauteur fixe
    color: "#000000",
    textAlignVertical: "top", // Aligne le texte en haut
  },
  input: {
    borderWidth: 2,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "auto",
    padding: 12,
    marginTop: 10,
    color: "#000000",
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginBottom: 5,
  },
  infoText: {
    fontSize: 14,
    marginVertical: 10,
    color: "#555",
  },
  inputContainer: {
    position: "relative",
    marginBottom: 20,
    width: "100%",
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
    textAlign: "left",
  },
  introduction: {
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-between",
    marginBottom: 20,
    width: "100%",
  },
  introText: {
    fontSize: 14,
    color: "#262627",
    marginBottom: 15,
  },
  IAButtonContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f8f9fa",
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#e9ecef",
  },
  IAButtonText: {
    flex: 1,
    fontSize: 14,
    color: "#262627",
    marginRight: 10,
  },
  IAButton: {
    padding: 8,
  },
  IAButtonDisabled: {
    opacity: 0.7,
  },
  IAButtonImage: {
    height: 24,
    width: 24,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  loadingText: {
    fontSize: 12,
    color: '#f99527',
  },
  confirmButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    marginBottom: 100,
    backgroundColor: "#f99527",
    marginTop: 10,
  },
  confirmButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  imagePicker: {
    width: "100%",
    padding: 10,
    backgroundColor: "#FFFFFF",
    borderColor: "#CCC",
    borderRadius: 8,
    borderWidth: 2,
    alignItems: "center",
    marginBottom: 10,
    marginTop: 10,
  },
  imagePickerText: {
    color: "#1F2937",
    fontWeight: "bold",
  },
  imagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  imageContainer: {
    width: "100%",
    justifyContent: "center",
  },
  uploadPlaceholder: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  uploadIcon: {
    borderWidth: 1,
    borderColor: "#f99527",
    borderRadius: 7,
    padding: 5,
  },
  uploadText: {
    textAlign: "center",
    fontWeight: "normal",
  },
  fullImagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  dateTimePickerContainer: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    textAlign: "left",
    marginVertical: 20,
  },
  dateTimePickerLabel: {
    color: "#063E76",
    fontSize: 14,
    fontWeight: "bold",
    backgroundColor: "#FFF",
  },
  datePicker: {
    width: "100%",
    marginTop: 10,
    marginBottom: 10,
  },
});
