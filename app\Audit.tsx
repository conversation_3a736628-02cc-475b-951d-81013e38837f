import AuditsList from './menu/itemsMenuDefault/audit';
import { useContext } from 'react';
import { AppContext } from '@/state/AppContext';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import Header from '@/components/common/Header';
import { useRouter } from 'expo-router';

export default function Audit() {
  const { companySelected } = useContext(AppContext);
  const router = useRouter();

  if (!companySelected) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#F99527" />
      </View>
    );
  }

  return (
    <View style={{ flex: 1 }}>
      <Header onPressFlesh={() => router.navigate("/")} />
      <AuditsList />
    </View>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
}); 