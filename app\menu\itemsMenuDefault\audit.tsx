/*
app/screens/audits/AuditsList.tsx

Liste des Audits.

Informations pertinentes :

- Les audits sont récupérés depuis la table `audits` de Supabase.
- Chaque audit appartient à une entreprise (colonne `company_id`).
- Les données incluent les relations suivantes :
  -> Table `audit_observations` pour récupérer les constats des audits (via `observation_id` et jointure avec la table `observationss` pour obtenir les constats).
- Les données récupérées incluent :
  - `id` : Identifiant unique de l'audit.
  - `name` : Nom de l'audit.
  - `date` : Date de l'audit.
  - `type` : Type de l'audit (`validated`, `in_progress`, ou `invalid`).
  - `field` : Champ de l'audit
  - `created_at` : Date de création (automatique)
- Si aucune audit n'est trouvée, un bouton pour créer un nouvel audit est affiché.
*/

import {
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Platform,
  ScrollView,
  TextInput,
} from "react-native";
import {
  ParamListBase,
  useFocusEffect,
  useNavigation,
} from "@react-navigation/native";
import { useContext, useEffect } from "react";
import { AppContext } from "@/state/AppContext";
import { useState } from "react";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { Octicons } from "@expo/vector-icons";
import { supabase } from "@/lib/supabase";
import React from "react";
import moment from "moment";
import { useRouter } from "expo-router";
import Header from "@/components/common/Header";
import FontAwesome from "@expo/vector-icons/FontAwesome";

// Gestion des types
const getAuditTypeStyle = (type: string) => {
  switch (type) {
    case "validated":
      return { text: "Finalisé", backgroundColor: "#2FC12B" };
    case "in_progress":
      return { text: "En cours", backgroundColor: "#F39415" };
    case "invalid":
      return { text: "En relecture", backgroundColor: "#1C3144" };
    default:
      return { text: "Inconnu", backgroundColor: "#D3D3D3" }; // Valeur par défaut
  }
};

const filterOptions = [
  { label: "En cours", value: "in_progress" },
  { label: "Finalisé", value: "validated" },
  { label: "Archivé", value: "archived" },
];

export default function AuditsList() {
  const router = useRouter();
  const [auditsList, setAuditsList] = useState<any>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredAuditsList, setFilteredAuditsList] = useState<any>([]);
  const { user, companySelected } = useContext(AppContext);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const [selectedFilters, setSelectedFilters] = useState<string[]>(["in_progress", "validated"]);

  const toggleFilter = (value: string) => {
    setSelectedFilters((prevSelected) =>
      prevSelected.includes(value)
        ? prevSelected.filter((item) => item !== value)
        : [...prevSelected, value]
    );
  };

  // Récupérer la liste des audits de l'entreprise
  useFocusEffect(
    React.useCallback(() => {
      const fetchAudits = async () => {
        try {
          if (!user || !user.uid) {
            console.error("Utilisateur non connecté.");
            return;
          }

          // Vérifiez que l'utilisateur a une entreprise associée
          const companyId = companySelected?.id;
          if (!companyId) {
            setAuditsList([]); // Aucun document
            setLoading(false); // Terminer le chargement
            return;
          }

          // Requête pour récupérer les audits avec les relations jointes
          const { data, error } = await supabase
            .from("audits")
            .select("id, name, type, date, field, conclusion, archived")
            .eq("company_id", companyId);

          if (error) {
            console.error(
              "Erreur lors de la récupération des audits :",
              error.message
            );
            return;
          }

          setAuditsList(data);
        } catch (err) {
          console.error("Erreur inattendue :", err);
        } finally {
          setLoading(false);
        }
      };

      fetchAudits();
    }, [user])
  );

  // Mettre à jour la liste filtrée en fonction de la recherche et des filtres
  useEffect(() => {
    const filteredList = auditsList.filter((item: any) => {
      // Filtre par recherche
      const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase());
      
      // Filtre par statut
      const matchesFilter = selectedFilters.length === 0 || 
        (selectedFilters.includes("in_progress") && item.type === "in_progress" && !item.archived) ||
        (selectedFilters.includes("validated") && item.type === "validated" && !item.archived) ||
        (selectedFilters.includes("archived") && item.archived === true);

      return matchesSearch && matchesFilter;
    });
    
    setFilteredAuditsList(filteredList);
  }, [searchQuery, auditsList, selectedFilters]);

  return (
    <>
      <ScrollView 
        style={{ flex: 1, width: "100%" }}
        contentContainerStyle={{
          alignItems: "center",
          flexGrow: 1
        }}
      >
        <View style={{ 
          width: "100%",
          maxWidth: 800,
          paddingHorizontal: 10,
        }}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={{ fontWeight: "bold" }}>Créer un nouvel audit :</Text>
            <TouchableOpacity
              onPress={() => router.navigate("/screens/audits/CreateAudit")}
            >
              <Octicons name="diff-added" size={25} color="#F99527" />
            </TouchableOpacity>
          </View>

          {/* Barre de recherche */}
          <View style={styles.searchContainer}>
            <View style={styles.searchInputWrapper}>
              <FontAwesome
                name="search"
                size={24}
                color="#3C3c4399"
                style={styles.searchIcon}
              />
              <TextInput
                style={styles.searchInput}
                placeholder="Rechercher par nom..."
                placeholderTextColor="#3C3c4399"
                value={searchQuery}
                onChangeText={(text) => setSearchQuery(text)}
              />
            </View>
          </View>

          {/* Filtres */}
          <View style={styles.filterContainer}>
            <Text style={styles.filterLabel}>Filtrer par statut</Text>
            <View style={styles.filterButtons}>
              {filterOptions.map((filter) => (
                <TouchableOpacity
                  key={filter.value}
                  style={[
                    styles.filterButton,
                    selectedFilters.includes(filter.value) && styles.filterButtonSelected,
                  ]}
                  onPress={() => toggleFilter(filter.value)}
                >
                  <Text
                    style={
                      selectedFilters.includes(filter.value)
                        ? styles.filterTextSelected
                        : styles.filterText
                    }
                  >
                    {filter.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Liste des Audits */}
          {loading ? (
            <Text>Chargement...</Text>
          ) : filteredAuditsList.length === 0 ? (
            <View style={styles.noDataContainer}>
              <Text style={styles.noDataText}>Aucun audit trouvé</Text>
            </View>
          ) : (
            <View style={{ width: "100%" }}>
              <FlatList
                data={filteredAuditsList}
                keyExtractor={(item) => item.id.toString()|| Math.random().toString()}
                renderItem={({ item }) => {
                  const { text, backgroundColor } = getAuditTypeStyle(item.type);

                  return (
                    <TouchableOpacity
                      onPress={() =>
                        navigation.navigate("screens/audits/AuditDetails", {
                          id: item.id,
                          name: item.name,
                          type: item.type,
                          field: item.field,
                          date: item.date,
                          conclusion: item.conclusion ?? "Non renseignée",
                          archived: item.archived,
                        })
                      }
                      style={styles.listItem}
                    >
                      {/* 1ère ligne d'éléments */}
                      <View style={styles.elementsTop}>
                        <Text style={styles.listName}>{item.name}</Text>
                        <Text style={styles.date}>
                          {moment(item.date).format("DD/MM/YYYY")}
                        </Text>
                      </View>
                      {/* 2ème ligne d'éléments */}
                      <View style={styles.elementsBottom}>
                        {item.archived ? (
                          <Text style={[styles.texts, { backgroundColor: "#1C3144" }]}>
                            Archivé
                          </Text>
                        ) : (
                          <Text style={[styles.texts, { backgroundColor }]}>
                            {text}
                          </Text>
                        )}
                        <Text>
                          <Octicons name="chevron-right" size={24} color="black" />
                        </Text>
                      </View>
                    </TouchableOpacity>
                  );
                }}
                contentContainerStyle={styles.listContainer}
              />
            </View>
          )}
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 10,
    marginTop: 14,
  },
  searchContainer: {
    width: "100%",
    marginBottom: 20,
    marginTop: 10,
    paddingHorizontal: 10,
  },
  searchInputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1C314414",
    borderRadius: 8,
    paddingHorizontal: 10,
    height: 60,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    padding: 10,
    fontSize: 16,
    color: "#525252",
  },
  filterContainer: {
    width: "100%",
    marginBottom: 20,
  },
  filterLabel: {
    fontSize: 14,
    color: "#525252",
    marginBottom: 10,
    fontWeight: "bold",
  },
  filterButtons: {
    display: "flex",
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 10,
  },
  filterButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "#F99527",
  },
  filterButtonSelected: {
    backgroundColor: "#F99527",
    borderColor: "#F99527",
  },
  filterText: {
    color: "#F99527",
    fontSize: 14,
  },
  filterTextSelected: {
    color: "white",
    fontSize: 14,
  },
  listContainer: {
    width: "100%",
    paddingVertical: 10,
  },
  listItem: {
    width: "100%",
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    display: "flex",
    justifyContent: "space-between",
    flexDirection: "column",
    gap: 10,
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  elementsTop: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  elementsBottom: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  listName: {
    width: "50%",
    fontSize: 14,
    color: "#525252",
    fontStyle: "normal",
    flexWrap: "wrap",
    fontWeight: "bold",
  },
  date: {
    fontSize: 14,
    color: "#525252",
    fontStyle: "normal",
    flexWrap: "wrap",
    fontWeight: "normal",
  },
  texts: {
    fontSize: 11,
    color: "#FFFFFF",
    padding: 8,
    borderRadius: 50,
    fontStyle: "normal",
    flexWrap: "wrap",
    fontWeight: "normal",
    textAlign: "center",
    minWidth: 100,
  },
  noDataContainer: {
    height: Platform.OS === "web" ? 150 : 100,
    display: "flex",
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  noDataText: {
    fontSize: 14,
    color: "#525252",
    fontStyle: "normal",
    flexWrap: "wrap",
    fontWeight: "normal",
  },
});
