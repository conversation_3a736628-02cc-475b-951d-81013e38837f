/*
app/objectif/updateEquipment.tsx

- Quand le formulaire est validé les données sont envoyées dans la table `equipments`

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `Admin` ont accès à cette page.
*/

import React, { useContext, useEffect, useState } from "react";
import {
  StyleSheet,
  Image,
  View,
  ScrollView,
  Alert,
  TouchableOpacity,
  Text,
  Platform,
  KeyboardAvoidingView,
  TextInput,
} from "react-native";
import { Controller, useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import TypeEquipment from "@/types/typeEquipment";
import { ParamListBase, useNavigation, useFocusEffect } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import "react-native-get-random-values";
import { v4 as uuidv4 } from "uuid";
import DateTimePicker from "@react-native-community/datetimepicker";
import { DateTimePickerAndroid } from "@react-native-community/datetimepicker";
import * as ImagePicker from "expo-image-picker";
import uploadEquipmentImageToStorage from "@/lib/uploadEquipmentImageStorage";
import uploadEquipmentDocumentToStorage from "@/lib/uploadEquipmentDocumentStorage";
import { Dropdown } from "react-native-element-dropdown";
import { Octicons } from "@expo/vector-icons";
import * as DocumentPicker from "expo-document-picker";
import { useLocalSearchParams, useRouter } from "expo-router";
import ContainedButton from "@/components/ui/buttons/ContainedButton";

export default function UpdateEquipment() {
  const { user, companySelected } = useContext(AppContext);
  const [companyUsers, setCompanyUsers] = useState<any>([]);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedType, setSelectedType] = useState<string>("");
  const [purchaseDate, setPurchaseDate] = useState(new Date());
  const [documentUri, setDocumentUri] = useState<string | null>(null);
  const [imageUri, setImageUri] = useState<string | null>(null);
  const params = useLocalSearchParams<any>();
  console.log("PARAMÈTRES REÇUS DE `detailsEquipment.tsx` : ", params);
  const router = useRouter();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
    control,
    reset,
    setValue,
  } = useForm<TypeEquipment>({
    defaultValues: {
      name: params.name || "",
      reference: params.reference || "",
      purchase_date: params.purchase_date
        ? new Date(params.purchase_date)
        : new Date(),
      price: params.price || 0,
      status: params.status || "",
      document: params.document || "",
      image: params.image || "",
    },
  });

  // Récupérer les informations de l'équipement
  const fetchEquipmentDetails = async () => {
    if (!params.uuid) return;

    const { data, error } = await supabase
      .from("equipments")
      .select("*, users(first_name, last_name)")
      .eq("uuid", params.uuid)
      .single();

    if (error) {
      console.error("Erreur lors de la récupération des informations:", error);
      return;
    }

    // Mettre à jour les valeurs du formulaire
    setValue("name", data.name);
    setValue("reference", data.reference);
    setValue("price", data.price);
    setValue("status", data.status);
    setValue("document", data.document);
    setValue("image", data.image);

    // Mettre à jour les dates
    if (data.purchase_date) {
      setPurchaseDate(new Date(data.purchase_date));
    }
    if (data.verification_date) {
      setSelectedDate(new Date(data.verification_date));
    }

    // Mettre à jour le type
    setSelectedType(data.type);
  };

  // Recharger les données à chaque fois qu'on arrive sur la page
  useFocusEffect(
    React.useCallback(() => {
      fetchEquipmentDetails();
    }, [])
  );

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern", "Supplier"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page.",
      );
      navigation.goBack();
    }
  }, [user]);

  // Statuts
  const equipmentStatus = [
    { value: "compliant", label: "Conforme" },
    { value: "to_be_checked", label: "À vérifier" },
    { value: "under_verification", label: "En cours de vérification" },
    { value: "out_of_service", label: "Hors service" },
  ];

  // Date (iOS)
  const handleDateChange = (event: any, date: any) => {
    if (date) {
      setSelectedDate(date); // Mettre à jour avec la date sélectionnée
    }
  };

  // Date (Android)
  const showDatePicker = () => {
    DateTimePickerAndroid.open({
      value: selectedDate,
      mode: "date",
      onChange: handleDateChange,
      display: "default",
    });
  };

  // Sélectionner une image
  const handlePickImage = async () => {
    const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (permissionResult.granted === false) {
      Toast.show({
        type: "error",
        text1: "Permissions refusées",
        text1Style: { color: "#1C3144" },
        text2: "Vous devez autoriser l'accès à vos photos.",
        text2Style: { color: "#1C3144" },
      });
      return;
    }

    const pickerResult = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [500, 500],
      quality: 1,
    });

    if (!pickerResult?.canceled && pickerResult.assets && pickerResult.assets.length > 0) {
      setImageUri(pickerResult.assets[0].uri);
    }
  };

  // Sélectionner un document
  const handlePickDocument = async () => {
    const permissionResult =
      await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (permissionResult.granted === false) {
      Toast.show({
        type: "error",
        text1: "Permissions refusées",
        text1Style: { color: "#1C3144" },
        text2: "Vous devez autoriser l'accès à vos photos.",
        text2Style: { color: "#1C3144" },
      });
      return;
    }

    const pickerResult = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ["images"],
      allowsEditing: true,
      aspect: [500, 500],
      quality: 1,
    });

    if (
      !pickerResult?.canceled &&
      pickerResult.assets &&
      pickerResult.assets.length > 0
    ) {
      setDocumentUri(pickerResult.assets[0].uri);
    }
  };

  // Validation du formulaire
  const handleUpdateEquipment = async (data: TypeEquipment) => {
    const updatedAt = new Date();

    try {
      let uploadedDocumentUrl = null;
      let uploadedImageUrl = null;

      // Upload document si il existe
      if (documentUri) {
        const uploadDocumentResult = await uploadEquipmentDocumentToStorage(
          documentUri,
          user?.uid || "",
        );
        uploadedDocumentUrl = uploadDocumentResult?.url || null;
      }

      // Upload image si elle existe
      if (imageUri) {
        const uploadImageResult = await uploadEquipmentImageToStorage(
          imageUri,
          user?.uid || "",
        );
        uploadedImageUrl = uploadImageResult?.url || null;
      }

      // Conversion du prix en nombre
      const price = parseFloat(data.price.toString().replace(',', '.'));

      const { error } = await supabase
        .from("equipments")
        .update({
          name: data.name,
          reference: data.reference,
          purchase_date: purchaseDate,
          price: price,
          status: data.status,
          document: uploadedDocumentUrl || data.document,
          image: uploadedImageUrl || data.image,
          updated_at: updatedAt,
        })
        .eq("uuid", params.uuid);

      if (error) {
        console.error("Erreur lors de la mise à jour de l'équipement :", error);
        Toast.show({
          type: "error",
          text1: "Erreur lors de la mise à jour de l'équipement",
          text1Style: { color: "#1C3144" },
        });
        return;
      }

      Toast.show({
        type: "success",
        text1: "Équipement modifié avec succès",
        text1Style: { color: "#1C3144" },
      });

      Alert.alert("Équipement modifié", "Équipement modifié avec succès.");

      router.back();
      reset();
    } catch (err) {
      console.error("Erreur inattendue :", err);
      Toast.show({
        type: "error",
        text1: "Une erreur est survenue",
        text1Style: { color: "#1C3144" },
      });
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 0}
    >
      <ScrollView
        style={styles.form}
        contentContainerStyle={styles.formContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.container}>
          {/* Formulaire */}
          <View style={styles.form}>
            {/* Nom */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Nom</Text>
              <Controller
                control={control}
                name="name"
                rules={{ required: "Le libellé est requis." }}
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    maxLength={50}
                    placeholderTextColor={"#6B7280"}
                    style={styles.input}
                    placeholder={params.name || "Nom de l'équipement"}
                    onChangeText={onChange}
                    value={value}
                  />
                )}
              />
            </View>

            {/* Référence */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Référence</Text>
              <Controller
                control={control}
                name="reference"
                rules={{ required: "La référence est requise." }}
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    maxLength={300}
                    placeholderTextColor={"#6B7280"}
                    style={styles.input}
                    placeholder="Référence de l'équipement"
                    onChangeText={onChange}
                    value={value}
                    multiline
                  />
                )}
              />
            </View>

            {/* Date Picker pour Android */}
            {Platform.OS === "android" && (
              <TouchableOpacity
                onPress={showDatePicker}
                style={styles.dateTimePickerContainer}
              >
                <Text style={styles.dateTimePickerLabel}>Date d'achat</Text>
                <Text style={styles.datePicker}>
                  {purchaseDate.toLocaleDateString()}
                </Text>
              </TouchableOpacity>
            )}

            {/* Date Picker pour iOS */}
            {Platform.OS === "ios" && (
              <View style={styles.dateTimePickerContainer}>
                <Text style={styles.label}>Date d'achat</Text>
                <DateTimePicker
                  value={purchaseDate}
                  mode="date"
                  display="default"
                  onChange={handleDateChange}
                  style={styles.datePicker}
                />
              </View>
            )}

            {/* Coût */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Coût</Text>
              <Controller
                control={control}
                name="price"
                rules={{ required: "Le prix est requis." }}
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    maxLength={10}
                    placeholderTextColor={"#6B7280"}
                    style={styles.input}
                    placeholder={params.price || "Coût de l'équipement"}
                    onChangeText={(text) => {
                      // Permettre uniquement les chiffres et la virgule
                      const formattedText = text.replace(/[^0-9,]/g, '');
                      // S'assurer qu'il n'y a qu'une seule virgule
                      const parts = formattedText.split(',');
                      if (parts.length > 2) return;
                      // Limiter à 2 décimales après la virgule
                      if (parts[1] && parts[1].length > 2) return;
                      onChange(formattedText);
                    }}
                    value={value?.toString()}
                    keyboardType="decimal-pad"
                  />
                )}
              />
            </View>

            {/* Statut */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Statut</Text>
              <Controller
                control={control}
                name="status"
                rules={{ required: "Le statut est requis." }}
                render={({ field: { onChange, value } }) => (
                  <Dropdown
                    style={dropdownStyles.dropdown}
                    placeholderStyle={dropdownStyles.placeholderStyle}
                    selectedTextStyle={dropdownStyles.selectedTextStyle}
                    data={equipmentStatus}
                    labelField="label"
                    valueField="value"
                    value={value}
                    onChange={(item) => onChange(item.value)}
                    placeholder="Sélectionner un statut"
                  />
                )}
              />
            </View>

            {/* Image */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Photo</Text>
              <TouchableOpacity
                style={styles.imagePicker}
                onPress={handlePickImage}
              >
                {imageUri || params.image ? (
                  <View style={styles.imagePreviewContainer}>
                    <Image 
                      source={{ uri: imageUri || params.image }} 
                      style={styles.imagePreview} 
                    />
                  </View>
                ) : (
                  <View style={styles.uploadPlaceholder}>
                    <Octicons
                      style={styles.uploadIcon}
                      name="upload"
                      size={24}
                      color="black"
                    />
                    <Text style={styles.uploadText}>Choisir une photo</Text>
                  </View>
                )}
              </TouchableOpacity>
            </View>

            {/* Document */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Document</Text>
              <TouchableOpacity
                style={styles.imagePicker}
                onPress={handlePickDocument}
              >
                {documentUri || params.document ? (
                  <View style={styles.documentPreview}>
                    <Octicons name="file" size={24} color="#f99527" />
                    <Text style={styles.documentText}>Document PDF</Text>
                  </View>
                ) : (
                  <View style={styles.uploadPlaceholder}>
                    <Octicons
                      style={styles.uploadIcon}
                      name="upload"
                      size={24}
                      color="black"
                    />
                    <Text style={styles.uploadText}>Choisir un document PDF</Text>
                  </View>
                )}
              </TouchableOpacity>
            </View>

            {/* Bouton de soumission */}
            <ContainedButton
              label="Modifier"
              backgroundColor="#F99527"
              onPress={async () => {
                await handleSubmit(handleUpdateEquipment)();
              }}
            />
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
      width: '100%',
    } : {}),
  },
  form: {
    padding: 20,
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
      width: '100%',
    } : {}),
  },
  formContent: {
    gap: 20,
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
      width: '100%',
    } : {}),
  },
  headSection: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 30,
  },
  imageGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "flex-start",
    gap: 10,
    marginBottom: 20,
  },
  backButton: {
    width: 24,
    textAlign: "left",
  },
  titleWrapper: {
    display: "flex",
    flexDirection: "row",
    marginVertical: 15,
  },
  title: {
    color: "#262627",
    fontSize: 17,
    textAlign: "left",
  },
  input: {
    borderWidth: 2,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "auto",
    padding: 12,
    marginTop: 10,
    color: "#000000",
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginBottom: 5,
  },
  infoText: {
    fontSize: 14,
    marginVertical: 10,
    color: "#555",
  },
  inputContainer: {
    marginBottom: 20,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
    textAlign: "left",
  },
  confirmButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    marginBottom: 100,
    backgroundColor: "#f99527",
    marginTop: 10,
  },
  confirmButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  imagePicker: {
    width: "100%",
    height: 200,
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 20,
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  imagePickerText: {
    color: "#1F2937",
    fontWeight: "bold",
  },
  imagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  imageContainer: {
    width: "100%",
    justifyContent: "center",
  },
  uploadPlaceholder: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  uploadIcon: {
    borderWidth: 1,
    borderColor: "#f99527",
    borderRadius: 7,
    padding: 5,
  },
  uploadText: {
    textAlign: "center",
    fontWeight: "normal",
  },
  fullImagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  dateTimePickerContainer: {
    width: "100%",
    marginBottom: 20,
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  dateTimePickerLabel: {
    color: "#063E76",
    fontSize: 14,
    fontWeight: "bold",
    backgroundColor: "#FFF",
  },
  datePicker: {
    width: "100%",
    marginTop: 10,
    marginBottom: 10,
  },
  documentPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 10,
  },
  documentText: {
    fontSize: 16,
    color: '#262627',
  },
  imagePreviewContainer: {
    width: '100%',
    alignItems: 'center',
  },
});

const dropdownStyles = StyleSheet.create({
  dropdown: {
    marginVertical: 16,
    height: 50,
    backgroundColor: "white",
    borderRadius: 12,
    padding: 12,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,

    elevation: 2,
  },
  icon: {
    marginRight: 5,
  },
  item: {
    padding: 17,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  textItem: {
    flex: 1,
    fontSize: 16,
  },
  placeholderStyle: {
    fontSize: 16,
  },
  selectedTextStyle: {
    fontSize: 16,
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 16,
  },
});
