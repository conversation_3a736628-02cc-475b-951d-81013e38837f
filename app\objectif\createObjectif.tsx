/*
app/objectif/createObjectif.tsx

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern` et `Extern` n'ont pas accès à cette page.
*/

import ContainedButton from "@/components/ui/buttons/ContainedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useRouter } from "expo-router";
import {
  StyleSheet,
  Image,
  View,
  ScrollView,
  Alert,
  Platform,
} from "react-native";

import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import CustomizedSelect from "@/components/ui/customizedSelect/customizedSelect";
import TypeProcess from "@/types/typeProcess";
import TypeObjectif from "@/types/typeObjectif";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";

const inputs = [
  {
    label: "Designation",
    name: "designation",
    placeholder: "Ecrire la designation",
  },
  {
    label: "Cible",
    name: "target",
    placeholder: "Ecrire la cible",
  },

  {
    label: "Indicateur de mesure",
    name: "measuring_indicator",
    placeholder: "Indicateur de mesure",
  },
  //
];

export default function CreateObjectifs() {
  const [loading, setLoading] = useState(false);
  const [imageUri, setImageUri] = useState<string | null>(null);
  const { user, companySelected } = useContext(AppContext);
  const [process, setProcess] = useState<TypeProcess[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  useEffect(() => {
    setIsLoading(true);
    const fetchUsersAdmin = async () => {
      const { data: dataGetted, error } = await supabase
        .from("process")
        .select()
        .eq("uid_admin", user?.uid);

      setProcess(dataGetted as any);

      setIsLoading(false);
    };
    fetchUsersAdmin();
  }, []);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors, touchedFields },
    control,
    reset,
    watch,
  } = useForm<TypeObjectif>({
    defaultValues: {
      designation: "",
      target: "",
      measuring_indicator: "",
      process_id: 0,
    },
  });

  const createObjectif = async (data: TypeObjectif) => {
    if (
      !data.designation &&
      !data.target &&
      !data.measuring_indicator &&
      !data.process_id &&
      !data.process.process_name
    ) {
      Toast.show({
        type: "error",
        text1: "Le nom et le prénom sont obligatoires",
        text1Style: { color: "#1C3144" },
      });
    }

    if (
      data.designation &&
      data.target &&
      data.measuring_indicator &&
      data.process_id
    ) {
      setLoading(true);

      const created_at = new Date();

      try {
        const { error } = await supabase.from("objectifs").insert({
          uid_user: user?.uid,
          target: data.target,
          designation: data.designation,
          measuring_indicator: data.measuring_indicator,
          process_id: data.process_id,
          created_at: created_at,
          level: 0,
          company_id: companySelected?.id,
        });

        if (error) {
          console.log("error__", error);
          Toast.show({
            type: "error",
            text1: "Erreur lors d'enregistrement d'objectif",

            text1Style: { color: "#1C3144" },
          });
          setLoading(false);
          return;
        }

        Toast.show({
          type: "success",
          text1: "L'objectif était bien crée",
          text1Style: { color: "#1C3144" },
        });

        router.back();

        reset();
        setLoading(false);
      } catch (err) {
        setImageUri("");
        setLoading(false);

        Toast.show({
          type: "error",
          text1: "Une erreur est survenue",
          text1Style: { color: "#1C3144" },
          // text2: "This is some something 👋",
        });
      }
    }
  };

  return (
    <View style={styles.mainContainer}>
      <ScrollView style={styles.container}>
        <View style={styles.containerContent}>
          <View style={styles.registerInfo}>
            <View style={styles.inputs}>
              {inputs.map((input, index) => {
                return (
                  <View key={index}>
                    <InputCustomized
                      label={input.label}
                      placeholder={input.placeholder}
                      name={input.name}
                      register={register}
                      control={control}
                      errors={errors}
                      setError={setError}
                    />
                  </View>
                );
              })}
              <CustomizedSelect
                name="process_id"
                label="Processus"
                register={register}
                control={control}
                errors={errors}
                setError={setError}
                data={
                  process.map((item) => {
                    return { label: item.process_name, value: item.id };
                  }) as any
                }
              />
            </View>
          </View>

          {imageUri && (
            <Image
              source={{ uri: imageUri }}
              style={styles.image}
            />
          )}

          <View style={styles.buttons}>
            <ContainedButton
              label="Créer"
              backgroundColor="#F99527"
              onPress={handleSubmit(createObjectif)}
              disabled={loading}
            />
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  containerContent: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    width: "100%",
    gap: 20,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
    marginTop: 20,
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
  image: {
    width: 150,
    height: 150,
    marginVertical: 20,
    borderRadius: 150,
    alignSelf: 'center',
  },
});
