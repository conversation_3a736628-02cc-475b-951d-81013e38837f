/*
app/screens/actions/UpdateAction.tsx

Formulaire pour modifier une action.

Informations pertinentes :

- Une action est reliée à l'entreprise de l'utilisateur connecté qui la crée.
- On récupère les utilisateurs de l'entreprise depuis la table de jointure `company_users`
- Quand le formulaire est validé les données sont envoyées dans :
  -> la table `actions`
  -> la table de jointure `actions_administrators`

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern` et `Extern` n'ont pas accès à cette page.
*/

import React, { useContext, useEffect, useState, useRef, useCallback } from "react";
import {
  Alert,
  StyleSheet,
  View,
  TextInput,
  Text,
  Platform,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { supabase } from "../../../lib/supabase.native";
import { Controller, useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { ParamListBase, useNavigation, useRoute, CommonActions } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AntDesign } from "@expo/vector-icons";
import { Dropdown } from "react-native-element-dropdown";
import DateTimePicker from "@react-native-community/datetimepicker";
import { DateTimePickerAndroid } from "@react-native-community/datetimepicker";
import { useRouter } from "expo-router";
import { useLocalSearchParams } from "expo-router";

// Choix du dropdown (actions)
const actionTypes = [
  { label: "Immédiate", value: "immediate" },
  { label: "Correctif", value: "corrective" },
];

// Choix du dropdown (actions)
const actionStatus = [
  { label: "Réalisée", value: "achieved" },
  { label: "Non réalisée", value: "not_achieved" },
];

// Type pour les utilisateurs
type User = {
  label: string;
  value: number;
};

export default function UpdateAction() {
  const params = useLocalSearchParams();
  const actionParam = Array.isArray(params.action) ? params.action[0] : params.action;
  const action = actionParam ? JSON.parse(actionParam) : null;
  const formValuesSet = useRef(false);

  console.log("🚀 Action reçue :", action);

  const [selectedDate, setSelectedDate] = useState(new Date());
  const [users, setUsers] = useState<User[]>([]);
  const router = useRouter();
  const { user, companySelected } = useContext(AppContext);
  const companyId = companySelected?.id;
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  
  const {
    handleSubmit,
    formState: { errors },
    control,
    reset,
    setValue,
  } = useForm({
    defaultValues: {
      name: "",
      date: new Date(),
      administrator: "",
      status: "",
      type: "",
    },
    mode: "onBlur",
    criteriaMode: "all",
  });

  // Récupérer les informations de l'utilisateur connecté
  useEffect(() => {
    if (!companyId) {
      console.error("❌ Aucun `companyId` fourni pour récupérer les utilisateurs.");
      return;
    }
    
    const fetchData = async () => {
      try {
        // Récupérer les utilisateurs de l'entreprise
        console.log("Company ID utilisé pour la requête :", companyId);
        
        // Récupérer les utilisateurs comme dans CreateAction
        const { data: usersData, error: usersError } = await supabase
          .from("users")
          .select()
          .eq("uid_admin", user?.uid);

        console.log("Données utilisateurs récupérées :", usersData);

        if (usersError) {
          console.error(
            "Erreur lors de la récupération des utilisateurs :",
            usersError.message
          );
          return;
        } else if (usersData) {
          // Formater les utilisateurs comme dans CreateAction
          const formattedUsers = usersData.map((item: any) => ({
            label: `${item.first_name} ${item.last_name}`,
            value: item.uid,
          }));

          console.log("Formatted users:", formattedUsers);
          setUsers(formattedUsers);
          
          // Set form values after users are loaded
          if (action && !formValuesSet.current) {
            console.log("Setting form values with action:", action);
            console.log("Administrator to find:", action.administrator);
            
            // Vérifier si l'administrateur est dans la liste des utilisateurs
            const adminExists = formattedUsers.some(u => String(u.value) === String(action.administrator));
            console.log("Administrator exists in users list:", adminExists);
            
            setValue("name", action.name || "");
            setValue("date", action.date ? new Date(action.date) : new Date());
            setValue("administrator", action.administrator || "");
            setValue("status", action.status || "");
            setValue("type", action.type || "");
            
            if (action.date) {
              setSelectedDate(new Date(action.date));
            }
            
            formValuesSet.current = true;
          }
        }
      } catch (err) {
        console.error("Erreur inattendue lors de la récupération :", err);
      }
    };

    fetchData();
  }, [companyId, action, setValue, user]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      if (Platform.OS === 'web') {
        console.error("Accès restreint - Redirection");
        navigation.goBack();
      } else {
        Alert.alert(
          "Accès restreint",
          "Vous n'avez pas les droits pour accéder à cette page."
        );
        navigation.goBack();
      }
    }
  }, [user, navigation]);

  // Date (iOS)
  const handleDateChange = (event: any, date: any) => {
    if (date) {
      setSelectedDate(date);
    }
  };

  // Date (Android)
  const showDatePicker = () => {
    DateTimePickerAndroid.open({
      value: selectedDate,
      mode: "date",
      onChange: handleDateChange,
      display: "default",
    });
  };

  // Mettre à jour l'action
  const handleUpdateAction = async (data: any) => {
    try {
      if (!action?.id) {
        Alert.alert("Erreur", "ID de l'action manquant");
        return;
      }

      console.log("Données soumises:", data);
      console.log("Administrator à mettre à jour:", data.administrator);

      // Préparation des données pour la mise à jour
      const actionData = {
        name: data.name,
        date: selectedDate,
        type: data.type,
        status: data.status,
      };

      // Mise à jour de l'action
      const { error: actionError } = await supabase
        .from("actions")
        .update(actionData)
        .eq("id", action.id);

      if (actionError) {
        if (Platform.OS === 'web') {
          console.error("Échec de la mise à jour: ", actionError.message || "Une erreur est survenue.");
          return;
        } else {
          Alert.alert(
            "Échec de la mise à jour",
            actionError.message || "Une erreur est survenue."
          );
          return;
        }
      }

      // Mise à jour de l'administrateur si nécessaire
      if (data.administrator) {
        // D'abord supprimer l'ancien administrateur
        const { error: deleteError } = await supabase
          .from("action_administrators")
          .delete()
          .eq("action_id", action.id);
          
        if (deleteError) {
          console.error("Erreur lors de la suppression de l'ancien responsable:", deleteError.message);
        }
        
        // Puis ajouter le nouveau
        console.log("Insertion du responsable d'action:", {
          action_id: action.id,
          user_id: data.administrator
        });
        
        const { error: adminError } = await supabase
          .from("action_administrators")
          .insert({
            action_id: action.id,
            user_id: data.administrator,
          });

        if (adminError) {
          console.error(
            "Erreur lors de la mise à jour de le responsable d'action :",
            adminError.message
          );
          if (Platform.OS === 'web') {
            console.error("Impossible de mettre à jour le responsable d'action.");
            return;
          } else {
            Alert.alert("Erreur", "Impossible de mettre à jour le responsable d'action.");
            return;
          }
        }
      } else {
        if (Platform.OS === 'web') {
          console.error("Veuillez sélectionner un responsable d'action.");
          return;
        } else {
          Alert.alert("Erreur", "Veuillez sélectionner un responsable d'action.");
          return;
        }
      }

      // Confirmation de la mise à jour
      if (Platform.OS === 'web') {
        // En web, on navigue directement sans alerte
        console.log("Redirection directe sur le web après mise à jour");
        reset();
        
        // Retourner à la page précédente avec un paramètre forceRefresh
        router.back();
        // Forcer le rechargement des données dans ActionDetails
        router.push({
          pathname: "/screens/actions/ActionDetails",
          params: { 
            action_id: action.id,
            forceRefresh: new Date().getTime()
          }
        });
      } else {
        // Sur mobile, on affiche l'alerte
        Alert.alert("Succès", "L'action a été mise à jour avec succès.", [
          {
            text: "OK",
            onPress: () => {
              reset();
              // Retourner à la page précédente avec un paramètre forceRefresh
              router.back();
              // Forcer le rechargement des données dans ActionDetails
              router.push({
                pathname: "/screens/actions/ActionDetails",
                params: { 
                  action_id: action.id,
                  forceRefresh: new Date().getTime()
                }
              });
            },
          },
        ]);
      }
    } catch (err) {
      if (Platform.OS === 'web') {
        console.error("Erreur inattendue :", err);
      } else {
        Alert.alert("Erreur inattendue", "Veuillez réessayer plus tard.");
        console.error("Erreur inattendue :", err);
      }
    }
  };

  return (
    <>
      <View style={styles.container}>
        <ScrollView style={styles.form}>
          {/* Champ Nom */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Libellé de l'action</Text>
            <Controller
              control={control}
              name="name"
              rules={{ required: "Le nom est requis." }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  maxLength={500}
                  placeholderTextColor={"#6B7280"}
                  style={styles.input}
                  placeholder="Libellé de l'action"
                  onChangeText={onChange}
                  value={value}
                  multiline={true}
                />
              )}
            />
            {errors.name && (
              <Text style={styles.errorText}>{String(errors.name.message)}</Text>
            )}
          </View>

          {/* Date */}
          {/* Date Picker pour Android */}
          {Platform.OS === "android" && (
            <TouchableOpacity
              onPress={showDatePicker}
              style={styles.dateTimePickerContainer}
            >
              <Text style={styles.dateTimePickerLabel}>Date</Text>
              <Text style={styles.datePicker}>
                {selectedDate.toLocaleDateString()}
              </Text>
            </TouchableOpacity>
          )}

          {/* Date Picker pour iOS */}
          {Platform.OS === "ios" && (
            <View style={styles.dateTimePickerContainer}>
              <Text style={styles.label}>Date</Text>
              <DateTimePicker
                value={selectedDate}
                mode="date"
                display="default"
                onChange={handleDateChange}
                style={styles.datePicker}
              />
            </View>
          )}

          {/* Date Picker pour Web */}
          {Platform.OS === "web" && (
            <View style={styles.dateTimePickerContainer}>
              <Text style={styles.label}>Date</Text>
              <input
                type="date"
                value={selectedDate.toISOString().split('T')[0]}
                onChange={(e) => {
                  const newDate = new Date(e.target.value);
                  setSelectedDate(newDate);
                }}
                style={{
                  marginTop: 10,
                  padding: 10,
                  borderWidth: 2,
                  borderColor: '#ccc',
                  borderRadius: 8,
                  width: '100%',
                }}
              />
            </View>
          )}

          {/* Champ Responsable/Administrateur */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Responsable de l'action</Text>
            <Controller
              control={control}
              name="administrator"
              rules={{ required: "Veuillez sélectionner un responsable d'action." }}
              render={({ field: { onChange, value } }) => {
                console.log("Rendering administrator dropdown with value:", value);
                console.log("Available users:", users.map(u => ({ label: u.label, value: u.value })));
                
                // Vérifier si la valeur existe dans la liste des utilisateurs
                const userExists = users.some(u => String(u.value) === String(value));
                console.log("User exists in dropdown options:", userExists);
                
                return (
                  <Dropdown
                    key={`admin-dropdown-${users.length}`}
                    style={dropdownStyles.dropdown}
                    placeholderStyle={dropdownStyles.placeholderStyle}
                    selectedTextStyle={dropdownStyles.selectedTextStyle}
                    data={users}
                    placeholder="Sélectionner un responsable d'action"
                    value={userExists ? value : null}
                    onChange={(item) => {
                      console.log("Selected new responsable d'action:", item.value);
                      onChange(item.value);
                    }}
                    labelField="label"
                    valueField="value"
                  />
                );
              }}
            />
            {errors.administrator && (
              <Text style={styles.errorText}>
                {String(errors.administrator.message)}
              </Text>
            )}
          </View>

          {/* Champ Statut (Réalisé/Non réalisé) */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Statut</Text>
            <Controller
              control={control}
              name="status"
              rules={{ required: "Le statut est requis." }}
              render={({ field: { onChange, value } }) => (
                <Dropdown
                  style={dropdownStyles.dropdown}
                  placeholderStyle={dropdownStyles.placeholderStyle}
                  selectedTextStyle={dropdownStyles.selectedTextStyle}
                  inputSearchStyle={dropdownStyles.inputSearchStyle}
                  iconStyle={dropdownStyles.iconStyle}
                  data={actionStatus}
                  search
                  maxHeight={300}
                  labelField="label"
                  valueField="value"
                  placeholder={"Choix du statut"}
                  searchPlaceholder="Rechercher un statut"
                  value={value}
                  onChange={(item) => {
                    onChange(item.value);
                  }}
                  renderLeftIcon={() => (
                    <AntDesign
                      style={dropdownStyles.icon}
                      color="black"
                      name="Safety"
                      size={20}
                    />
                  )}
                />
              )}
            />
            {errors.status && (
              <Text style={styles.errorText}>{String(errors.status.message)}</Text>
            )}
          </View>

          {/* Champ Type (Immédiate/Correctif) */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Types d'actions*</Text>
            <Controller
              control={control}
              name="type"
              rules={{ required: "Le type est requis." }}
              render={({ field: { onChange, value } }) => (
                <Dropdown
                  style={dropdownStyles.dropdown}
                  placeholderStyle={dropdownStyles.placeholderStyle}
                  selectedTextStyle={dropdownStyles.selectedTextStyle}
                  inputSearchStyle={dropdownStyles.inputSearchStyle}
                  iconStyle={dropdownStyles.iconStyle}
                  data={actionTypes}
                  search
                  maxHeight={300}
                  labelField="label"
                  valueField="value"
                  placeholder={"Choix du type"}
                  searchPlaceholder="Rechercher un type"
                  value={value}
                  onChange={(item) => {
                    onChange(item.value);
                  }}
                  renderLeftIcon={() => (
                    <AntDesign
                      style={dropdownStyles.icon}
                      color="black"
                      name="Safety"
                      size={20}
                    />
                  )}
                />
              )}
            />
            {errors.type && (
              <Text style={styles.errorText}>{String(errors.type.message)}</Text>
            )}
          </View>

          {/* Bouton de soumission */}
          <TouchableOpacity
            style={styles.confirmButton}
            onPress={() => {
              console.log("Clic sur le bouton de mise à jour.");
              handleSubmit(handleUpdateAction)();
            }}
          >
            <Text style={styles.confirmButtonText}>Mettre à jour</Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    height: "100%",
    width: "100%",
    alignItems: "center",
    alignContent: "center",
    backgroundColor: "#FFFFFF",
  },
  headSection: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 30,
  },
  backButton: {
    width: 24,
    textAlign: "left",
  },
  titleWrapper: {
    display: "flex",
    flexDirection: "row",
    marginVertical: 15,
  },
  title: {
    color: "#262627",
    fontSize: 17,
    textAlign: "left",
  },
  form: {
    width: "100%",
    alignSelf: "center",
    paddingHorizontal: Platform.OS === "web" ? 100 : 30,
    marginBottom: Platform.OS === "web" ? 50 : 250,
    marginTop: 20,
    maxWidth: Platform.OS === "web" ? 800 : "100%",
  },
  input: {
    borderWidth: 2,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "auto",
    padding: Platform.OS === "web" ? 15 : 12,
    marginTop: 10,
    color: "#000000",
    fontSize: Platform.OS === "web" ? 16 : 14,
  },
  errorText: {
    color: "red",
    fontSize: Platform.OS === "web" ? 14 : 12,
    marginBottom: 5,
  },
  infoText: {
    fontSize: 14,
    marginVertical: 10,
    color: "#555",
  },
  inputContainer: {
    position: "relative",
    marginBottom: Platform.OS === "web" ? 30 : 20,
    width: "100%",
  },
  label: {
    fontSize: Platform.OS === "web" ? 16 : 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
    textAlign: "left",
  },
  confirmButton: {
    maxHeight: Platform.OS === "web" ? 60 : 50,
    borderRadius: 7,
    width: "100%",
    padding: Platform.OS === "web" ? 15 : 12,
    marginBottom: 16,
    backgroundColor: "#f99527",
    marginTop: 10,
  },
  confirmButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: Platform.OS === "web" ? 18 : 14,
  },
  dateTimePickerContainer: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    textAlign: "left",
    marginVertical: Platform.OS === "web" ? 30 : 20,
  },
  dateTimePickerLabel: {
    color: "#063E76",
    fontSize: Platform.OS === "web" ? 16 : 14,
    fontWeight: "bold",
    backgroundColor: "#FFF",
  },
  datePicker: {
    width: "100%",
    marginTop: 10,
    marginBottom: 10,
  },
});

// Style du dropdown
const dropdownStyles = StyleSheet.create({
  dropdown: {
    marginVertical: Platform.OS === "web" ? 20 : 16,
    height: Platform.OS === "web" ? 60 : 50,
    backgroundColor: "white",
    borderRadius: 12,
    padding: Platform.OS === "web" ? 15 : 12,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
  },
  icon: {
    marginRight: 5,
  },
  item: {
    padding: Platform.OS === "web" ? 20 : 17,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  textItem: {
    flex: 1,
    fontSize: Platform.OS === "web" ? 16 : 14,
  },
  placeholderStyle: {
    fontSize: Platform.OS === "web" ? 16 : 14,
  },
  selectedTextStyle: {
    fontSize: Platform.OS === "web" ? 16 : 14,
  },
  iconStyle: {
    width: Platform.OS === "web" ? 24 : 20,
    height: Platform.OS === "web" ? 24 : 20,
  },
  inputSearchStyle: {
    height: Platform.OS === "web" ? 50 : 40,
    fontSize: Platform.OS === "web" ? 16 : 14,
  },
});
