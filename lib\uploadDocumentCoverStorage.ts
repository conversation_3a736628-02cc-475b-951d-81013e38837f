/*
lib/uploadDocumentCoverStorage.ts

Fonction pour envoyer les `PDF` des documents vers le Storage Supabase.
*/

import Toast from "react-native-toast-message";
import { supabase } from "./supabase.native";

export default async function uploadPDF(fileUri: string, userId: string) {
  try {
    const response = await fetch(fileUri);
    const blob = await response.blob();

    // Générer un nom de fichier unique (ex: `userId-timestamp.pdf`)
    const fileName = `${userId}_${Date.now()}_document.pdf`;

    const arrayBuffer = await new Response(blob).arrayBuffer();
    const { data, error } = await supabase.storage
      .from("images") // Stockage des PDF
      .upload(`documents/${fileName}`, arrayBuffer, {
        contentType: "application/pdf",
        upsert: false, // Ne pas écraser un fichier existant
      });

    if (error) {
      console.error("Upload error:", error);
      Toast.show({
        type: "error",
        text1: "Une erreur est survenue lors de l'upload",
        text1Style: { color: "#1C3144" },
      });
      return null;
    }

    // Générer une URL publique pour accéder au PDF
    return {
      url: supabase.storage.from("images").getPublicUrl(`documents/${fileName}`)
        .data.publicUrl,
    };
  } catch (err) {
    console.error("Erreur lors de l'upload :", err);
    Toast.show({
      type: "error",
      text1: "Une erreur est survenue lors de l'upload",
      text1Style: { color: "#1C3144" },
    });
    return null;
  }
}
