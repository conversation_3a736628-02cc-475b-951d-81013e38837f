import React from "react";
import ContainedButton from "@/components/ui/buttons/ContainedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useLocalSearchParams, useRouter } from "expo-router";
import {
  StyleSheet,
  Image,
  View,
  ScrollView,
  Alert,
  TouchableOpacity,
  Text,
} from "react-native";

import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import InputFileCustomized from "@/components/ui/inputs/InputFileCustomized";
import * as ImagePicker from "expo-image-picker";
import uploadPhoto from "@/lib/uploadPictureStorage";
import Header from "@/components/common/Header";

type TypeDoc = {
  name: string;
  image: string;
};

export default function CreateDoc() {
  const [loading, setLoading] = useState(false);
  const [imageUri, setImageUri] = useState<string | null>(null);
  const { user, companySelected } = useContext(AppContext);

  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  const { product_id, action_id } = useLocalSearchParams();

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors, touchedFields },
    control,
    reset,
  } = useForm<TypeDoc>({
    defaultValues: {
      name: "",
      image: "",
    },
  });

  const inputs = [
    {
      label: "Nom du document",
      name: "name",
      placeholder: "Nom du document",
    },
    {
      label: "Photo",
      name: "image",
      placeholder: "Choisir la photo",
      type: "img",
    },
  ];

  const createDoc = async (data: TypeDoc) => {
    if (!data.name && !imageUri) {
      Toast.show({
        type: "error",
        text1: "Veuillez remplir tout les champs requise",
        text1Style: { color: "#1C3144" },
      });
    }

    setLoading(true);

    const created_at = new Date();

    const responseImg = await uploadPhoto(
      imageUri as string,
      user?.uid as string,
      "images",
      "docs",
      "image/jpeg"
    );

    try {
      const { error } = await supabase.from("docsProduct").insert({
        uid_user: user?.uid,
        created_at: created_at,
        company_id: companySelected?.id,
        name: data.name,
        image: responseImg?.url,
        product_id: product_id,
        type: "action",
        action_id: action_id,
      });

      if (error) {
        console.log("error__", error);
        Toast.show({
          type: "error",
          text1: "Erreur lors d'enregistrement du produit",
          text1Style: { color: "#1C3144" },
        });
        setLoading(false);
        return;
      }

      Toast.show({
        type: "success",
        text1: "L'doc était bien crée",
        text1Style: { color: "#1C3144" },
      });

      router.push({
        pathname: "/product/action/detailsAction",
        params: { action_id: action_id, product_id: product_id },
      });

      reset();
      setLoading(false);
    } catch (err) {
      setImageUri("");
      setLoading(false);

      Toast.show({
        type: "error",
        text1: "Une erreur est survenue",
        text1Style: { color: "#1C3144" },
        // text2: "This is some something 👋",
      });
    }
  };

  const pickImage = async () => {
    const permissionResult =
      await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (!permissionResult.granted) {
      Toast.show({
        type: "error",
        text1: "Permissions refusées",
        text1Style: { color: "#1C3144" },
        text2: "Vous devez autoriser l'accès à vos photos.",
      });
      return;
    }

    const pickerResult = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ["images"],
      allowsEditing: true,
      quality: 1,
    });

    if (!pickerResult.canceled && pickerResult.assets.length > 0) {
      const selectedImageUri = pickerResult.assets[0].uri;
      setImageUri(selectedImageUri); // ✅ Mise à jour du state (même si asynchrone)
    }
  };

  return (
    <>
      <Header
        title="Créer un document"
        onPressFlesh={() => router.back()}
      />
      <ScrollView>
        <View style={styles.containerContent}>
          <View style={styles.registerInfo}>
            <View style={styles.inputs}>
              {inputs.map((input, index) => {
                if (input?.type === "img") {
                  return (
                    <>
                      <>
                        {!imageUri && (
                          <InputFileCustomized
                            label={"Photo"}
                            placeholder={"Choisir la photo"}
                            onPress={pickImage}
                          />
                        )}
                        {imageUri ? (
                          <View style={{ display: "flex" }}>
                            <Text style={styles.label}>Photo</Text>
                            <TouchableOpacity onPress={pickImage}>
                              <Image
                                source={{ uri: imageUri }}
                                style={{
                                  width: "100%",
                                  height: 200,
                                  marginTop: 10,
                                  alignSelf: "center",
                                }}
                              />
                            </TouchableOpacity>
                          </View>
                        ) : null}
                      </>
                    </>
                  );
                } else {
                  return (
                    <View key={index}>
                      <InputCustomized
                        label={input.label}
                        placeholder={input.placeholder}
                        register={register}
                        name={input.name}
                        control={control}
                        errors={errors}
                        setError={setError}
                      />
                    </View>
                  );
                }
              })}
            </View>
          </View>

          <View style={styles.buttons}>
            <ContainedButton
              label="Créer"
              backgroundColor="#F99527"
              onPress={handleSubmit(createDoc)}
              disabled={loading}
            />
          </View>
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  containerContent: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    // padding: 20,
    gap: 20,
    marginVertical: 20,
    width: "90%",
    alignSelf: "center",
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    // backgroundColor: "yellow",
    width: "100%",
    gap: 20,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  buttonPassword: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  titleContainer: {
    flexDirection: "row",
    gap: 8,
  },
  imgIsLoadingPage: {
    height: 75,
    width: 130,
    alignSelf: "center",
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
  input: {
    backgroundColor: "white",
    borderColor: "none",
    height: 40,
    padding: 10,
    borderRadius: 4,
  },
  label: {
    fontSize: 14,
    color: "#262627",
    marginVertical: 1,
    fontWeight: "bold",
  },
});
