/*
app/menu/itemsMenuDefault/event.tsx

Informations pertinentes :

- On récupère les événements de la table `events`
- On récupère les constats de l'entreprise de la table `observations` qui ont un type :
  -> `non_compliant`
  -> `sensitive_point`
*/

import { StyleSheet, View, Text, FlatList, TouchableOpacity } from "react-native";
import { useFocusEffect, useRouter } from "expo-router";
import { useCallback, useContext, useState } from "react";
import { ContainerImgText } from "@/components/common/containerImgText";
import PaperInfoImgEvent from "@/components/common/paperInfoImgEvent";
import { supabase } from "@/lib/supabase";
import { AppContext } from "@/state/AppContext";
import TypeEvent from "@/types/typeEvent2";
import Header from "@/components/common/Header";
import { getEventTypeLabel } from "@/utils/eventUtils";
import { Octicons } from "@expo/vector-icons";

export default function Event() {
  const router = useRouter();
  const { user, companySelected } = useContext(AppContext);
  const [events, setEvents] = useState<TypeEvent[] | null>(null);
  const defaultImage = require("@/assets/images/imgDefaultEvent.png");

  const fetchUsersAdmin = async () => {
    const companyId = companySelected?.id;

    // Récupérer les événements
    const { data: eventsData, error: eventsError } = await supabase
      .from("events")
      .select("id, wording, causes, date, type, file, created_at, description")
      .eq("company_id", companyId)
      .order("created_at", { ascending: false });

    if (eventsError) {
      console.error(
        "Erreur lors de la récupération des événements :",
        eventsError.message
      );
      return;
    }

    // Récupérer les actions associées aux événements
    const eventIds = eventsData.map((event) => event.id); // Liste des ID des événements
    const { data: actionsData, error: actionsError } = await supabase
      .from("event_actions")
      .select("id, event_id, type") // Ajoute d'autres champs si nécessaire
      .in("event_id", eventIds); // Filtre par événements

    if (actionsError) {
      console.error(
        "Erreur lors de la récupération des actions :",
        actionsError.message
      );
      return;
    }

    // Regrouper les actions par événement
    const actionsByEvent = eventIds.reduce((acc, eventId) => {
      acc[eventId] = {
        immediateActions: actionsData.filter(
          (action) => action.event_id === eventId && action.type === "immediate"
        ),
        correctiveActions: actionsData.filter(
          (action) =>
            action.event_id === eventId && action.type === "corrective"
        ),
      };
      return acc;
    }, {});

    // Ajouter les actions à chaque événement
    const enrichedEvents = eventsData.map((event) => ({
      ...event,
      immediateActions: actionsByEvent[event.id]?.immediateActions || [],
      correctiveActions: actionsByEvent[event.id]?.correctiveActions || [],
    }));

    setEvents(enrichedEvents);
  };

  /*
  // const fetchCompanyEventsAndObservations = async () => {
  //   // Récupérer l'entreprise de l'utilisateur connecté
  //   const companyId = companySelected?.id;

  //   // Récupérer les événements
  //   const { data: events, error: eventsError } = await supabase
  //     .from("events")
  //     .select("id, wording, causes, date, type, file, created_at")
  //     .eq("company_id", companyId)
  //     .order("created_at", { ascending: false }); // ✅ Tri du plus récent au plus ancien


  //   if (eventsError) {
  //     console.error(
  //       "Erreur lors de la récupération des événements :",
  //       eventsError.message
  //     );
  //   }

  //   // Récupérer les constats de type "non-conformité" (`non_compliant`) et "Point sensible" (`sensitive_point`)
  //   const { data: observations, error: observationsError } = await supabase
  //     .from("observations")
  //     .select("id, name, requirements, created_at, type")
  //     .eq("company_id", companyId)
  //     .in("type", ["non_compliant", "sensitive_point"]);

  //   if (observationsError) {
  //     console.error(
  //       "Erreur lors de la récupération des constats :",
  //       observationsError.message
  //     );
  //   }

  //   // Fusionner les résultats en un seul tableau
  //   const combinedData = [
  //     ...(events ?? []).map((event) => ({
  //       id: event.id,
  //       title: event.wording,
  //       description: event.causes,
  //       date: event.date,
  //       type: "event",
  //       file: event.file

  //     })),
  //     ...(observations ?? []).map((observation) => ({
  //       id: observation.id,
  //       title: observation.name,
  //       description: observation.requirements,
  //       date: observation.created_at,
  //       type: observation.type, // Garde le type de l'observation
  //     })),
  //   ];

  //   // Trier les données par date décroissante
  //   combinedData.sort(
  //     (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
  //   );

  //   setEvents(combinedData);
  //   console.log("🔍 Events récupérés :", events);
  //   console.log("🔍 IDs des Events:", events?.map((e) => e.id));
  // };
  */

  /*
  const fetchCompanyEvents = async () => {
    // Récupérer l'entreprise de l'utilisateur connecté
    const { data: userCompany, error: userCompanyError } = await supabase
      .from("company_users") // Ou "users" selon ta structure
      .select("company_id")
      .eq("uid_user", user?.uid)
      .single(); // On suppose qu'un utilisateur a une seule entreprise

    if (userCompanyError || !userCompany) {
      console.error(
        "Erreur lors de la récupération de l'entreprise :",
        userCompanyError?.message
      );
      return;
    }

    // Récupérer tous les événements de l'entreprise
    const { data: events, error: eventsError } = await supabase
      .from("events")
      .select("*, companies(*), process(*), action_events(*)")
      .eq("company_id", userCompany.company_id); // Filtrer par entreprise

    if (eventsError) {
      console.error(
        "Erreur lors de la récupération des événements :",
        eventsError.message
      );
      return;
    }

    setEvents(events ?? []); // Mise à jour des événements
  };
  */

  useFocusEffect(
    useCallback(() => {
      fetchUsersAdmin();
      // fetchCompanyEventsAndObservations();
    }, [])
  );

  return (
    <View style={{ flex: 1 }}>
      <View style={{ width: "100%" }}>
        <Header onPressFlesh={() => router.navigate("/")} />
        <View style={styles.container}>
          <View
            style={{ flexDirection: "row", justifyContent: "center", gap: 20 }}
          >
            <ContainerImgText
              title={"Mes événements"}
              onPress={() => router.navigate("/event/eventSpecific")}
            />
            <ContainerImgText
              title={"Les événements"}
              onPress={() => router.navigate("/event/eventsCompany")}
            />
          </View>

          <View style={styles.header}>
            <Text style={{ fontWeight: "bold" }}>Derniers événements QSE</Text>
            <TouchableOpacity
              onPress={() => router.push("/event/createEvent")}
            >
              <Octicons name="diff-added" size={25} color="#F99527" />
            </TouchableOpacity>
          </View>
        </View>
      </View>
      <View style={{ flex: 1, gap: 10, paddingHorizontal: 10, maxWidth: 800, width: "100%", alignSelf: "center" }}>
        <FlatList
          data={events}
          keyExtractor={(item) => `event-${item.id}`}
          renderItem={({ item }) => (
            <View style={styles.itemContainer}>
              <PaperInfoImgEvent
                title={
                  item.wording.length > 30
                    ? item.wording.slice(0, 30) + "..."
                    : item.wording
                }
                text1={getEventTypeLabel(item.type)}
                text2={
                  item.description
                    ? item.description.length > 95
                      ? item.description.slice(0, 95) + "..."
                      : item.description
                    : "Pas de description"
                }
                circle1={item.immediateActions.length > 0} // S'il y a des actions immédiates
                circle2={item.causes !== "" && item.causes !== null}
                circle3={item.correctiveActions.length > 0} // S'il y a des actions correctives
                imgSrc={item?.file?.length ? item.file[0] : ""}
                onPressPen={() =>
                  router.push({
                    pathname: "/event/updateEvent",
                    params: {
                      date: item.date,
                      wording: item.wording,
                      causes: item.causes,
                      idEvent: item.id,
                      description: item.description,
                      typeEvent: item.type,
                    },
                  })
                }
                onPressEye={() =>
                  router.push({
                    pathname: "/event/detailsEvent",
                    params: {
                      date: item.date,
                      wording: item.wording,
                      causes: item.causes,
                      idEvent: item.id,
                      description: item.description,
                      typeEvent: item.type,
                    },
                  })
                }
              />
            </View>
          )}
          contentContainerStyle={{ paddingBottom: 800 }}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={<Text>Aucun événement disponible</Text>}
          ItemSeparatorComponent={() => <View style={styles.separator} />}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    gap: 10,
    paddingHorizontal: 10,
    maxWidth: 800,
    width: "100%",
    alignSelf: "center",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 10,
    marginTop: 14,
  },
  listContainer: {
    paddingBottom: 300, // Ajuste la hauteur pour éviter que le dernier élément soit trop collé
  },
  itemContainer: {
    marginBottom: 10, // Espacement entre les éléments
  },
  separator: {
    height: 10, // Ajoute un espacement entre les éléments
  },
  emptyText: {
    textAlign: "center",
    marginTop: 20,
    fontSize: 16,
    color: "gray",
  },
});
