import ContainedButton from "@/components/ui/buttons/ContainedButton";
import OutlinedButton from "@/components/ui/buttons/OutlinedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useRouter, useLocalSearchParams, useFocusEffect } from "expo-router";
import {
  StyleSheet,
  Image,
  View,
  Text,
  Linking,
  Alert,
  TextInput,
  ScrollView,
  TouchableOpacity,
  Platform,
} from "react-native";

import { useCallback, useContext, useEffect, useState } from "react";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Paper from "@/components/common/paper";
import Header from "@/components/common/Header";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";

const items = [
  {
    label: "Processus concerné",
    name: "process_id",
    placeholder: "Processus concerné",
  },
  {
    label: "Événement redouté",
    name: "event",
    placeholder: "Événement redouté",
  },
  {
    label: "Possibilité que cela arrive ?",
    name: "possibility",
    placeholder: "Possibilité que cela arrive ?",
  },
  {
    label: "Conséquence potentielle ?",
    name: "consequence",
    placeholder: "Conséquence potentielle ?",
  },
  {
    label: "Description du dispositif de prévention",
    name: "description",
    placeholder: "Description",
  },
  {
    label: "Niveau de maîtrise de risque ?",
    name: "level",
    placeholder: "Niveau de maîtrise de risque ?",
  },
];

const possibilities = [
  { value: 0, label: "Quasi impossible" },
  { value: 1, label: "Possible" },
  { value: 2, label: "Fortes chances" },
  { value: 3, label: "Quasi certain !" },
];

const consequences = [
  { value: 0, label: "Impact critique" },
  { value: 1, label: "Impact important" },
  { value: 2, label: "Impact modéré" },
  { value: 0, label: "Impact mineur" },
];

const levels = [
  { value: 0, label: "Non maîtrisée" },
  { value: 1, label: "Partiel" },
  { value: 2, label: "Maîtrisée" },
];

export default function detailsObjectif() {

  const { user } = useContext(AppContext);

  const router = useRouter();

  const [data, setData] = useState();

  const { event, id, score } = useLocalSearchParams();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  

  const getLabel = (name: string, value: number) => {
    if (name === "possibility") {
      return possibilities.find((item) => item.value === value)?.label || "";
    }
    if (name === "consequence") {
      return consequences.find((item) => item.value === value)?.label || "";
    }
    if (name === "level") {
      return levels.find((item) => item.value === value)?.label || "";
    }
    return value; // Retourne la valeur brute pour les autres champs
  };

  const fetchData = async () => {
    const { data: dataGetted, error } = await supabase
      .from("risks")
      .select("*, process(*), companies(*)")
      .eq("id", id);

    if (!error && dataGetted?.length > 0) {
      const risk = dataGetted[0];

      const formattedData = items.map(({ name, label }) => {
        let value = risk[name]; // Valeur brute

        if (name === "process_id") {
          value = risk.process?.process_name || "Non défini"; // Récupère le nom du processus
        } else {
          value = getLabel(name, value);
        }

        return { label, value };
      });

      setData(formattedData as any);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [])
  );

   // 🔥 Fonction de suppression d'un risque
   const handleDeleteRisk = async () => {
    const confirmDelete =
      Platform.OS === "web"
        ? window.confirm("Êtes-vous sûr de vouloir supprimer ce risque ?")
        : await new Promise((resolve) =>
            Alert.alert(
              "Confirmation",
              "Êtes-vous sûr de vouloir supprimer ce risque ?",
              [
                { text: "Annuler", style: "cancel", onPress: () => resolve(false) },
                { text: "Oui, supprimer", onPress: () => resolve(true) },
              ]
            )
          );

    if (!confirmDelete) return;

    try {
      const { error } = await supabase
        .from("risks")
        .delete()
        .eq("id", id);

      if (error) throw error;
      navigation.goBack();
      alert("Le risque a été supprimé avec succès.");
    } catch (error) {
      console.error("❌ Erreur lors de la suppression :", error);
      alert("Une erreur est survenue lors de la suppression.");
    }
  };


  
  return (
    <View style={styles.mainContainer}>
      <Header
        title={"Risque concerné"}
        onPressIcon={() =>
          router.push({
            pathname: "/risk/updateRisk",
            params: { riskId: id, event, id, score },
          })
        }
      />

      <ScrollView style={styles.container}>
        <View style={styles.containerContent}>
          <Paper title={event as string} score={score as any} data={data} />
          {/* 🔥 Bouton de suppression visible uniquement pour les Admins */}
          {user?.status === "Admin" && (
            <View style={styles.deleteButtonContainer}>
              <TouchableOpacity style={styles.deleteButton} onPress={handleDeleteRisk}>
                <Text style={styles.deleteButtonText}>Supprimer ce risque</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  containerContent: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  deleteButtonContainer: {
    width: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 30,
  },
  deleteButton: {
    width: 200,
    backgroundColor: "#ff4f42",
    padding: 10,
    borderRadius: 8,
    alignItems: "center",
  },
  deleteButtonText: {
    color: "#FFFFFF",
    fontWeight: "normal",
  },
});
