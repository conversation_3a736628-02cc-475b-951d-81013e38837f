/*
app/screens/actions/CreateAction.tsx

Formulaire pour créer une action.

Informations pertinentes :

- Une action est reliée à l'entreprise de l'utilisateur connecté qui la crée.
- On récupère les utilisateurs de l'entreprise depuis la table de jointure `company_users`
- On récupère les processus de l'entreprise depuis la table `process`
- On récupère les événements de l'entreprise depuis la table `events`
- Quand le formulaire est validé les données sont envoyées dans :
  -> la table `actions`
  -> les tables de jointures `actions_administrators`, `action_events`, `action_processes`


+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern` et `Extern` n'ont pas accès à cette page.
*/

import React, { useContext, useEffect, useState } from "react";
import {
  Alert,
  StyleSheet,
  View,
  TextInput,
  Text,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import { supabase } from "../../../lib/supabase.native";
import { Controller, useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AntDesign } from "@expo/vector-icons";
import { Dropdown } from "react-native-element-dropdown";
import DateTimePicker from "@react-native-community/datetimepicker";
import { DateTimePickerAndroid } from "@react-native-community/datetimepicker";
import HeaderV2 from "@/components/common/HeaderV2.native";
import { useRouter } from "expo-router";
import { useLocalSearchParams } from "expo-router";

type UserOption = {
  label: string;
  value: string;
};

// Choix du dropdown (actions)
const actionTypes = [
  { label: "Immédiate", value: "immediate" },
  { label: "Correctif", value: "corrective" },
];

// Choix du dropdown (actions)
const actionStatus = [
  { label: "Réalisée", value: "achieved" },
  { label: "Non réalisée", value: "not_achieved" },
];

export default function CreateAction() {
  const [process, setProcess] = useState([]);
  const [loadingProcess, setLoadingProcess] = useState(false);
  const [event, setEvent] = useState([]);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [users, setUsers] = useState([]);
  const router = useRouter();
  const { user, companySelected } = useContext(AppContext);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const companyId = companySelected?.id;
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const { context, productId, productName, actionType } = useLocalSearchParams();

  // Choix du champ d'action
  const actionChamp = [
    { label: "Interne", value: "interne" },
    { label: "Externe", value: "externe" },
  ];

  const {
    handleSubmit,
    formState: { errors },
    control,
    reset,
  } = useForm({
    defaultValues: {
      name: "",
      date: "",
      administrator: "",
      process: "",
      event: null,
      status: "not_achieved",
      type: actionType || "preventive",
      action_champ: "",
      uid_company: "",
    },
    mode: "onBlur",
    criteriaMode: "all",
  });

  // Récupérer les informations de l'utilisateur connecté
  useEffect(() => {
    if (!companyId) {
      console.error(
        "❌ Aucun `companyId` fourni pour récupérer les utilisateurs."
      );
      return;
    }

    const fetchData = async () => {
      try {
        // Récupérer les utilisateurs de l'entreprise
        console.log("Company ID utilisé pour la requête :", companyId);
        if (!companyId) {
          console.error(
            "❌ Aucun `companyId` fourni pour récupérer les utilisateurs."
          );
          return;
        }

        const { data: usersData, error: usersError } = await supabase
          .from("users")
          .select()
          .eq("uid_admin", user?.uid);

        console.log("Données utilisateurs récupérées :", usersData);

        if (usersError) {
          console.error(
            "Erreur lors de la récupération des utilisateurs :",
            usersError.message
          );
          return;
        } else {
          const formattedUsers: any = usersData.map((item: any) => ({
            label: `${item.first_name} ${item.last_name}`,
            value: item.uid,
          }));

          setUsers(formattedUsers);
        }

        // const formattedUsers: any = usersData.map((item: any) => ({
        //   label: `${item.users.first_name} ${item.users.last_name}`,
        //   value: item.user_id,
        // }));

        // setUsers(formattedUsers);

        // Récupérer les processus de l'entreprise
        const { data: processData, error: processError } = await supabase
          .from("process")
          .select("id, process_name")
          .eq("company_id", companyId);

        if (processError) {
          console.error(
            "Erreur lors de la récupération des processus :",
            processError.message
          );
        } else {
          const formattedProcess: any = processData.map((process: any) => ({
            label: process.process_name,
            value: process.id,
          }));
          setProcess(formattedProcess);
        }

        console.log("Entreprise infos : ", companyId);
        // Récupérer les événements de l'entreprise
        const { data: eventData, error: eventError } = await supabase
          .from("events")
          .select("id, wording")
          .eq("company_id", companyId);

        if (eventError) {
          console.error(
            "Erreur lors de la récupération des événements :",
            eventError.message
          );
        } else {
          const formattedEvents: any = eventData.map((event: any) => ({
            label: event.wording,
            value: event.id,
          }));

          console.log(formattedEvents);

          setEvent(formattedEvents);
        }
      } catch (err) {
        console.error("Erreur inattendue lors de la récupération :", err);
      }
    };

    fetchData();
  }, [user]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  // Date (iOS)
  const handleDateChange = (event: any, date: any) => {
    if (date) {
      setSelectedDate(date); // Mettre à jour avec la date sélectionnée
    }
  };

  // Date (Android)
  const showDatePicker = () => {
    DateTimePickerAndroid.open({
      value: selectedDate,
      mode: "date",
      onChange: handleDateChange,
      display: "default",
    });
  };

  // Valider le formulaire
  const handleSubmitCreateAction = async (data: any) => {
    try {
      setIsSubmitting(true);

      if (!data.name || !data.administrator || !data.process || !data.status || !data.type) {
        Alert.alert("Erreur", "Veuillez remplir tous les champs obligatoires.");
        setIsSubmitting(false);
        return;
      }

      // Préparation des données pour la table actions
      const actionData = {
        name: data.name,
        date: selectedDate,
        type: data.type,
        status: data.status,
        company_id: companyId,
        created_at: new Date(),
      };

      // Étape 1 : Insertion dans la table actions
      const { data: createdAction, error: actionError } = await supabase
        .from("actions")
        .insert(actionData)
        .select("id")
        .single();

      if (actionError) {
        Alert.alert("Échec de la création", actionError.message || "Une erreur est survenue.");
        return;
      }

      const actionId = createdAction.id;

      // Étape 2 : Insertion dans la table action_administrators
      if (data.administrator) {
        const administratorId = typeof data.administrator === 'object' ? data.administrator.value : data.administrator;
        
        const { error: adminError } = await supabase
          .from("action_administrators")
          .insert({
            action_id: actionId,
            user_id: administratorId,
          });

        if (adminError) {
          console.error("Erreur lors de l'ajout de l'administrateur :", adminError.message);
          Alert.alert("Erreur", "Impossible d'ajouter l'administrateur.");
          return;
        }
      }

      // Étape 3 : Insertion dans la table action_processes
      if (data.process) {
        const processId = typeof data.process === 'object' ? data.process.value : data.process;
        
        const { error: processError } = await supabase
          .from("action_processes")
          .insert({
            action_id: actionId,
            process_id: processId,
          });

        if (processError) {
          console.error("Erreur lors de l'ajout du processus :", processError.message);
          Alert.alert("Erreur", "Impossible d'ajouter le processus.");
          return;
        }
      }

      // Étape 4 : Insertion dans la table action_events
      if (data.event) {
        const { error: eventError } = await supabase
          .from("action_events")
          .insert({
            action_id: actionId,
            event_id: data.event,
          });

        if (eventError) {
          console.error(
            "Erreur lors de l'ajout de l'événement :",
            eventError.message
          );
          Alert.alert("Erreur", "Impossible d'ajouter l'événement.");
          return;
        }
      }
      // else {
      //   console.error("Aucun événement sélectionné !");
      //   Alert.alert("Erreur", "Veuillez sélectionner un événement.");
      //   return;
      // }

      // Étape 5 : Insertion dans event_actions pour lier l'action à l'événement
      if (data.event) {
        const { error: eventActionError } = await supabase
          .from("event_actions")
          .insert({
            action_id: actionId, // Action créée précédemment
            event_id: data.event, // ID de l'événement sélectionné
            process_id: data.process, // ID du processus sélectionné
            uid_user: data.administrator, // Responsable de l'action
            company_id: companySelected?.id, // Entreprise sélectionnée
            wording_action: data.name, // Libellé de l'action
            date: selectedDate, // Date choisie par l'utilisateur
            type: data.type, // Type d'action ("immediate" ou "corrective")
            status: data.status, // Statut ("achieved" ou "not_achieved")
          });

        if (eventActionError) {
          console.error(
            "❌ Erreur lors de l'ajout de l'événement dans event_actions :",
            eventActionError.message
          );
          Alert.alert(
            "Erreur",
            "Impossible d'ajouter l'événement à event_actions."
          );
          return;
        }
      }
      console.log("🔍 Données envoyées à event_actions :", {
        action_id: actionId,
        event_id: data.event,
        process_id: data.process,
        uid_user: data.administrator,
        company_id: companySelected?.id,
        wording_action: data.name,
        date: selectedDate,
        type: data.type,
        status: data.status,
      });

      // Étape 6 : Si c'est une action liée à un produit, créer la liaison
      if (context === 'product' && productId) {
        const { error: productActionError } = await supabase
          .from("action_products")
          .insert({
            action_id: actionId,
            product_id: productId,
            created_at: new Date(),
          });

        if (productActionError) {
          console.error("Erreur lors de la liaison avec le produit :", productActionError.message);
          Alert.alert("Erreur", "Impossible de lier l'action au produit.");
          return;
        }
      }

      // Confirmation de la création
      Alert.alert("Succès", "L'action a été créée avec succès.", [
        {
          text: "OK",
          onPress: () => {
            reset(); // Réinitialisation du formulaire
            router.back(); // Retour à la liste des actions
          },
        },
      ]);
    } catch (err) {
      Alert.alert("Erreur inattendue", "Veuillez réessayer plus tard.");
      console.error("Erreur inattendue :", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <View style={styles.mainContainer}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardView}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <ScrollView 
            style={styles.container}
            contentContainerStyle={styles.scrollContent}
          >
            <View style={styles.formContainer}>
              <Text style={styles.title}>
                {context === 'product' ? `Nouvelle action préventive pour ${productName}` : "Nouvelle action"}
              </Text>
              {/* Nom de l'action */}
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Nom de l'action</Text>
                <Controller
                  control={control}
                  name="name"
                  rules={{ required: "Le nom est requis" }}
                  render={({ field: { onChange, value } }) => (
                    <TextInput
                      style={styles.input}
                      onChangeText={onChange}
                      value={value}
                      placeholder="Entrez le nom de l'action"
                    />
                  )}
                />
                {errors.name && (
                  <Text style={styles.errorText}>{errors.name.message}</Text>
                )}
              </View>

              {/* Date */}
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Date d'échéance</Text>
                <TouchableOpacity
                  style={styles.datePickerButton}
                  onPress={showDatePicker}
                >
                  <Text>
                    {selectedDate.toLocaleDateString("fr-FR", {
                      day: "2-digit",
                      month: "2-digit",
                      year: "numeric",
                    })}
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Responsable */}
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Responsable</Text>
                <Controller
                  control={control}
                  name="administrator"
                  rules={{ required: "Le responsable est requis" }}
                  render={({ field: { onChange, value } }) => (
                    <Dropdown
                      style={styles.dropdown}
                      data={users}
                      labelField="label"
                      valueField="value"
                      placeholder="Sélectionnez un responsable"
                      value={value}
                      onChange={(item: UserOption) => onChange(item.value)}
                    />
                  )}
                />
                {errors.administrator && (
                  <Text style={styles.errorText}>{errors.administrator.message}</Text>
                )}
              </View>

              {/* Processus */}
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Processus</Text>
                <Controller
                  control={control}
                  name="process"
                  rules={{ required: "Le processus est requis" }}
                  render={({ field: { onChange, value } }) => (
                    <Dropdown
                      style={styles.dropdown}
                      data={process}
                      labelField="label"
                      valueField="value"
                      placeholder="Sélectionnez un processus"
                      value={value}
                      onChange={onChange}
                    />
                  )}
                />
                {errors.process && (
                  <Text style={styles.errorText}>{errors.process.message}</Text>
                )}
              </View>

              {/* Champ d'action */}
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Champ d'action</Text>
                <Controller
                  control={control}
                  name="action_champ"
                  rules={{ required: "Le champ d'action est requis" }}
                  render={({ field: { onChange, value } }) => (
                    <Dropdown
                      style={styles.dropdown}
                      data={actionChamp}
                      labelField="label"
                      valueField="value"
                      placeholder="Sélectionnez un champ d'action"
                      value={value}
                      onChange={onChange}
                    />
                  )}
                />
                {errors.action_champ && (
                  <Text style={styles.errorText}>
                    {errors.action_champ.message}
                  </Text>
                )}
              </View>

              {/* Bouton de soumission */}
              <TouchableOpacity
                style={styles.submitButton}
                onPress={handleSubmit(handleSubmitCreateAction)}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <ActivityIndicator color="#fff" />
                ) : (
                  <Text style={styles.submitButtonText}>Créer l'action</Text>
                )}
              </TouchableOpacity>
            </View>
          </ScrollView>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  keyboardView: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
  },
  formContainer: {
    flex: 1,
  },
  disabledButton: { backgroundColor: "#ccc" },

  headSection: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 30,
  },
  backButton: {
    width: 24,
    textAlign: "left",
  },
  titleWrapper: {
    display: "flex",
    flexDirection: "row",
    marginVertical: 15,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  form: {
    width: "100%",
    alignSelf: "center",
    paddingHorizontal: 30,
    marginBottom: 250,
    marginTop: 20,
  },
  input: {
    borderWidth: 2,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "auto",
    padding: 12,
    marginTop: 10,
    color: "#000000",
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginBottom: 5,
  },
  infoText: {
    fontSize: 14,
    marginVertical: 10,
    color: "#555",
  },
  inputContainer: {
    position: "relative",
    marginBottom: 20,
    width: "100%",
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
    textAlign: "left",
  },
  confirmButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    marginBottom: 16,
    backgroundColor: "#f99527",
    marginTop: 10,
  },
  confirmButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  imagePicker: {
    width: "100%",
    padding: 10,
    backgroundColor: "#FFFFFF",
    borderColor: "#CCC",
    borderRadius: 8,
    borderWidth: 2,
    alignItems: "center",
    marginBottom: 10,
    marginTop: 10,
  },
  imagePickerText: {
    color: "#1F2937",
    fontWeight: "bold",
  },
  imagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  imageContainer: {
    width: "100%",
    justifyContent: "center",
  },
  uploadPlaceholder: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  uploadIcon: {
    borderWidth: 1,
    borderColor: "#f99527",
    borderRadius: 7,
    padding: 5,
  },
  uploadText: {
    textAlign: "center",
    fontWeight: "normal",
  },
  fullImagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  dateTimePickerContainer: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    textAlign: "left",
    marginVertical: 20,
  },
  dateTimePickerLabel: {
    color: "#063E76",
    fontSize: 14,
    fontWeight: "bold",
    backgroundColor: "#FFF",
  },
  datePicker: {
    width: "100%",
    marginTop: 10,
    marginBottom: 10,
  },
  submitButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    marginBottom: 16,
    backgroundColor: "#f99527",
    marginTop: 10,
  },
  submitButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  datePickerButton: {
    borderWidth: 2,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "100%",
    padding: 12,
    marginTop: 10,
  },
  dropdown: {
    marginVertical: 16,
    height: 50,
    backgroundColor: "white",
    borderRadius: 12,
    padding: 12,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
  },
});
