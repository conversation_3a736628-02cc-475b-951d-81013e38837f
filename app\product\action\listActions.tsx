/*
app/menu/itemsMenuCockpit/product.tsx

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `Supplier` n'ont pas accès à cette page.
*/

import React from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  Platform,
} from "react-native";
import { useFocusEffect, useLocalSearchParams, useRouter } from "expo-router";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { useCallback, useContext, useEffect, useState } from "react";
import { Spinner } from "@/components/ui/spinner";
import { supabase } from "@/lib/supabase";
import { AppContext } from "@/state/AppContext";
import TypeProduct from "@/types/typeProduct";
import PaperInfo from "@/components/common/paperInfo";
import { Octicons } from "@expo/vector-icons";
import FilterPage from "@/components/common/filterPage";
import Header from "@/components/common/Header";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import PaperInfoImgEvent from "@/components/common/paperInfoImgEvent";
import PaperInfoImg from "@/components/common/paperInfoImg";
import TypeActionEvent from "@/types/typeActionEvent";

export default function Maintenance() {
  const router = useRouter();
  const [data, setData] = useState<TypeProduct[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { user, companySelected } = useContext(AppContext);
  const [refreshKey, setRefreshKey] = useState(0);
  const [productActions, setProductActions] = useState<TypeActionEvent[]>([]);

  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const [filters, setFilters] = useState({
    statutEnService: false,
    statutHorsService: false,
  });

  const { product_id } = useLocalSearchParams();

  const fetchActions = async () => {
    const { data: dataGetted, error } = await supabase
      .from("actions")
      .select(`
        *,
        action_administrators!inner(users:users(*)),
        action_processes!inner(process:process(*)),
        action_products!inner(product_id)
      `)
      .eq("action_products.product_id", product_id)
      .eq("type", "preventive")
      .order('created_at', { ascending: false });

    if (error) {
      console.error("Error fetching actions:", error);
      return;
    }

    if (dataGetted && dataGetted.length > 0) {
      console.log("Actions data:", dataGetted);
      const transformedData = dataGetted.map(action => ({
        id: action.id,
        name: action.name,
        type: action.type,
        status: action.status,
        date: action.date,
        resultAction: action.resultAction,
        administrator: action.action_administrators[0]?.users,
        process: action.action_processes[0]?.process,
        product_id: action.action_products[0]?.product_id
      }));
      setProductActions(transformedData as any);
    } else {
      console.log("No actions found for this product");
      setProductActions([]);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchActions();
    }, [])
  );

  useEffect(() => {
    if (!companySelected?.id) {
      console.error("❌ Aucun company_id défini, requête annulée !");
      return;
    }

    fetchActions();

    // ✅ Écoute les changements en temps réel et rafraîchit la liste
    const channel = supabase
      .channel("products-changes")
      .on(
        "postgres_changes",
        { event: "*", schema: "public", table: "products" },
        (payload: any) => {
          console.log("📌 Changement détecté dans products :", payload);
          setRefreshKey((prev) => prev + 1); // 🔥 Force un re-render en incrémentant `refreshKey`
        }
      )
      .subscribe(async (status: boolean) => {
        if (status === true) {
          console.log("✅ Abonné aux changements de la table `products` !");
        }
      });

    return () => {
      // Nettoyer l'écouteur lorsqu'on quitte la page
      supabase.removeChannel(channel);
    };
  }, [companySelected, refreshKey]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["User", "Supplier"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  function capitalizeFirstLetter(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
  }

  return (
    <>
      <ScrollView style={styles.container}>
        <View style={{ gap: 10, marginBottom: 10 }}>
          <View style={styles.header}>
            <Text style={{ fontWeight: "bold" }}>
              Créer une action préventive
            </Text>
            <TouchableOpacity
              onPress={() =>
                router.push({
                  pathname: "/product/action/createAction",
                  params: {
                    product_id: product_id,
                  },
                })
              }
            >
              <Octicons name="diff-added" size={25} color="#F99527" />
            </TouchableOpacity>
          </View>

          <View style={styles.users}>
            {isLoading ? (
              <Spinner size="small" color="#1C3144" />
            ) : productActions.length > 0 ? (
              productActions.map((item: any) => (
                <PaperInfo
                  key={item.id}
                  title={item.name}
                  text1={`Responsable: ${
                    item?.administrator?.first_name + " " + item?.administrator?.last_name
                  }`}
                  text2={`Processus: ${item.process?.process_name || "Non défini"}`}
                  text3={`Statut: ${item.status === "achieved" ? "Réalisée" : "Non réalisée"}`}
                  date={new Date(item.date)}
                  badgeShow={false}
                  level={item.status === "achieved" ? 2 : 1}
                  onPress={() =>
                    router.push({
                      pathname: "/screens/actions/ActionDetails",
                      params: {
                        action: JSON.stringify({
                          id: item.id,
                          name: item.name,
                          type: item.type,
                          status: item.status,
                          date: item.date,
                          resultAction: item.resultAction,
                          administrator: {
                            first_name: item.administrator?.first_name,
                            last_name: item.administrator?.last_name,
                            uid: item.administrator?.uid
                          },
                          process: {
                            id: item.process?.id,
                            process_name: item.process?.process_name
                          }
                        })
                      }
                    })
                  }
                />
              ))
            ) : (
              <Text style={styles.noActionsText}>Vous n'avez pas encore créé d'actions</Text>
            )}
          </View>
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "column",
    width: "100%",
    gap: 10,
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
      paddingHorizontal: 20,
    } : {}),
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 10,
    marginTop: 14,
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
      width: '100%',
    } : {}),
  },
  users: {
    paddingHorizontal: 10,
    gap: 10,
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
      width: '100%',
    } : {}),
  },
  noActionsText: {
    textAlign: "center",
    color: "#666",
    marginTop: 20,
    fontStyle: "italic"
  }
});
