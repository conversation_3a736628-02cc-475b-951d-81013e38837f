/*
app/info/policy.tsx

Affichage du la politique de l'entreprise.
*/

import {
  StyleSheet,
  ScrollView,
  View,
  TextInput,
} from "react-native";
import { useCallback, useContext, useEffect, useState } from "react";
import { AppContext } from "@/state/AppContext";
import { FontAwesome, Octicons } from "@expo/vector-icons";
import { useFocusEffect, useRouter } from "expo-router";
import { supabase } from "@/lib/supabase";
import PoliciesInfoSection from "@/components/infoSection/policy";
import TypePolicy from "@/types/typePolicies";
import AntDesign from '@expo/vector-icons/AntDesign';

export default function Policies() {
  const router = useRouter();
  const [policys, setPolicies] = useState<TypePolicy[] | null>(null);
  const { user, companySelected } = useContext(AppContext);
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredDataList, setFilteredDataList] = useState<any>([]);

  // Récupérer le policye
  useEffect(() => {
    const filteredList = policys?.filter((item) =>
      `${item.context}`.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredDataList(filteredList);
  }, [searchQuery, policys]);

  // Récupérer la politique
  const fetchData = async () => {
    console.log("Company ID : ", companySelected);
    const { data, error } = await supabase
      .from("policies")
      .select()
      .eq("company_id", companySelected?.id);

    if (!error) {
      setPolicies(data || null);
    }
  };

  // Récupérer le policye
  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [])
  );

  return (
    <ScrollView>
      {/* Barre de recherche */}
      {/* <View style={styles.searchContainer}>
        <View style={styles.searchInputWrapper}>
          <FontAwesome
            name="search"
            size={24}
            color="#3C3c4399"
            style={styles.searchIcon}
          />
          <TextInput
            style={styles.searchInput}
            placeholder="Rechercher par nom..."
            placeholderTextColor="#3C3c4399"
            value={searchQuery}
            onChangeText={(text) => setSearchQuery(text)}
          />
        </View>
      </View> */}

      <PoliciesInfoSection
        icon={
          !policys || policys.length === 0 ? ( // Vérifie s'il n'y a pas encore de policye
            <Octicons
              name="diff-added"
              size={24}
              color="#F99527"
              onPress={() => router.navigate("/info/createPolicy")}
            />
          ) : (
            <AntDesign
              name="sync"
              size={24}
              color="#F99527"
              onPress={() => router.navigate("/info/updatePolicy")}
            />
          )
        }
        data={filteredDataList as any}
      />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  searchContainer: {
    width: "100%",
    marginBottom: 20,
    marginTop: 10,
    paddingHorizontal: 10,
  },
  searchInputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1C314414",
    borderRadius: 8,
    paddingHorizontal: 10,
    height: 60,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    padding: 10,
    fontSize: 16,
    color: "#000000",
  },
});
