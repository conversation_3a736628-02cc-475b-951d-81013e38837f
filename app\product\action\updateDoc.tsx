import ContainedButton from "@/components/ui/buttons/ContainedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useLocalSearchParams, useRouter } from "expo-router";
import {
  StyleSheet,
  Image,
  View,
  ScrollView,
  Alert,
  TouchableOpacity,
  Text,
} from "react-native";

import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import CustomizedSelect from "@/components/ui/customizedSelect/customizedSelect";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import InputFileCustomized from "@/components/ui/inputs/InputFileCustomized";
import * as ImagePicker from "expo-image-picker";
import uploadPhoto from "@/lib/uploadPictureStorage";
import Header from "@/components/common/Header";

const optionsStatus = [
  { label: "En service", value: true },
  { label: "Hors service", value: false },
];

type TypeDoc = {
  name: string;
  created_at: string;
  image: string;
  id: number;
};

export default function UpdateDoc() {
  const [loading, setLoading] = useState(false);
  const [imageUri, setImageUri] = useState<string | null>(null);
  const { user } = useContext(AppContext);

  // const [product, setDoc] = useState<TypeProdut>();

  const router = useRouter();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const { doc_id, name, image, product_id } = useLocalSearchParams();

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors, touchedFields },
    control,
    reset,
    watch,
  } = useForm<TypeDoc>({
    defaultValues: {
      name: name as string,
      image: image as string,
    },
  });

  const inputs = [
    {
      label: "Nom",
      name: "name",
      placeholder: "Nom du produit",
    },
    {
      label: "Photo",
      name: "image",
      placeholder: "Choisir la photo",
      type: "img",
    },
  ];

  const updateDoc = async (data: TypeDoc) => {
    if (!data.name && !data.image && !imageUri) {
      Toast.show({
        type: "error",
        text1: "Veuillez remplir tout les champs requise",
        text1Style: { color: "#1C3144" },
      });
    }

    setLoading(true);

    let responseImg;

    if (imageUri) {
      responseImg = await uploadPhoto(
        imageUri as string,
        user?.uid as string,
        "images",
        "docs",
        "image/jpeg"
      );
    }

    try {
      const { error } = await supabase
        .from("docsProduct")
        .update({
          name: data.name,
          image: responseImg?.url ? responseImg?.url : image,
        })
        .eq("id", doc_id);

      if (error) {
        console.log("error__", error);
        Toast.show({
          type: "error",
          text1: "Erreur lors d'enregistrement du produit",
          text1Style: { color: "#1C3144" },
        });
        setLoading(false);
        return;
      }

      Toast.show({
        type: "success",
        text1: "L'product était bien crée",
        text1Style: { color: "#1C3144" },
      });

      router.back();

      reset();
      setLoading(false);
    } catch (err) {
      setImageUri("");
      setLoading(false);

      Toast.show({
        type: "error",
        text1: "Une erreur est survenue",
        text1Style: { color: "#1C3144" },
        // text2: "This is some something 👋",
      });
    }
  };

  const pickImage = async () => {
    const permissionResult =
      await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (!permissionResult.granted) {
      Toast.show({
        type: "error",
        text1: "Permissions refusées",
        text1Style: { color: "#1C3144" },
        text2: "Vous devez autoriser l'accès à vos photos.",
      });
      return;
    }

    const pickerResult = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ["images"],
      allowsEditing: true,
      quality: 1,
    });

    if (!pickerResult.canceled && pickerResult.assets.length > 0) {
      const selectedImageUri = pickerResult.assets[0].uri;
      setImageUri(selectedImageUri); // ✅ Mise à jour du state (même si asynchrone)

      // 📌 Déclencher immédiatement la mise à jour du profil avec l'image sélectionnée
    }
  };

  return (
    <ScrollView>
      <Header
        onPressFlesh={() =>
          router.push({
            pathname: "/product/doc/detailsDoc",
            params: {
              doc_id: doc_id,
              product_id: product_id,
            },
          })
        }
        title={name as string}
      />
      <View style={styles.containerContent}>
        <View style={styles.registerInfo}>
          <View style={styles.inputs}>
            {inputs.map((input, index) => {
              if (input?.type === "img") {
                return (
                  <>
                    <>
                      {!imageUri && !image && (
                        <InputFileCustomized
                          label={"Photo"}
                          placeholder={"Choisir la photo"}
                          onPress={pickImage}
                        />
                      )}
                      {imageUri || image ? (
                        <View style={{ display: "flex" }}>
                          <Text style={styles.label}>Photo</Text>
                          <TouchableOpacity onPress={pickImage}>
                            <Image
                              source={{ uri: imageUri || (image as string) }}
                              style={{
                                width: "100%",
                                height: 200,
                                marginTop: 10,
                                alignSelf: "center",
                              }}
                            />
                          </TouchableOpacity>
                        </View>
                      ) : null}
                    </>
                  </>
                );
              } else {
                return (
                  <View key={index}>
                    <InputCustomized
                      label={input.label}
                      placeholder={input.placeholder}
                      register={register}
                      name={input.name}
                      control={control}
                      errors={errors}
                      setError={setError}
                    />
                  </View>
                );
              }
            })}
          </View>
        </View>

        <View style={styles.buttons}>
          <ContainedButton
            label="Modifier"
            backgroundColor="#F99527"
            onPress={handleSubmit(updateDoc)}
            disabled={loading}
          />
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  containerContent: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    // padding: 20,
    gap: 20,
    marginVertical: 20,
    width: "90%",
    alignSelf: "center",
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    // backgroundColor: "yellow",
    width: "100%",
    gap: 20,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  buttonPassword: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  titleContainer: {
    flexDirection: "row",
    gap: 8,
  },
  imgIsLoadingPage: {
    height: 75,
    width: 130,
    alignSelf: "center",
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
  input: {
    backgroundColor: "white",
    borderColor: "none",
    height: 40,
    padding: 10,
    borderRadius: 4,
  },
  label: {
    fontSize: 14,
    color: "#262627",
    marginVertical: 1,
    fontWeight: "bold",
  },
});
