import React from "react";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
} from "react-native";
import { useRouter, useLocalSearchParams, useFocusEffect } from "expo-router";
import { useCallback, useContext, useEffect, useState } from "react";
import { AppContext } from "@/state/AppContext";
import PaperInfo from "@/components/common/paperInfo";
import { supabase } from "@/lib/supabase";
import Header from "@/components/common/Header";
import TypeEvent from "@/types/typeEvent2";
import { getEventTypeLabel } from "@/utils/eventUtils";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";

export default function ListEvents() {
  const { user, companySelected } = useContext(AppContext);
  const [events, setEvents] = useState<TypeEvent[]>([]);
  const [refreshKey, setRefreshKey] = useState(0);
  const router = useRouter();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const { product_id } = useLocalSearchParams();

  const fetchEvents = async () => {
    const { data: dataGetted, error } = await supabase
      .from("events")
      .select("*")
      .eq("product_id", product_id)
      .order("created_at", { ascending: false });

    if (!error) {
      setEvents(dataGetted || []);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchEvents();
    }, [])
  );

  useEffect(() => {
    if (!companySelected?.id) {
      console.error("❌ Aucun company_id défini, requête annulée !");
      return;
    }

    fetchEvents();

    // ✅ Écoute les changements en temps réel et rafraîchit la liste
    const channel = supabase
      .channel("events-changes")
      .on(
        "postgres_changes",
        { event: "*", schema: "public", table: "events" },
        (payload: any) => {
          console.log("📌 Changement détecté dans events :", payload);
          setRefreshKey((prev) => prev + 1); // 🔥 Force un re-render en incrémentant `refreshKey`
        }
      )
      .subscribe(async (status: boolean) => {
        if (status === true) {
          console.log("✅ Abonné aux changements de la table `events` !");
        }
      });

    return () => {
      // Nettoyer l'écouteur lorsqu'on quitte la page
      supabase.removeChannel(channel);
    };
  }, [companySelected, refreshKey]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["User", "Supplier"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  return (
    <View style={styles.container}>
      <Header title="Événements du produit" />
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          {events.length > 0 ? (
            events.map((item, index) => {
              return (
                <PaperInfo
                  key={index}
                  title={item.wording}
                  text1={`Type: ${getEventTypeLabel(item.type)}`}
                  text2={`Description: ${item.description}`}
                  date={new Date(item.date)}
                  badgeShow={false}
                  onPress={() =>
                    router.push({
                      pathname: "/event/detailsEvent",
                      params: {
                        date: item.date,
                        wording: item.wording,
                        causes: item.causes,
                        idEvent: item.id,
                        description: item.description,
                        typeEvent: item.type
                      },
                    })
                  }
                />
              );
            })
          ) : (
            <Text style={styles.noEventsText}>
              Aucun événement n'a été créé pour ce produit
            </Text>
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
    gap: 15,
  },
  noEventsText: {
    textAlign: "center",
    color: "#666",
    marginTop: 20,
  },
}); 