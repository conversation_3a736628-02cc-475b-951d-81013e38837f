/*
app/info/createProcess.tsx

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern` et `Extern` n'ont pas accès à cette page.
*/

import ContainedButton from "@/components/ui/buttons/ContainedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useRouter } from "expo-router";
import {
  StyleSheet,
  Image,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  Platform,
} from "react-native";

import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import InputFileCustomized from "@/components/ui/inputs/InputFileCustomized";
import { AppContext } from "@/state/AppContext";
import * as ImagePicker from "expo-image-picker";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import uploadPhoto from "@/lib/uploadPictureStorage";
import TypeProcess from "@/types/typeProcess";
import typeUserByAdmin from "@/types/typeUserByAdmin";
import CustomizedSelect from "@/components/ui/customizedSelect/customizedSelect";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import Header from "@/components/common/Header";

export default function CreateProcess() {
  const [loading, setLoading] = useState(false);
  const [usersAdmin, setUsersAdmin] = useState<typeUserByAdmin[] | null>([]);
  const { user, companySelected } = useContext(AppContext);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  type InputType = {
    label: string;
    name: string;
    placeholder: string;
    type?: "Select";
    data?: typeUserByAdmin[] | null;
  };

  const inputs: InputType[] = [
    {
      label: "Nom",
      name: "process_name",
      placeholder: "Ecrire le nom du processus",
    },
    {
      label: "Pilote du processus",
      name: "process_pilot",
      placeholder: "Ecrire le nom du pilote",
      type: "Select",
      data: usersAdmin,
    },
  ];

  useEffect(() => {
    const fetchUsersAdmin = async () => {
      const { data, error } = await supabase
        .from("users")
        .select()
        .eq("uid_admin", user?.uid)
        .in("profil", ["Pilote de processus", "Responsable QSE", "Responsable QSE Adjoint"]);

      setUsersAdmin(
        (data?.map((item: { first_name: string; uid: string }) => {
          return { label: item.first_name, value: item.uid };
        }) as any) || null
      );
    };
    fetchUsersAdmin();
  }, []);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const router = useRouter();
  const {
    register,
    handleSubmit,
    setError,
    formState: { errors, touchedFields },
    control,
    reset,
  } = useForm<TypeProcess>({
    defaultValues: {
      process_name: "",
      process_pilot: "",
    },
  });

  const createProcess = async (data: TypeProcess) => {
    if (!user) {
      console.log("Utilisateur non connecté.");
      return;
    }

    if (!data.process_name && !data.process_pilot) {
      Toast.show({
        type: "error",
        text1: "Le nom et le prénom sont obligatoires",
        text1Style: { color: "#1C3144" },
      });
    }

    if (data.process_name && data.process_pilot) {
      setLoading(true);

      const created_at = new Date();
      try {
        const { data: dataReturn, error } = await supabase
          .from("process")
          .insert({
            uid_admin: user?.uid,
            process_name: data.process_name,
            process_pilot: data.process_pilot,
            created_at: created_at,
            company_id: companySelected?.id,
          });

        if (error) {
          console.log("err_resp__", error);
          Toast.show({
            type: "error",
            text1: "Erreur lors d'enregistrement d'utilisateur",
            text1Style: { color: "#1C3144" },
          });
          setLoading(false);
          return;
        }

        Toast.show({
          type: "success",
          text1: "Processus créé",
          text1Style: { color: "#1C3144" },
        });
        reset();
        router.back();
        setLoading(false);
      } catch (err) {
        setLoading(false);
        Toast.show({
          type: "error",
          text1: "Une erreur est survenue",
          text1Style: { color: "#1C3144" },
        });
      }
    }
  };

  return (
    <View style={{ flex: 1 }}>

      <View style={styles.mainContainer}>
        <ScrollView style={styles.container}>
          <View style={styles.containerContent}>
            <View style={styles.registerInfo}>
              <View style={styles.inputs}>
                {inputs.map((input, index) => {
                  if (input?.type === "Select") {
                    return (
                      <View key={index}>
                        <CustomizedSelect
                          name={input.name}
                          label={input.label}
                          register={register}
                          control={control}
                          errors={errors}
                          setError={setError}
                          data={input.data as any}
                        />
                      </View>
                    );
                  } else {
                    return (
                      <View key={index}>
                        <InputCustomized
                          label={input.label}
                          placeholder={input.placeholder}
                          register={register}
                          name={input.name}
                          control={control}
                          errors={errors}
                          setError={setError}
                        />
                      </View>
                    );
                  }
                })}
              </View>
            </View>

            <View style={styles.buttons}>
              <ContainedButton
                label="Créer"
                backgroundColor="#F99527"
                onPress={handleSubmit(createProcess)}
                disabled={loading}
              />
            </View>
          </View>
        </ScrollView>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    alignItems: 'center',
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  containerContent: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    width: "100%",
    gap: 20,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
    marginTop: 20,
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
});
