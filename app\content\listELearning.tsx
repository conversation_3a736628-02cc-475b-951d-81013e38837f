import { supabase } from "@/lib/supabase";
import { AppContext } from "@/state/AppContext";
import TypeELearning from "@/types/typeELearning";
import { AntDesign, Ionicons } from "@expo/vector-icons";
import { router, useFocusEffect } from "expo-router";
import { useCallback, useContext, useState, useEffect } from "react";
import { Dimensions, Platform, useWindowDimensions, ActivityIndicator } from "react-native";
import {
  TouchableOpacity,
  View,
  FlatList,
  StyleSheet,
  Text,
  Image,
} from "react-native";
import Header from "@/components/common/Header";

export default function CreateELearning() {
  const [eLearningsVideos, setELearningsVideos] = useState<TypeELearning[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { width } = useWindowDimensions();
  const { user, companySelected } = useContext(AppContext);
  const [viewStatus, setViewStatus] = useState<{ [key: number]: boolean }>({});
  const [clickStatus, setClickStatus] = useState<{ [key: number]: boolean }>({});

  // Détermine si on doit afficher en une ou deux colonnes
  const isDoubleColumn = Platform.OS === 'web' && width >= 1000;

  type typeRender = {
    item: TypeELearning;
  };

  const getYouTubeVideoId = (url: string) => {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
    const match = url?.match(regExp);
    return (match && match[2].length === 11) ? match[2] : null;
  };

  const isYouTubeUrl = (url: string) => {
    return url?.includes('youtube.com') || url?.includes('youtu.be');
  };

  const fetchDataELearnings = async () => {
    if (!user?.uid) return;
    
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from("elearnings")
        .select()
        .eq("uid_user", user.uid)
        .eq("file_type", "video");

      if (!error) {
        setELearningsVideos(data as any);
      } else {
        console.error('Erreur lors du chargement des vidéos:', error);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des vidéos:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getViewStatus = async (itemId: number) => {
    const { data } = await supabase
      .from("elearnings_students")
      .select("view_at")
      .eq("elearning_id", itemId)
      .eq("user_id", user?.uid);
    
    return data?.[0]?.view_at != null;
  };

  const getClickStatus = async (itemId: number) => {
    const { data } = await supabase
      .from("elearnings_students")
      .select("click_at")
      .eq("elearning_id", itemId)
      .eq("user_id", user?.uid);
    
    return data?.[0]?.click_at != null;
  };

  useEffect(() => {
    const fetchStatus = async () => {
      const newViewStatus: { [key: number]: boolean } = {};
      const newClickStatus: { [key: number]: boolean } = {};
      
      for (const item of eLearningsVideos) {
        newViewStatus[item.id] = await getViewStatus(item.id);
        newClickStatus[item.id] = await getClickStatus(item.id);
      }
      
      setViewStatus(newViewStatus);
      setClickStatus(newClickStatus);
    };
    
    fetchStatus();
  }, [eLearningsVideos]);

  // Charger les données au montage initial du composant
  useEffect(() => {
    if (user?.uid) {
      fetchDataELearnings();
    }
  }, [user?.uid]);

  // Recharger les données quand la page redevient active
  useFocusEffect(
    useCallback(() => {
      if (user?.uid) {
        fetchDataELearnings();
      }
    }, [user?.uid])
  );

  const renderItem = ({ item }: typeRender) => {
    const videoId = isYouTubeUrl(item.url_media) ? getYouTubeVideoId(item.url_media) : null;
    const thumbnailUrl = videoId 
      ? `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`
      : item.url_mignature;

    const handlePress = async () => {
      // Enregistrer la vue avant de naviguer
      const { error } = await supabase.from("elearnings_students").insert({
        elearning_id: item.id,
        user_id: user?.uid,
        view_at: new Date().toISOString(),
        company_id: companySelected?.id
      });

      if (error) {
        console.error('Erreur lors de l\'enregistrement de la vue:', error);
      }

      router.push({
        pathname: "/content/oneELearning",
        params: {
          idELearning: item.id,
        },
      });
    };

    return (
      <TouchableOpacity
        style={styles.card}
        onPress={handlePress}
      >
        {/* Image de la vidéo */}
        <View>
          <Image 
            source={{ uri: thumbnailUrl }} 
            style={styles.image}
          />
          {/* Icône Play en haut à gauche */}
          <View style={[styles.playIconContainer, videoId && styles.youtubePlayIcon]}>
            {videoId ? (
              <AntDesign name="youtube" size={32} color="red" />
            ) : (
              <AntDesign name="playcircleo" size={32} color="#F99527" />
            )}
          </View>
        </View>

        {/* Texte de la vidéo */}
        <View style={styles.overlay}>
          <Text style={styles.title} numberOfLines={2} ellipsizeMode="tail">
            {item.title}
          </Text>
          <Text style={styles.description} numberOfLines={2} ellipsizeMode="tail">
            {item.description}
          </Text>

          <View style={styles.checkContainer}>
            <Ionicons
              name="checkmark-done-sharp"
              size={20}
              color={viewStatus[item.id] && clickStatus[item.id] ? "#34C759" : "gray"}
              style={styles.checkDoubl}
            />
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <Header
        title="Vidéos"
        onPressIcon={() => router.navigate("/content/createELearning")}
        onPressFlesh={() => router.navigate("/content/elearning")}
      />
      <View style={[
        styles.contentContainer,
        !isDoubleColumn && styles.singleColumnContainer
      ]}>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#F99527" />
          </View>
        ) : eLearningsVideos?.length > 0 ? (
          <FlatList
            data={eLearningsVideos}
            renderItem={renderItem}
            keyExtractor={(item) => item.id as any}
            showsHorizontalScrollIndicator={false}
            snapToAlignment="start"
            snapToInterval={width * 0.8}
            decelerationRate="fast"
            contentContainerStyle={styles.listContainer}
            numColumns={isDoubleColumn ? 2 : 1}
            key={isDoubleColumn ? 'double' : 'single'}
          />
        ) : (
          <Text style={{ padding: 10 }}>
            Vous avez pas encore crée ce contenu !
          </Text>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    flex: 1,
    ...(Platform.OS === 'web' && {
      maxWidth: 1000,
      alignSelf: 'center',
      width: '100%',
      paddingHorizontal: 20,
    }),
  },
  singleColumnContainer: {
    maxWidth: 500,
  },
  listContainer: {
    paddingBottom: 300,
    ...(Platform.OS === 'web' && {
      alignItems: 'center',
    }),
  },
  card: {
    borderRadius: 10,
    overflow: "hidden",
    backgroundColor: "#fff",
    elevation: 3,
    padding: 10,
    margin: 10,
    paddingBottom: 0,
    ...(Platform.OS === 'web' && {
      width: 450,
      marginHorizontal: 10,
      marginVertical: 15,
    }),
  },
  image: {
    width: "100%",
    height: Platform.OS === 'web' ? 250 : 150,
    borderRadius: 10,
  },
  overlay: {
    padding: 10,
  },
  title: {
    fontSize: Platform.OS === 'web' ? 18 : 16,
    fontWeight: "bold",
    alignSelf: "center",
  },
  description: {
    fontSize: Platform.OS === 'web' ? 16 : 14,
    color: "#666",
    alignSelf: "center",
    textAlign: 'center',
    marginTop: 5,
  },
  checkContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 5,
  },
  checkDoubl: {
    alignSelf: 'flex-end',
  },
  playIconContainer: {
    position: "absolute",
    bottom: 10,
    left: 10,
    borderRadius: 20,
    padding: 5,
  },
  youtubePlayIcon: {
    backgroundColor: 'white',
    borderRadius: 50,
    padding: 5,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
});
