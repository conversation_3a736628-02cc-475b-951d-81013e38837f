/*
app/event/createEvent.tsx

Page pour créer un événement.

Informations pertinentes :

- On récupère les processus de l'entreprise depuis la table `process`
  - La requête est effectuée sur `process` en filtrant avec `company_id`
  - Les données sont formatées sous forme de liste pour être utilisées dans un `Dropdown`

- On récupère les utilisateurs admin/responsables de l'entreprise depuis la table `users`
  - La requête sélectionne uniquement les utilisateurs ayant un rôle d'administrateur
  - On ajoute également les fournisseurs de l'entreprise via une seconde requête sur `suppliers`
  - La liste des administrateurs et fournisseurs est fusionnée pour permettre la sélection dans le formulaire

- Lors de la validation du formulaire :
  - Les données sont insérées dans la table `events` avec les informations saisies et les images uploadées
  - Un administrateur est assigné à l'événement et enregistré dans la table `event_administrators`
  - Les images sont uploadées via `uploadMultipleImagesStorage` et stockées sous forme d'URL dans la colonne `file`
  - L'événement est créé avec un lien vers le processus sélectionné

- Redirections et interactions :
  - Une fois l'événement créé, un message de succès est affiché (`Alert`)
  - L'utilisateur est redirigé vers la page précédente après un court délai (`router.back()`)
  - Les utilisateurs `User`, `Intern`, `Supplier` et `Extern` sont automatiquement redirigés (`navigation.goBack()`) s'ils accèdent à la page

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern`, `Supplier` et `Extern` n'ont pas accès à cette page.
*/


import { useRouter } from "expo-router";
import {
  StyleSheet,
  Image,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Platform,
  TextInput,
  Alert,
  KeyboardAvoidingView,
} from "react-native";
import DateTimePicker from "@react-native-community/datetimepicker";
import { DateTimePickerAndroid } from "@react-native-community/datetimepicker";
import * as ImagePicker from "expo-image-picker";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import TypeProcess from "@/types/typeProcess";
import { Dropdown } from "react-native-element-dropdown";
import { AntDesign, Octicons } from "@expo/vector-icons";
import typeUserByAdmin from "@/types/typeUserByAdmin";
import TypeEvent from "@/types/typeEvent";
import uploadMultipleImages from "@/lib/uploadMultipleImagesStorage";
import InputFileCustomized from "@/components/ui/inputs/InputFileCustomized";
import ContainedButton from "@/components/ui/buttons/ContainedButton";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import React from "react";
import { WebDateTimePicker } from "@/components/ui/datepicker/WebDateTimePicker";

// Types d'événement (dropdown)
const typesEvent = [
  { label: "Non conformité - Fournisseur", value: "supplier" },
  { label: "Audit - Non-conformité (code NC)", value: "non_compliant" },
  { label: "Audit - Point sensible (code PS)", value: "sensitive_point" },
  { label: "Situation dangereuse (code SD)", value: "dangerous_situation" },
  { label: "Accident du travail (code AT)", value: "work_accident" },
  { label: "Presqu'accident (code PAT)", value: "near_miss" },
  { label: "Malfaçon produit ou service (code NQ)", value: "product_defect" },
  { label: "Danger environnemental (code DE)", value: "environmental_danger" },
  {
    label: "Accident environnemental (code AE)",
    value: "environmental_accident",
  },
  { label: "Réclamation client (code REC)", value: "customer_complaint" },
  { label: "Risque (code RI)", value: "risk" },
  { label: "Opportunité (code OPP)", value: "opportunity" },
  { label: "Objectif (code OBJ)", value: "objective" },
  {
    label: "Partie intéressée pertinente (code PIP)",
    value: "interested_party",
  },
  { label: "Matériel - Défaut (code MAT)", value: "material_defect" },
];

// Étendre le type typeUserByAdmin
interface ExtendedUserByAdmin extends typeUserByAdmin {
  type: "user" | "supplier";
}

export default function CreateEvent() {
  const [loading, setLoading] = useState(false);
  const [imageUris, setImageUris] = useState<string[]>([]);
  const { user, companySelected } = useContext(AppContext);
  const [process, setProcess] = useState<TypeProcess[]>([]);
  const [usersAdmin, setUsersAdmin] = useState<ExtendedUserByAdmin[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<ExtendedUserByAdmin[]>([]);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [products, setProducts] = useState<{ label: string; value: number }[]>([]);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const router = useRouter();
  const {
    register,
    handleSubmit,
    setError,
    watch,
    formState: { errors },
    control,
    reset,
  } = useForm({
    defaultValues: {
      uid_admin: "",
      created_at: "",
      type: "",
      administrator: "",
      process: "",
      file: "",
      causes: "",
      wording: "",
      description: "",
      product_id: "",
    },
  });

  // Surveiller le type d'événement sélectionné
  const selectedType = watch("type");

  // Récupérer les produits de l'entreprise
  useEffect(() => {
    const fetchProducts = async () => {
      if (companySelected?.id) {
        const { data, error } = await supabase
          .from("products")
          .select("id, name")
          .eq("company_id", companySelected.id);

        if (!error && data) {
          const formattedProducts = data.map((product: { id: number; name: string }) => ({
            label: product.name,
            value: product.id
          }));
          setProducts(formattedProducts);
        }
      }
    };

    if (selectedType === "material_defect") {
      fetchProducts();
    }
  }, [selectedType, companySelected?.id]);

  // Filtrer les utilisateurs en fonction du type d'événement
  useEffect(() => {
    if (selectedType === "supplier") {
      // Pour les événements de type fournisseur, afficher uniquement les fournisseurs
      const suppliersOnly = usersAdmin.filter(user => user.type === "supplier");
      setFilteredUsers(suppliersOnly);
    } else {
      // Pour tous les autres types d'événements, afficher uniquement les utilisateurs (pas les fournisseurs)
      const usersOnly = usersAdmin.filter(user => user.type === "user");
      setFilteredUsers(usersOnly);
    }
  }, [selectedType, usersAdmin]);

  // Récupérer les informations nécessaires
  useEffect(() => {
    const fetchData = async () => {
      try {
        const companyId = companySelected?.id;

        // Récupérer les processus de l'entreprise
        const { data: processData, error: processError } = await supabase
          .from("process")
          .select("id, process_name")
          .eq("company_id", companyId);

        if (processError) {
          console.error(
            "Erreur lors de la récupération des processus :",
            processError.message
          );
        } else {
          const formattedProcess: any = processData.map((process: any) => ({
            label: process.process_name,
            value: process.id,
          }));
          setProcess(formattedProcess);
        }

        // Récupérer les utilisateurs admin de l'entreprise
        const { data: usersData, error: usersError } = await supabase
          .from("company_users")
          .select(`
            user_id,
            users (
              uid,
              first_name,
              last_name,
              profil
            )
          `)
          .eq("company_id", companyId);

        if (usersError) {
          console.error(
            "Erreur lors de la récupération des utilisateurs :",
            usersError.message
          );
          return;
        }

        // Formatage des utilisateurs admins
        const formattedUsers: any = usersData
          .filter((item: any) => item.users?.profil != "Fournisseur")
          .map((item: any) => ({
            label: `${item.users.first_name} ${item.users.last_name}`,
            value: item.users.uid,
            type: "user"
          }));

        // Récupérer les fournisseurs de l'entreprise
        const { data: suppliersData, error: suppliersError } = await supabase
          .from("company_users")
          .select(`
            user_id,
            users (
              id,
              uid,
              first_name,
              last_name,
              phone_number,
              email,
              profil
            )
          `)
          .eq("company_id", companyId);

        if (suppliersError) {
          console.error(
            "Erreur lors de la récupération des fournisseurs :",
            suppliersError.message
          );
          return;
        }

        // Filtrer uniquement les fournisseurs
        const suppliersList = suppliersData
          .map((entry: any) => entry.users)
          .filter((user: any) => user.profil === "Fournisseur");

        // Récupérer les informations des fournisseurs depuis la table suppliers
        const { data: suppliersInfo, error: suppliersInfoError } = await supabase
          .from("suppliers")
          .select("uid_user, company_name")
          .in("uid_user", suppliersList.map((s: any) => s.uid));

        if (suppliersInfoError) {
          console.error(
            "Erreur lors de la récupération des informations des fournisseurs :",
            suppliersInfoError.message
          );
          return;
        }

        // Créer un map des informations des fournisseurs
        const suppliersInfoMap = suppliersInfo.reduce((acc: Record<string, string>, curr: any) => {
          acc[curr.uid_user] = curr.company_name;
          return acc;
        }, {});

        // Formatage des fournisseurs
        const formattedSuppliers: any = suppliersList.map((supplier: any) => ({
          label: suppliersInfoMap[supplier.uid] || `${supplier.first_name} ${supplier.last_name}`,
          value: supplier.uid,
          type: "supplier",
          subtitle: `${supplier.first_name} ${supplier.last_name}`
        }));

        console.log("Utilisateurs formatés:", formattedUsers);
        console.log("Fournisseurs formatés:", formattedSuppliers);

        // Stocker toutes les données
        setUsersAdmin([...formattedUsers, ...formattedSuppliers]);
      } catch (err) {
        console.error("Erreur inattendue lors de la récupération :", err);
      }
    };

    fetchData();
  }, [user]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "Intern", "Supplier"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  // Date (iOS)
  const handleDateChange = (event: any, date: any) => {
    if (date) {
      setSelectedDate(date); // Mettre à jour avec la date sélectionnée
    }
  };

  // Date (Android)
  const showDatePicker = () => {
    DateTimePickerAndroid.open({
      value: selectedDate,
      mode: "date",
      onChange: handleDateChange,
      display: "default",
    });
  };

  // Choix de plusieurs images
  const handleSelectImages = async () => {
    const permissionResult =
      await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (permissionResult.granted === false) {
      Toast.show({
        type: "error",
        text1: "Permissions refusées",
        text1Style: { color: "#1C3144" },
        text2: "Vous devez autoriser l'accès à vos photos.",
        text2Style: { color: "#1C3144" },
      });
      return;
    }

    const pickerResult = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsMultipleSelection: true,
      quality: 1,
    });

    if (!pickerResult.canceled && pickerResult.assets) {
      const uris = pickerResult.assets.map((asset) => asset.uri);
      setImageUris(uris);
    }
  };

  // Validation du formulaire
  const handleSubmitCreateEvent = async (data: any) => {
    try {
      let uploadedUrls: string[] = [];
  
      if (imageUris.length > 0) {
        for (const uri of imageUris) {
          const uploadResult = await uploadMultipleImages(uri, user?.uid || "");
          if (uploadResult?.url) uploadedUrls.push(uploadResult.url);
        }
      }
  
      const created_at = new Date();
  
      // Insérer l'événement
      const { data: eventData, error: eventError } = await supabase
        .from("events")
        .insert({
          file: uploadedUrls,
          created_at: created_at,
          company_id: companySelected?.id ?? null,
          uid_admin: user?.uid ?? null,
          administrator: data.administrator ?? null,
          type: data?.type ?? "",
          date: selectedDate ?? new Date(),
          process: data?.process ?? null,
          causes: data?.causes ?? "",
          wording: data?.wording ?? "",
          description: data?.description ?? "",
          product_id: data?.type === "material_defect" ? data?.product_id : null,
        })
        .select("id")
        .single();
  
      if (eventError) {
        console.error("Erreur lors de l'insertion dans `events` :", eventError);
        Alert.alert("Échec de la création", eventError.message || "Une erreur est survenue.");
        return;
      }
  
      // Vérifier si l'administrateur est valide dans `administrators`
      const { data: adminCheck } = await supabase
        .from("administrators")
        .select("administrator_uuid")
        .eq("administrator_uuid", data.administrator)
        .single();
  
      if (!adminCheck) {
        console.warn("L'administrateur sélectionné n'existe pas dans `administrators`.");
        Alert.alert("Erreur", "L'administrateur sélectionné n'existe pas.");
        return;
      }
  
      // Insérer l'administrateur dans `event_administrators`
      const { error: adminError } = await supabase.from("event_administrators").insert({
        event_id: eventData?.id,
        administrator_uuid: data.administrator,
        administrator_type: data.administratorType || "user",
      });
  
      if (adminError) {
        console.error("Erreur lors de l'insertion dans `event_administrators` :", adminError);
        Alert.alert("Erreur", "Impossible d'ajouter l'administrateur à l'événement.");
        return;
      }
    
      setTimeout(() => {
        reset();
        router.back();
      }, 1000);
  
      Alert.alert("Succès", "L'événement a été créé avec succès.");

    } catch (err) {
      console.error("Erreur inattendue :", err);
      Alert.alert("Erreur inattendue", "Veuillez réessayer plus tard.");
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 0}
    >
      <ScrollView 
        style={styles.form}
        contentContainerStyle={styles.formContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.container}>
          {/* Formulaire */}
          <View style={styles.form}>
            {/* Type */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Type d'événement</Text>
              <Controller
                control={control}
                name="type"
                rules={{ required: "Veuillez sélectionner un événement." }}
                render={({ field: { onChange, value } }) => (
                  <Dropdown
                    style={dropdownStyles.dropdown}
                    placeholderStyle={dropdownStyles.placeholderStyle}
                    selectedTextStyle={dropdownStyles.selectedTextStyle}
                    data={typesEvent}
                    placeholder="Sélectionner un événement"
                    value={value}
                    onChange={(item) => {
                      onChange(item.value);
                    }}
                    labelField="label"
                    valueField="value"
                  />
                )}
              />
              {errors.type && (
                <Text style={styles.errorText}>{errors.type.message}</Text>
              )}
            </View>

            {/* Sélection du produit pour les défauts matériels */}
            {selectedType === "material_defect" && (
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Produit concerné</Text>
                <Controller
                  control={control}
                  name="product_id"
                  rules={{ required: "Veuillez sélectionner un produit." }}
                  render={({ field: { onChange, value } }) => (
                    <Dropdown
                      style={dropdownStyles.dropdown}
                      placeholderStyle={dropdownStyles.placeholderStyle}
                      selectedTextStyle={dropdownStyles.selectedTextStyle}
                      data={products}
                      placeholder="Sélectionner un produit"
                      value={value}
                      onChange={(item) => {
                        onChange(item.value);
                      }}
                      labelField="label"
                      valueField="value"
                    />
                  )}
                />
                {errors.product_id && (
                  <Text style={styles.errorText}>{errors.product_id.message}</Text>
                )}
              </View>
            )}

            {/* Champ Nom */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Libellé</Text>
              <Controller
                control={control}
                name="wording"
                rules={{ required: "Le libellé est requis." }}
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    maxLength={50}
                    placeholderTextColor={"#6B7280"}
                    style={styles.input}
                    placeholder="Libellé"
                    onChangeText={onChange}
                    value={value}
                  />
                )}
              />
              {errors.wording && (
                <Text style={styles.errorText}>{errors.wording.message}</Text>
              )}
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>Description</Text>
              <Controller
                control={control}
                name="description"
                rules={{ required: "La description est requis." }}
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    maxLength={300}
                    placeholderTextColor={"#6B7280"}
                    style={styles.input}
                    placeholder="Description"
                    onChangeText={onChange}
                    value={value}
                    multiline
                  />
                )}
              />
              {errors.wording && (
                <Text style={styles.errorText}>{errors.wording.message}</Text>
              )}
            </View>

            {/* Date */}
            {Platform.OS === "android" ? (
              <TouchableOpacity
                onPress={showDatePicker}
                style={styles.dateTimePickerContainer}
              >
                <Text style={styles.dateTimePickerLabel}>Date</Text>
                <Text style={styles.datePicker}>
                  {selectedDate.toLocaleDateString()}
                </Text>
              </TouchableOpacity>
            ) : Platform.OS === "ios" ? (
              <View style={styles.dateTimePickerContainer}>
                <Text style={styles.label}>Date</Text>
                <DateTimePicker
                  value={selectedDate}
                  mode="date"
                  display="default"
                  onChange={handleDateChange}
                  style={styles.datePicker}
                />
              </View>
            ) : (
              <View style={styles.dateTimePickerContainer}>
                <Text style={styles.label}>Date</Text>
                <WebDateTimePicker
                  value={selectedDate}
                  onChange={(date) => {
                    if (date) {
                      setSelectedDate(date);
                    }
                  }}
                  style={styles.datePicker}
                />
              </View>
            )}

            {/* Champ Responsable/Administrateur */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>
                {selectedType === "supplier" ? "Fournisseur concerné" : "Responsable supposé"}
              </Text>
              <Controller
                control={control}
                name="administrator"
                rules={{ required: "Veuillez sélectionner un responsable." }}
                render={({ field: { onChange, value } }) => (
                  <Dropdown
                    style={dropdownStyles.dropdown}
                    placeholderStyle={dropdownStyles.placeholderStyle}
                    selectedTextStyle={dropdownStyles.selectedTextStyle}
                    data={filteredUsers}
                    placeholder={
                      loading
                        ? "Chargement du responsable..."
                        : selectedType === "supplier"
                        ? "Choix du fournisseur"
                        : "Choix du responsable"
                    }
                    searchPlaceholder="Sélectionner un responsable"
                    value={value}
                    onChange={(item) => {
                      onChange(item.value);
                    }}
                    labelField="label"
                    valueField="value"
                    renderItem={(item) => (
                      <View style={styles.dropdownItem}>
                        <Text style={styles.dropdownLabel}>{item.label}</Text>
                        {item.subtitle && (
                          <Text style={styles.dropdownSubtitle}>{item.subtitle}</Text>
                        )}
                      </View>
                    )}
                  />
                )}
              />
              {errors.administrator && (
                <Text style={styles.errorText}>
                  {errors.administrator.message}
                </Text>
              )}
            </View>

            {/* Champ Processus */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Processus concerné</Text>
              <Controller
                control={control}
                name="process"
                rules={{ required: "Le responsable est requis." }}
                render={({ field: { onChange, value } }) => (
                  <Dropdown
                    style={dropdownStyles.dropdown}
                    placeholderStyle={dropdownStyles.placeholderStyle}
                    selectedTextStyle={dropdownStyles.selectedTextStyle}
                    inputSearchStyle={dropdownStyles.inputSearchStyle}
                    iconStyle={dropdownStyles.iconStyle}
                    data={process}
                    search
                    maxHeight={300}
                    labelField="label"
                    valueField="value"
                    placeholder={
                      loading
                        ? "Chargement des processus..."
                        : "Choix du processus"
                    }
                    searchPlaceholder="Choix du processus"
                    value={value}
                    onChange={(item) => {
                      onChange(item.value);
                    }}
                    renderLeftIcon={() => (
                      <AntDesign
                        style={dropdownStyles.icon}
                        color="black"
                        name="Safety"
                        size={20}
                      />
                    )}
                  />
                )}
              />
            </View>

            {imageUris.length > 0 ? (
              <>
                <View style={styles.imageGrid}>
                  {imageUris.map((uri, index) => (
                    <Image
                      key={index}
                      source={{ uri }}
                      style={styles.imagePreview}
                    />
                  ))}
                </View>

                <TouchableOpacity onPress={() => setImageUris([])}>
                  <Text
                    style={{
                      color: "blue",
                      textAlign: "center",
                      marginBottom: 20,
                    }}
                  >
                    Changer les photos
                  </Text>
                </TouchableOpacity>
              </>
            ) : (
              <View style={styles.inputContainer}>
                <InputFileCustomized
                  label={"Photos"}
                  placeholder={"Choisir des photos"}
                  onPress={handleSelectImages}
                />
              </View>
            )}

            {/* Champ Causes
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Analyse des causes</Text>
              <Controller
                control={control}
                name="causes"
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    maxLength={20}
                    placeholderTextColor={"#6B7280"}
                    style={styles.input}
                    placeholder="Libellé"
                    onChangeText={onChange}
                    value={value}
                  />
                )}
              />
              {errors.causes && (
                <Text style={styles.errorText}>{errors.causes.message}</Text>
              )}
            </View> */}

            {/* Bouton de soumission */}
            <ContainedButton
              label="Créer"
              backgroundColor="#F99527"
              onPress={async () => {
                await handleSubmit(handleSubmitCreateEvent)(); // ✅ Gère l'attente de la soumission
              }}
            />
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  form: {
    flex: 1,
  },
  formContent: {
    paddingHorizontal: 30,
    paddingBottom: 50,
  },
  headSection: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 30,
  },
  imageGrid: {
    flexDirection: "row",
    flexWrap: "wrap", // ✅ Permet le passage à la ligne après 2 images
    justifyContent: "flex-start", // ✅ Alignement des images
    gap: 10, // ✅ Espacement entre les images
    marginBottom: 20,
  },
  backButton: {
    width: 24,
    textAlign: "left",
  },
  titleWrapper: {
    display: "flex",
    flexDirection: "row",
    marginVertical: 15,
  },
  title: {
    color: "#262627",
    fontSize: 17,
    textAlign: "left",
  },
  input: {
    borderWidth: 2,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "auto",
    padding: 12,
    marginTop: 10,
    color: "#000000",
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginBottom: 5,
  },
  infoText: {
    fontSize: 14,
    marginVertical: 10,
    color: "#555",
  },
  inputContainer: {
    position: "relative",
    marginBottom: 20,
    width: "100%",
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
    textAlign: "left",
  },
  confirmButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    marginBottom: 100,
    backgroundColor: "#f99527",
    marginTop: 10,
  },
  confirmButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  imagePicker: {
    width: "100%",
    padding: 10,
    backgroundColor: "#FFFFFF",
    borderColor: "#CCC",
    borderRadius: 8,
    borderWidth: 2,
    alignItems: "center",
    marginBottom: 10,
    marginTop: 10,
  },
  imagePickerText: {
    color: "#1F2937",
    fontWeight: "bold",
  },
  imagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  imageContainer: {
    width: "100%",
    justifyContent: "center",
  },
  uploadPlaceholder: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  uploadIcon: {
    borderWidth: 1,
    borderColor: "#f99527",
    borderRadius: 7,
    padding: 5,
  },
  uploadText: {
    textAlign: "center",
    fontWeight: "normal",
  },
  fullImagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  dateTimePickerContainer: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    textAlign: "left",
    marginVertical: 20,
  },
  dateTimePickerLabel: {
    color: "#063E76",
    fontSize: 14,
    fontWeight: "bold",
    backgroundColor: "#FFF",
  },
  datePicker: {
    width: "100%",
    marginTop: 10,
    marginBottom: 10,
  },
  dropdownItem: {
    padding: 10,
  },
  dropdownLabel: {
    fontSize: 16,
    color: '#000',
  },
  dropdownSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
});

const dropdownStyles = StyleSheet.create({
  dropdown: {
    marginVertical: 16,
    height: 50,
    backgroundColor: "white",
    borderRadius: 12,
    padding: 12,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,

    elevation: 2,
  },
  icon: {
    marginRight: 5,
  },
  item: {
    padding: 17,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  textItem: {
    flex: 1,
    fontSize: 16,
  },
  placeholderStyle: {
    fontSize: 16,
  },
  selectedTextStyle: {
    fontSize: 16,
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 16,
  },
});
