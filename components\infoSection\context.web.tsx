import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { useRouter, usePathname } from "expo-router";
import { Spinner } from "../ui/spinner";
import TypeContext from "@/types/typeContext";
import PaperCompany from "../common/paperCompany";

type typeContextsProps = {
  icon?: any;
  data: TypeContext[];
  isLanding?: boolean;
};

export default function ContextsInfoSection({
  icon,
  data,
  isLanding,
}: typeContextsProps) {
  const router = useRouter();
  const path = usePathname();

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Contexte</Text>
        <TouchableOpacity onPress={() => router.navigate("/info/context")}>
          {icon ? (
            icon
          ) : (
            <Text style={styles.seeMore}>Voir plus</Text>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView 
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {isLanding ? (
          <View style={styles.spinnerContainer}>
            <Spinner size="small" color="#1C3144" />
          </View>
        ) : data?.length > 0 ? (
          <View style={styles.gridContainer}>
            {(path === "/Informations" || path === "/menu/itemsMenuCockpit/info"
              ? data?.slice(-2)
              : data
            ).map((item, index) => (
              <View key={index} style={styles.gridItem}>
                <PaperCompany
                  title={item.context}
                  text2={item.organisation}
                  text3={item.website}
                  text4={item.history}
                />
              </View>
            ))}
          </View>
        ) : (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>Aucun contexte d'entreprise.</Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: "100%",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#E6E6E6",
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1C3144",
  },
  seeMore: {
    color: "#F99527",
    textDecorationLine: "underline",
    fontFamily: "Poppins",
    fontWeight: "bold",
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingVertical: 16,
  },
  gridContainer: {
    display: "grid",
    gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
    gap: 16,
    maxWidth: 1200,
    marginHorizontal: "auto",
    paddingHorizontal: 16,
  },
  gridItem: {
    width: "100%",
  },
  spinnerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
  },
}); 