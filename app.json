{"expo": {"name": "management-qse-mobile", "slug": "management-qse-mobile", "version": "1.0.0", "orientation": "portrait", "scheme": "management-qse", "platforms": ["ios", "android", "web"], "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.app.managementqse", "icon": "./assets/images/login/logoIZYS.png", "infoPlist": {"NSPhotoLibraryUsageDescription": "Nous avons besoin d'accéder à votre bibliothèque pour sélectionner un fichier.", "NSPhotoLibraryAddUsageDescription": "Nous avons besoin d'enregistrer un fichier.", "NSDocumentsDirectoryUsageDescription": "Nous avons besoin d'accéder à vos documents.", "CFBundleURLTypes": [{"CFBundleURLSchemes": ["management-qse"]}]}}, "splash": {"image": "./assets/images/login/logoIZYS_white.png", "resizeMode": "contain", "backgroundColor": "#1C3144"}, "android": {"permissions": ["INTERNET"], "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.captainsubmit.managementqsemobile", "intentFilters": [{"action": "VIEW", "autoVerify": true, "data": [{"scheme": "management-qse"}], "category": ["BROWSABLE", "DEFAULT"]}]}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/icon3.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-font", "expo-video", "expo-web-browser"], "experiments": {"typedRoutes": true}, "extra": {"expo-router": {"appDir": "app"}, "NEXT_PUBLIC_OPENAI_API_KEY": "***********************************************************************************************"}}}