/*
app/info/detailsActivity.tsx

Page de détails d'une activité.

Informations pertinentes :

- Les informations de l'activité sont récupérées depuis la table `activities`
- Les processus sont récupérés depuis la table `process`. Les processus attribués à l'activité spécifique sont dans la colonne `process_id` dans de la table `activities`
*/

import React, { useContext, useEffect, useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  Alert,
  FlatList,
  TouchableOpacity,
  Platform,
} from "react-native";
import {
  ParamListBase,
  RouteProp,
  useNavigation,
  useRoute,
  useFocusEffect,
} from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AppContext } from "@/state/AppContext";
import Header from "@/components/common/Header";
import { supabase } from "@/lib/supabase";
import { useLocalSearchParams } from "expo-router";
import { useRouter } from "expo-router";
import moment from "moment";

// Données de navigation
type DetailActivityRouteParams = {
  activity: {
    id: number;
    activity_name: string;
    description: string;
    inputs: string;
    outputs: string;
    created_at: string;
  };
};

export default function detailsActivity() {
  const { user } = useContext(AppContext);
  const params = useLocalSearchParams();
  const [activity, setActivity] = useState<any>(null);
  const [process, setProcess] = useState<any>([]);
  const router = useRouter();
  const route =
    useRoute<RouteProp<{ params: DetailActivityRouteParams }, "params">>();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  // Fonction pour récupérer les informations de l'activité
  const fetchActivityDetails = async () => {
    try {
      if (!params.id) {
        console.error("Aucun `id` fourni");
        return;
      }

      // Récupérer les détails de l'activité
      const { data: activityData, error: activityError } = await supabase
        .from("activities")
        .select("*")
        .eq("id", params.id)
        .single();

      if (activityError) {
        console.error(
          "Erreur lors de la récupération de l'activité :",
          activityError
        );
        return;
      }

      setActivity(activityData);

      // Vérifier si l'activité a un `process_id`
      if (!activityData.process_id) {
        console.log("Aucun processus associé à cette activité.");
        return;
      }

      // Récupérer le processus associé
      const { data: processData, error: processError } = await supabase
        .from("process")
        .select("*")
        .eq("id", activityData.process_id)
        .single();

      if (processError) {
        console.error(
          "Erreur lors de la récupération du processus :",
          processError
        );
        return;
      }

      setProcess([processData]);
    } catch (err) {
      console.error("Erreur inattendue :", err);
    }
  };

  // Récupérer les informations de l'activité au chargement et après chaque focus
  useFocusEffect(
    React.useCallback(() => {
      fetchActivityDetails();
    }, [])
  );

  // Restrictions selon le statut
  useEffect(() => {
    if (!user) return;

    if (["User", "Extern", "Intern", "Supplier"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const handlUpdateIcon = () => {
    router.push({
      pathname: "/info/updateActivity",
      params: {
        id: params.id,
        activityId: params.uid,
        activity_name: activity?.activity_name,
        description: activity?.description,
        inputs: activity?.inputs,
        status: activity?.status,
        outputs: activity?.outputs,
        process_id: activity?.process_id,
        process_name: process[0]?.process_name
      },
    });
  };

  return (
    <View style={{ flex: 1 }}>
      <Header onPressIcon={() => handlUpdateIcon()} />
      <View style={styles.mainContainer}>
        <FlatList
          data={process}
          keyExtractor={(item) => `process-${item.id}`}
          nestedScrollEnabled={true}
          showsVerticalScrollIndicator={false}
          style={styles.container}
          contentContainerStyle={styles.contentContainer}
          ListHeaderComponent={
            <View style={styles.containerContent}>
              <View style={styles.activityCard}>
                <View style={styles.activityInfo}>
                  <Text style={styles.companyName}>
                    {activity?.activity_name || "Nom non disponible"}
                  </Text>
                  <Text style={styles.contactInfo}>
                    Description : {activity?.description || "Non spécifié"}
                  </Text>
                  <Text style={styles.contactInfo}>
                    Entrées : {activity?.inputs || "Non spécifié"}
                  </Text>
                  <Text style={styles.contactInfo}>
                    Sorties : {activity?.outputs || "Non spécifié"}
                  </Text>
                </View>
              </View>

              <Text style={{ fontWeight: "bold", marginBottom: 10 }}>
                Processus associés
              </Text>
            </View>
          }
          renderItem={({ item }) => (
            <TouchableOpacity
              style={styles.processItem}
              onPress={() =>
                navigation.navigate("/process/updateProcess", {
                  process: item,
                })
              }
            >
              <View style={styles.processHeader}>
                <Text style={styles.processTitle}>
                  {item.process_name.length > 30
                    ? item.process_name.slice(0, 30) + "..."
                    : item.process_name}
                </Text>
                <Text style={styles.date}>
                  {moment(item.created_date).format("DD/MM/YYYY")}
                </Text>
              </View>

              <Text style={styles.processDescription}>
                {item.causes && item.causes.length > 90
                  ? item.causes.slice(0, 90) + "..."
                  : item.causes || "Pas de description"}
              </Text>
            </TouchableOpacity>
          )}
          ListEmptyComponent={
            <Text style={styles.emptyText}>Aucun processus disponible.</Text>
          }
          ItemSeparatorComponent={() => <View style={styles.separator} />}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    alignItems: 'center',
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  contentContainer: {
    paddingBottom: 100,
    ...(Platform.OS === 'web' ? {
      paddingHorizontal: 20,
    } : {
      paddingHorizontal: 10,
    }),
  },
  containerContent: {
    padding: 20,
  },
  activityCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  activityInfo: {
    flex: 1,
    gap: 5,
  },
  companyName: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1C3144",
  },
  cardName: {
    fontSize: 16,
    color: "#525252",
  },
  contactInfo: {
    fontSize: 14,
    color: "#525252",
  },
  statusContainer: {
    marginLeft: 10,
  },
  statusText: {
    fontSize: 12,
    color: "#FFFFFF",
    padding: 8,
    borderRadius: 50,
    textAlign: "center",
    minWidth: 100,
  },
  processCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  detailSection: {
    marginBottom: 15,
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#1C3144",
    marginBottom: 5,
  },
  detailText: {
    fontSize: 14,
    color: "#525252",
  },
  editButton: {
    backgroundColor: "#F99527",
    borderRadius: 8,
    padding: 15,
    alignItems: "center",
    marginTop: 10,
  },
  editButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "bold",
  },
  disabledButton: {
    backgroundColor: "#CCCCCC",
  },
  listContainer: {
    paddingBottom: 300, // Ajuste la hauteur pour éviter que le dernier élément soit trop collé
  },
  itemContainer: {
    marginBottom: 10,
  },
  separator: {
    height: 10, // Ajoute un espacement entre les éléments
  },
  emptyText: {
    textAlign: "center",
    marginTop: 20,
    fontSize: 16,
    color: "gray",
  },
  processContainer: {
    marginTop: 20,
  },
  processItem: {
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    marginHorizontal: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  processHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 10,
  },
  processTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#1C3144",
    flex: 1,
  },
  processDescription: {
    fontSize: 14,
    color: "#525252",
    marginBottom: 10,
  },
  typeContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 10,
  },
  typeText: {
    fontSize: 12,
    color: "#FFFFFF",
    padding: 8,
    borderRadius: 50,
    textAlign: "center",
    minWidth: 100,
  },
  date: {
    fontSize: 14,
    color: "#525252",
  },
});
