/*
app/info/updateActivity.tsx

Formulaire pour modifier une activité.

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern` et `Extern` n'ont pas accès à cette page.
*/

import ContainedButton from "@/components/ui/buttons/ContainedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useFocusEffect, useLocalSearchParams, useRouter } from "expo-router";
import { StyleSheet, Image, View, Text, Alert, ScrollView, Platform } from "react-native";

import { useCallback, useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import TypeProcess from "@/types/typeProcess";
import TypeActivity from "@/types/typeActivity";
import CustomizedSelect from "@/components/ui/customizedSelect/customizedSelect";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";

export default function updateActivity() {
  const [loading, setLoading] = useState(false);
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [process, setProcess] = useState<TypeProcess[] | any>([]);
  const [activity, setActivity] = useState<TypeActivity | any>([]);
  const params = useLocalSearchParams();
  const { user, companySelected } = useContext(AppContext);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const router = useRouter();
  const { process_id, process_name, path, pilote } = useLocalSearchParams();

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
    control,
    reset,
  } = useForm<any>({
    defaultValues: {
      activity_name: "",
      description: "",
      inputs: "",
      outputs: "",
      process_id: process_id as any,
    },
  });

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const fetchData = async () => {
    const { data: dataGetted, error } = await supabase
      .from("process")
      .select()
      .eq("uid_admin", user?.uid)
      .eq("company_id", companySelected?.id);

    if (!error) {
      setProcess(
        (dataGetted?.map((item: any) => {
          return { label: item.process_name, value: item.id };
        }) as any) || null
      );
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [])
  );

  // Charger les valeurs initiales à partir des paramètres
  useEffect(() => {
    if (params.id && (!activity || activity.id !== Number(params.id))) {
      const newActivity = {
        id: Number(params.id),
        activity_name: params.activity_name as string,
        description: params.description as string,
        inputs: params.inputs as string,
        outputs: params.outputs as string,
        process_id: params.process_id ? Number(params.process_id) : null,
      };
  
      setActivity(newActivity);
  
      reset({
        activity_name: newActivity.activity_name,
        description: newActivity.description,
        inputs: newActivity.inputs,
        outputs: newActivity.outputs,
        process_id: newActivity.process_id,
      });
    }
  }, [params, reset]);  

  const inputs = [
    {
      label: "Nom",
      name: "activity_name",
      placeholder: "Ecrire le nom de l'activité",
    },
    {
      label: "Description",
      name: "description",
      placeholder: "Ecrire ton description",
      multiline: true,
    },
    {
      label: "Processus concerné",
      name: "process_id",
      placeholder: "",
      type: "Process",
      defaultValue: process_name || [],
      data: process || [],
    },

    {
      label: "Entrants",
      name: "inputs",
      placeholder: "Entrants",
      multiline: true,
    },
    {
      label: "Sortants",
      name: "outputs",
      placeholder: "Sortants",
      multiline: true,
    },
  ];

  const handleUpdateActivity = async (data: TypeActivity) => {
    if (
      !data.activity_name &&
      !data.description &&
      !data.inputs &&
      !data.outputs
    ) {
      Toast.show({
        type: "error",
        text1: "Tous les champs sont vides",
        text1Style: { color: "#1C3144" },
      });
      return;
    }

    setLoading(true);

    try {
      const updatedFields: Partial<TypeActivity> = {};

      if (data.activity_name !== activity?.activity_name)
        updatedFields.activity_name = data.activity_name;
      if (data.description !== activity?.description)
        updatedFields.description = data.description;
      if (data.inputs !== activity?.inputs) updatedFields.inputs = data.inputs;
      if (data.outputs !== activity?.outputs)
        updatedFields.outputs = data.outputs;
      if (data.process_id !== activity?.process_id)
        updatedFields.process_id = data.process_id;

      if (Object.keys(updatedFields).length === 0) {
        Toast.show({
          type: "info",
          text1: "Aucune modification détectée",
          text1Style: { color: "#1C3144" },
        });
        setLoading(false);
        return;
      }

      const { error } = await supabase
        .from("activities")
        .update(updatedFields)
        .eq("id", activity?.id);

      if (error) {
        Toast.show({
          type: "error",
          text1: "Erreur lors de la mise à jour",
          text1Style: { color: "#1C3144" },
        });
        setLoading(false);
        return;
      }

      Toast.show({
        type: "success",
        text1: "Activité mise à jour avec succès",
        text1Style: { color: "#1C3144" },
      });

      router.back();
    } catch (err) {
      console.error("Erreur lors de la mise à jour :", err);
      Toast.show({
        type: "error",
        text1: "Une erreur est survenue",
        text1Style: { color: "#1C3144" },
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={{ flex: 1 }}>
      <View style={styles.mainContainer}>
        <ScrollView style={styles.container}>
          <View style={styles.containerContent}>
            <View style={styles.registerInfo}>
              <View style={styles.inputs}>
                {inputs.map((input, index) => {
                  if (input?.type === "Process") {
                    if (process_id) {
                      return (
                        <View key={index}>
                          <Text style={styles.label}>{input.label}</Text>
                          <View
                            style={{
                              width: "100%",
                              borderRadius: 4,
                              borderStyle: "solid",
                              borderWidth: 1,
                              borderColor: "#d3d3d3",
                              padding: 10,
                            }}
                          >
                            <Text>{process_name}</Text>
                          </View>
                        </View>
                      );
                    } else {
                      return (
                        <View key={index}>
                          <CustomizedSelect
                            name={input.name}
                            label={input.label}
                            register={register}
                            control={control}
                            errors={errors}
                            setError={setError}
                            data={input.data as any}
                          />
                        </View>
                      );
                    }
                  } else {
                    return (
                      <View key={index}>
                        <InputCustomized
                          label={input.label}
                          placeholder={input.placeholder}
                          register={register}
                          name={input.name}
                          control={control}
                          errors={errors}
                          setError={setError}
                          multiline={input.multiline}
                        />
                      </View>
                    );
                  }
                })}
              </View>
            </View>

            {imageUri && (
              <Image
                source={{ uri: imageUri }}
                style={{
                  width: 150,
                  height: 150,
                  marginVertical: 20,
                  borderRadius: 150,
                }}
              />
            )}

            <View style={styles.buttons}>
              <ContainedButton
                label="Modifier"
                backgroundColor="#F99527"
                onPress={handleSubmit(handleUpdateActivity)}
                disabled={loading}
              />
            </View>
          </View>
        </ScrollView>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    alignItems: 'center',
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  containerContent: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    width: "100%",
    gap: 20,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
    marginTop: 20,
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
    textAlign: "left",
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
});
