import Toast from "react-native-toast-message";
import { supabase } from "./supabase.native";

export default async function uploadEquipmentDocumentToStorage(
  fileUri: string,
  userId: string,
) {
  try {
    const response = await fetch(fileUri);
    const blob = await response.blob();
    const fileName = `${userId}_${Date.now()}equipment.pdf`;
    const arrayBuffer = await new Response(blob).arrayBuffer();
    const { data: dataPicture, error } = await supabase.storage
      .from("images")
      .upload(`equipments/${fileName}`, arrayBuffer, {
        contentType: "application/pdf",
        upsert: false,
      });

    if (error) {
      console.error("Upload error:", error);
      Toast.show({
        type: "error",
        text1: "Une erreur est survenue lors de l'upload",
        text1Style: { color: "#1C3144" },
      });
      return null;
    }

    // Générer une URL signée pour accéder au fichier
    if (dataPicture.id) {
      const { data } = supabase.storage
        .from("images")
        .getPublicUrl(`equipments/${fileName}`);

      if (error) {
        console.error("Signed URL error:", error);

        Toast.show({
          type: "error",
          text1: "Une erreur est survenue",
          text1Style: { color: "#1C3144" },
        });
        return null;
      }
      return { url: data?.publicUrl, idPicture: dataPicture?.id };
    }
  } catch (err) {
    console.error("Error uploading file:", err);
    Toast.show({
      type: "error",
      text1: "Une erreur est survenue lors de l'upload",
      text1Style: { color: "#1C3144" },
    });
    return null;
  }
}
