/*
app/register.tsx

Formulaire pour se créer un compte/s'inscrire

Informations pertinentes :

- Ajout des informations utilisateur** dans la table `users` :
     - L'utilisateur reçoit un `uid` unique.
     - Le statut de l'utilisateur est `Admin`.
     - Un profil par défaut (`Responsable QSE`) est attribué.
- Création de l'entreprise associée** dans la table `companies` :
     - L'utilisateur est défini comme créateur de l'entreprise (`uid_user`).
- Mise à jour de l'utilisateur :
     - La colonne `company_id` est renseignée avec l'ID de l'entreprise nouvellement créée.
- Ajout de l'utilisateur dans la table de jointure `company_users` pour associer l'entreprise et l'utilisateur.
- Mise à jour de l'état global** avec les informations de session (`setSession`) et de l'utilisateur (`setUser`).
- Redirection vers l'accueil.
- ⚠️ Gestion des erreurs :
  - Si un problème survient à une étape, une alerte `Toast` est affichée.
  - En cas d'échec à l'étape 2 (`users`), l'utilisateur est supprimé de `supabase.auth`.
*/


import ContainedButton from "@/components/ui/buttons/ContainedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useRouter } from "expo-router";
import {
  StyleSheet,
  Image,
  View,
  Text,
  KeyboardAvoidingView,
  Keyboard,
  Platform,
  ScrollView,
  TouchableWithoutFeedback,
} from "react-native";
import { supabase } from "../lib/supabase.native";
import { useContext, useState } from "react";
import { useForm } from "react-hook-form";
import { Button, ButtonText } from "@/components/ui/button";
import TypeRegister from "@/types/register";
import { useNavigation } from "@react-navigation/native";
import { AppContext } from "@/state/AppContext";
import Toast from "react-native-toast-message";

const inputs = [
  { label: "Nom", name: "last_name", placeholder: "Ecrire ton nom" },
  { label: "Prénom", name: "first_name", placeholder: "Ecrire ton prénom" },
  {
    label: "Entreprise",
    name: "company_name",
    placeholder: "Ecrire ton entreprise",
  },
  { label: "Email", name: "email", placeholder: "Ecrire ton email" },
  {
    label: "Mot de passe",
    name: "password",
    placeholder: "Ecrire ton mot de passe",
  },
  {
    label: "Confirmer le mot de passe",
    name: "password_confirmation",
    placeholder: "Confirmer le mot de passe",
  },
];

export default function Register() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const { setSession, setUser, setCompanySelected } = useContext(AppContext);
  const navigation = useNavigation<any>();
  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
    control,
    reset,
  } = useForm<TypeRegister>({
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      password: "",
      password_confirmation: "",
    },
  });

  //
  const signUpWithEmail = async (data: TypeRegister) => {
    if (
      data.first_name &&
      data.last_name &&
      data.email &&
      data.password &&
      data.password_confirmation
    ) {
      setLoading(true);

      try {
        // Étape 1 : Inscription avec Supabase Auth
        const { data: sessionData, error: signUpError } =
          await supabase.auth.signUp({
            email: data.email,
            password: data.password,
          });

        if (signUpError) {
          Toast.show({
            type: "error",
            text1: "Erreur lors de l'enregistrement de l'utilisateur",
            text1Style: { color: "#1C3144" },
          });
          setLoading(false);
          return;
        }

        const userId = sessionData.user?.id;

        if (userId) {
          // Étape 2 : Enregistrer les données utilisateur dans la table "users"
          const created_at = new Date();

          const { error: insertError, data: userCreated } = await supabase
            .from("users")
            .insert([
              {
                uid: userId,
                first_name: data.first_name,
                last_name: data.last_name,
                email: data.email,
                status: "Admin",
                created_at: created_at,
                profil_picture: "",
                profil: "Responsable QSE",
              },
            ]);

          if (insertError) {
            // Étape 3 : Suppression de l'utilisateur dans Auth en cas d'échec
            await supabase.auth.admin.deleteUser(userId);

            Toast.show({
              type: "error",
              text1: "Erreur lors de l'enregistrement des données utilisateur",
              text1Style: { color: "#1C3144" },
            });

            setLoading(false);
            setUser(null);
            return;
          }

          // Étape 4 : Création de l'entreprise associée à l'utilisateur
          const { error: insertCompanyError, data: companyCreated } =
            await supabase
              .from("companies")
              .insert([
                {
                  uid_user: userId,
                  company_name: data.company_name,
                  created_at: created_at,
                },
              ])
              .select();

          if (insertCompanyError) {
            console.error(
              "Erreur lors de la création de l'entreprise :",
              insertCompanyError.message
            );
            Toast.show({
              type: "error",
              text1: "Erreur lors de la création de l'entreprise",
              text1Style: { color: "#1C3144" },
            });
            setLoading(false);
            return;
          }

          // Étape 5 : Mettre à jour la colonne `company_id` de l'utilisateur avec l'ID de l'entreprise créée
          // if (companyCreated.length > 0) {
          //   const { error: updateUserError } = await supabase
          //     .from("users")
          //     .update({ company_id: companyCreated[0].id }) // Mise à jour de `company_id`
          //     .eq("uid", userId);

          //   if (updateUserError) {
          //     console.error(
          //       "Erreur lors de la mise à jour du company_id de l'utilisateur :",
          //       updateUserError.message
          //     );
          //     Toast.show({
          //       type: "error",
          //       text1: "Erreur lors de la mise à jour du profil utilisateur",
          //       text1Style: { color: "#1C3144" },
          //     });
          //   }
          // }

          // Étape 6 : Mettre à jour l'état global et rediriger l'utilisateur
          setUser({
            uid: userId,
            first_name: data.first_name,
            last_name: data.last_name,
            email: data.email,
            status: "Admin",
            created_at: created_at,
            profil_picture: "",
            companies: companyCreated || [],
            profil: "Responsable QSE",
          });
          setSession(sessionData.session);

                if (sessionData.session) {
                  setSession(sessionData.session);
                }
          
                const { data: user, error: userError } = await supabase
                  .from("users")
                  .select("*, companies(*)")
                  .eq("uid", sessionData.user?.id)
                  .single();
          
                if (userError) {
                  console.error("User fetch error:", userError);
                  Toast.show({
                    type: "error",
                    text1: "Une erreur est survenue",
                    text1Style: { color: "#1C3144" },
                  });
                  return;
                }



          setCompanySelected(user?.companies[0]);
          console.log(user?.companies[0])
          router.navigate("/");
          reset();
        }
      } catch (error) {
        console.log("Erreur inattendue :", error);
        Toast.show({
          type: "error",
          text1: "Erreur lors de l'enregistrement des données utilisateur",
          text1Style: { color: "#1C3144" },
        });
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      keyboardVerticalOffset={Platform.select({ ios: 0, android: 100 })}
    >
      <ScrollView style={styles.form}>
        <Toast />
        <View style={styles.webContainer}>
          <View style={styles.containerContent}>
            <Image
              source={require("@/assets/images/login/logoIZYS.png")}
              style={styles.imgIsLoadingPage}
            />
            <View style={styles.registerInfo}>
              <Text
                style={
                  {
                    color: "black",
                    fontSize: 24,
                    alignSelf: "left",
                  } as any
                }
              >
                S'inscrire
              </Text>

              <View style={styles.inputs}>
                {inputs.map((input, index) => {
                  return (
                    <View key={index}>
                      <InputCustomized
                        label={input.label}
                        placeholder={input.placeholder}
                        register={register}
                        name={input.name}
                        control={control}
                        errors={errors}
                        setError={setError}
                      />
                    </View>
                  );
                })}
              </View>
            </View>
            <View style={styles.buttons}>
              <ContainedButton
                label="S'inscrire"
                backgroundColor="#F99527"
                onPress={handleSubmit(signUpWithEmail)}
                disabled={loading}
              />
            </View>
            <View style={{ flexDirection: "row", alignItems: "center", gap: 10 }}>
              <Text>Vous avez déjà un compte?</Text>
              <Button variant="link" size="sm" className="p-0">
                <ButtonText onPress={() => navigation.navigate("login")}>
                  Se connecter
                </ButtonText>
              </Button>
            </View>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    height: "100%",
    width: "100%",
    backgroundColor: "#fff",
    alignItems: "center",
    alignContent: "center",
  },
  form: {
    width: "100%",
    marginVertical: Platform.OS === "web" ? 0 : 20,
  },
  webContainer: {
    width: "100%",
    alignItems: "center",
  },
  containerContent: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    paddingHorizontal: 20,
    gap: 20,
    marginTop: Platform.OS === "web" ? "5%" : "15%",
    width: Platform.OS === "web" ? 400 : "100%",
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    width: "100%",
    gap: 20,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  titleContainer: {
    flexDirection: "row",
    gap: 8,
  },
  imgIsLoadingPage: {
    height: 75,
    width: 130,
    alignSelf: "center",
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
  input: {
    backgroundColor: "white",
    borderColor: "none",
    height: 40,
    padding: 10,
    borderRadius: 4,
  },
});
