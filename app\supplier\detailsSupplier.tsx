/*
app/supplier/detailsSupplier.tsx

Page de détails d'un fournisseur.

Informations pertinentes :

- Les informations du fournisseur sont récupérées depuis les paramètres de navigation
- Les événements sont récupérés depuis la table `events`. Les événements attribués au fournisseurs spécifique sont dans la colonne `administrator` de la table `events`
*/

import React, { useContext, useEffect, useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  FlatList,
  Platform,
} from "react-native";
import {
  ParamListBase,
  RouteProp,
  useNavigation,
  useRoute,
  useFocusEffect,
} from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AppContext } from "@/state/AppContext";
import Header from "@/components/common/Header";
import moment from "moment";
import { supabase } from "@/lib/supabase";
import TypeSupplier from "@/types/typeSupplier";
import { useLocalSearchParams } from "expo-router";
import { useRouter } from "expo-router";
import TypeEvent from "@/types/typeEvent";
import PaperInfoImgEvent from "@/components/common/paperInfoImgEvent";

// Données de navigation
type DetailSupplierRouteParams = {
  supplier: {
    id: number;
    uid: string;
    company_name: string;
    last_name: string;
    first_name: string;
    phone_number: string;
    status: string;
    created_at: string;
  };
};

export default function detailsSupplier() {
  const route =
    useRoute<RouteProp<{ params: DetailSupplierRouteParams }, "params">>();
  const params = useLocalSearchParams();
  const router = useRouter();
  const [supplier, setSupplier] = useState<any>(null);
  const [eventList, setEventList] = useState<any>([]);
  const { user } = useContext(AppContext);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  // Fonction pour récupérer les informations du fournisseur
  const fetchSupplierDetails = async () => {
    try {
      if (!params.uid) {
        console.error("Aucun UID fourni");
        return;
      }

      // Récupérer les informations de la table suppliers
      const { data: supplierData, error: supplierError } = await supabase
        .from("suppliers")
        .select("*")
        .eq("uid_user", params.uid)
        .single();

      if (supplierError) {
        console.error(
          "Erreur lors de la récupération des informations fournisseur :",
          supplierError
        );
        return;
      }

      // Récupérer les informations de la table users
      const { data: userData, error: userError } = await supabase
        .from("users")
        .select("*")
        .eq("uid", params.uid)
        .single();

      if (userError) {
        console.error(
          "Erreur lors de la récupération des informations utilisateur :",
          userError
        );
        return;
      }

      // Combiner les informations
      const combinedData = {
        ...supplierData,
        first_name: userData.first_name,
        last_name: userData.last_name,
        phone_number: userData.phone_number,
        email: userData.email,
      };

      setSupplier(combinedData);
    } catch (err) {
      console.error("Erreur inattendue :", err);
    }
  };

  // Récupérer les informations du fournisseur au chargement et après chaque focus
  useFocusEffect(
    React.useCallback(() => {
      fetchSupplierDetails();
      fetchSupplierEvents();
    }, [])
  );

  // Fonction pour récupérer les événements du fournisseur
  const fetchSupplierEvents = async () => {
    try {
      if (!params.uid) {
        console.error("Aucun UID fourni");
        return;
      }

      const { data: eventsData, error: eventsError } = await supabase
        .from("events")
        .select(`
          id,
          date,
          wording,
          description,
          causes,
          file
        `)
        .eq("administrator", params.uid)
        .order("date", { ascending: false });

      if (eventsError) {
        console.error(
          "Erreur lors de la récupération des événements :",
          eventsError
        );
        return;
      }

      setEventList(eventsData || []);
    } catch (err) {
      console.error("Erreur inattendue lors de la récupération des événements :", err);
    }
  };

  // Restrictions selon le statut
  useEffect(() => {
    if (!user) return;

    if (["User", "Extern", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const handlUpdateIcon = () => {
    router.push({
      pathname: "/supplier/updateSupplier",
      params: {
        id: params.id,
        supplierId: params.uid,
        company_name: supplier?.company_name,
        last_name: supplier?.last_name,
        first_name: supplier?.first_name,
        status: supplier?.status,
        phone_number: supplier?.phone_number,
      },
    });
  };

  return (
    <>
      <Header onPressIcon={() => handlUpdateIcon()} />

      <View style={styles.mainContainer}>
        <ScrollView style={styles.container}>
          <View style={styles.containerContent}>
            <View style={styles.supplierCard}>
              <View style={styles.supplierInfo}>
                <Text style={styles.companyName}>{supplier?.company_name}</Text>
                <Text style={styles.cardName}>
                  {supplier?.last_name} {supplier?.first_name}
                </Text>
                <Text style={styles.contactInfo}>{supplier?.phone_number}</Text>
                <Text style={styles.contactInfo}>{supplier?.email}</Text>
              </View>
              {user?.status === "Admin" && (
                <View style={styles.statusContainer}>
                  <Text
                    style={[
                      styles.statusText,
                      {
                        backgroundColor:
                          supplier?.status === "good"
                            ? "#2FC12B"
                            : supplier?.status === "regular"
                            ? "#F39415"
                            : "#1C3144",
                      },
                    ]}
                  >
                    {supplier?.status === "good"
                      ? "Bon"
                      : supplier?.status === "regular"
                      ? "Moyen"
                      : "Mauvais"}
                  </Text>
                </View>
              )}
            </View>

            <Text style={styles.eventsTitle}>Événements associés</Text>

            {eventList.map((item: any) => (
              <View key={item.id} style={styles.itemContainer}>
                <PaperInfoImgEvent
                  title={
                    item.wording.length > 30
                      ? item.wording.slice(0, 30) + "..."
                      : item.wording
                  }
                  text1={
                    item.description
                      ? item.description.length > 90
                        ? item.description.slice(0, 90) + "..."
                        : item.description
                      : "Pas de description"
                  }
                  circle1={item?.immediateActions?.length > 0}
                  circle2={item?.causes !== "" && item.causes !== null}
                  circle3={item?.correctiveActions?.length > 0}
                  imgSrc={item?.file?.length ? item.file[0] : ""}
                  onPressPen={() =>
                    router.push({
                      pathname: "/event/updateEvent",
                      params: {
                        date: item.date,
                        wording: item.wording,
                        causes: item.causes,
                        idEvent: item.id,
                        description: item.description,
                      },
                    })
                  }
                  onPressEye={() =>
                    router.push({
                      pathname: "/event/detailsEvent",
                      params: {
                        date: item.date,
                        wording: item.wording,
                        causes: item.causes,
                        idEvent: item.id,
                        description: item.description,
                      },
                    })
                  }
                />
              </View>
            ))}
            {eventList.length === 0 && (
              <Text style={styles.emptyText}>Aucun événement disponible.</Text>
            )}
          </View>
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  containerContent: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  supplierCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  supplierInfo: {
    flex: 1,
    gap: 5,
  },
  companyName: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1C3144",
  },
  cardName: {
    fontSize: 16,
    color: "#525252",
  },
  contactInfo: {
    fontSize: 14,
    color: "#525252",
  },
  statusContainer: {
    marginLeft: 10,
  },
  statusText: {
    fontSize: 12,
    color: "#FFFFFF",
    padding: 8,
    borderRadius: 50,
    textAlign: "center",
    minWidth: 100,
  },
  eventsTitle: {
    fontWeight: "bold",
    fontSize: 16,
    marginBottom: 15,
    color: "#1C3144",
  },
  itemContainer: {
    marginBottom: 15,
  },
  emptyText: {
    textAlign: "center",
    marginTop: 20,
    fontSize: 16,
    color: "gray",
  },
});
