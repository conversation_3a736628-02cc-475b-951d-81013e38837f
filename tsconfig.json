{"compilerOptions": {"strict": true, "jsx": "react-native", "target": "ESNext", "module": "ESNext", "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "baseUrl": ".", "paths": {"@/*": ["./*"], "expo-router/app/*": ["./app/*"], "tailwind.config": ["./tailwind.config.js"]}}, "include": ["app/**/*.ts", "app/**/*.tsx", "types/**/*.ts", "types/**/*.tsx", "expo-env.d.ts", "nativewind-env.d.ts", ".expo/types/**/*.ts", "lib/**/*.ts", "lib/**/*.tsx", "components/**/*.ts", "components/**/*.tsx"], "exclude": ["node_modules", "babel.config.js", "metro.config.js"], "extends": "expo/tsconfig.base"}