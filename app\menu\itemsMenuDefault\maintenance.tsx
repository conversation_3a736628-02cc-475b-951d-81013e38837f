/*
app/menu/itemsMenuDefault/maintenance.tsx

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `Supplier` n'ont pas accès à cette page.
*/

import React from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  TextInput,
  Platform,
} from "react-native";
import { useFocusEffect, useRouter } from "expo-router";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { useCallback, useContext, useEffect, useState } from "react";
import { Spinner } from "@/components/ui/spinner";
import { supabase } from "@/lib/supabase";
import { AppContext } from "@/state/AppContext";
import { Octicons } from "@expo/vector-icons";
import FilterPage from "@/components/common/filterPage";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import PaperInfoImg from "@/components/common/paperInfoImg";
import FontAwesome from "@expo/vector-icons/FontAwesome";

interface TypeProduct {
  id: number;
  name: string;
  description: string;
  image: string;
  status: boolean;
  uid_user: string;
}

const filterOptions = [
  { label: "En service", value: "en_service" },
  { label: "Hors service", value: "hors_service" },
];

export default function Maintenance() {
  const router = useRouter();
  const [data, setData] = useState<TypeProduct[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { user, companySelected } = useContext(AppContext);
  const [showFilter, setShowFilter] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
  const [filteredData, setFilteredData] = useState<TypeProduct[]>([]);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  const fetchData = async () => {
    setIsLoading(true);

    const { data: dataGetted, error } = await supabase
      .from("products")
      .select("*")
      .eq("uid_user", user?.uid);

    if (!error) {
      setData(dataGetted as any);
    }
    setIsLoading(false);
  };

  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [])
  );

  useEffect(() => {
    if (!companySelected?.id) {
      console.error("❌ Aucun company_id défini, requête annulée !");
      return;
    }

    fetchData();

    // ✅ Écoute les changements en temps réel et rafraîchit la liste
    const channel = supabase
      .channel("products-changes")
      .on(
        "postgres_changes",
        { event: "*", schema: "public", table: "products" },
        (payload: any) => {
          console.log("📌 Changement détecté dans products :", payload);
          setRefreshKey((prev) => prev + 1); // 🔥 Force un re-render en incrémentant `refreshKey`
        }
      )
      .subscribe(async (status: boolean) => {
        if (status === true) {
          console.log("✅ Abonné aux changements de la table `products` !");
        }
      });

    return () => {
      // Nettoyer l'écouteur lorsqu'on quitte la page
      supabase.removeChannel(channel);
    };
  }, [companySelected, refreshKey]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["User", "Supplier"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  // Mettre à jour la liste filtrée en fonction de la recherche et des filtres
  useEffect(() => {
    let filtered = data;
    
    // Filtre par recherche
    if (searchQuery) {
      filtered = filtered.filter((item) =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    
    // Filtre par statut
    if (selectedFilters.length > 0) {
      filtered = filtered.filter((item) => {
        if (selectedFilters.includes("en_service") && item.status) return true;
        if (selectedFilters.includes("hors_service") && !item.status) return true;
        return false;
      });
    }
    
    setFilteredData(filtered);
  }, [searchQuery, selectedFilters, data]);

  const toggleFilter = (value: string) => {
    setSelectedFilters((prevSelected) =>
      prevSelected.includes(value)
        ? prevSelected.filter((item) => item !== value)
        : [...prevSelected, value]
    );
  };

  return (
    <View style={styles.mainContainer}>
      <ScrollView style={styles.container}>
        {showFilter ? (
          <View>
            <FilterPage
              title="Filtres des matériels"
              filters={selectedFilters.reduce((acc, filter) => {
                acc[filter] = true;
                return acc;
              }, {} as Record<string, boolean>)}
              setFilters={(newFilters) => {
                const selected = Object.entries(newFilters)
                  .filter(([_, value]) => value)
                  .map(([key]) => key);
                setSelectedFilters(selected);
              }}
              filterLabels={filterOptions.reduce((acc, filter) => {
                acc[filter.value] = filter.label;
                return acc;
              }, {} as Record<string, string>)}
            />
            <TouchableOpacity 
              style={styles.closeFilterButton}
              onPress={() => setShowFilter(false)}
            >
              <Text style={styles.closeFilterButtonText}>Fermer les filtres</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.content}>
            <View style={styles.header}>
              <Text style={{ fontWeight: "bold" }}>
                Créer un nouveau matériel:
              </Text>
              <TouchableOpacity
                onPress={() => router.navigate("/product/createProduct")}
              >
                <Octicons name="diff-added" size={25} color="#F99527" />
              </TouchableOpacity>
            </View>

            <View style={styles.searchContainer}>
              <View style={styles.searchInputWrapper}>
                <FontAwesome
                  name="search"
                  size={24}
                  color="#3C3c4399"
                  style={styles.searchIcon}
                />
                <TextInput
                  style={styles.searchInput}
                  placeholder="Rechercher par nom..."
                  placeholderTextColor="#3C3c4399"
                  value={searchQuery}
                  onChangeText={(text) => setSearchQuery(text)}
                />
              </View>
            </View>

            <View style={styles.filterContainer}>
              <Text style={styles.filterLabel}>Filtrer par statut</Text>
              <View style={styles.filterButtons}>
                {filterOptions.map((filter) => (
                  <TouchableOpacity
                    key={filter.value}
                    style={[
                      styles.filterButton,
                      selectedFilters.includes(filter.value) && styles.filterButtonSelected,
                    ]}
                    onPress={() => toggleFilter(filter.value)}
                  >
                    <Text
                      style={
                        selectedFilters.includes(filter.value)
                          ? styles.filterTextSelected
                          : styles.filterText
                      }
                    >
                      {filter.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.users}>
              {isLoading ? (
                <Spinner size="small" color="#1C3144" />
              ) : filteredData.length > 0 ? (
                filteredData.map((item, index) => (
                  <PaperInfoImg
                    key={index}
                    title={item.name}
                    badge={item.status ? "En service" : "Hors service"}
                    badgeColor={item.status ? "#2FC12B" : "#B91C1C"}
                    text2={item.description}
                    imgSrc={item.image}
                    onPress={() =>
                      router.push({
                        pathname: "/product/detailsProduct",
                        params: {
                          product_id: item.id,
                          name: item.name,
                          description: item.description,
                          image: item.image,
                          status: item.status as any,
                        },
                      })
                    }
                  />
                ))
              ) : (
                <Text style={styles.emptyText}>Aucun matériel crée</Text>
              )}
            </View>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  content: {
    gap: 20,
    padding: 20,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 14,
  },
  users: {
    gap: 10,
  },
  searchContainer: {
    width: "100%",
    marginBottom: 20,
  },
  searchInputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1C314414",
    borderRadius: 8,
    paddingHorizontal: 10,
    height: 60,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    padding: 10,
    fontSize: 16,
    color: "#000000",
  },
  filterContainer: {
    width: "100%",
    marginBottom: 20,
  },
  filterLabel: {
    fontSize: 14,
    color: "#262627",
    marginBottom: 10,
    fontWeight: "bold",
  },
  filterButtons: {
    display: "flex",
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 10,
  },
  filterButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "#F99527",
  },
  filterButtonSelected: {
    backgroundColor: "#F99527",
    borderColor: "#F99527",
  },
  filterText: {
    color: "#F99527",
  },
  filterTextSelected: {
    color: "white",
  },
  closeFilterButton: {
    backgroundColor: '#F99527',
    padding: 15,
    borderRadius: 8,
    margin: 10,
    alignItems: 'center',
  },
  closeFilterButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  emptyText: {
    fontSize: 14,
    color: '#525252',
    textAlign: 'center',
    padding: 20,
  },
}); 