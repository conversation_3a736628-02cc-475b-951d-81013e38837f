import { supabase } from "@/lib/supabase";
import { AppContext } from "@/state/AppContext";
import TypeELearning from "@/types/typeELearning";
import { Ionicons } from "@expo/vector-icons";
import { router, useFocusEffect } from "expo-router";
import { useCallback, useContext, useState, useEffect } from "react";
import { Dimensions, Platform } from "react-native";
import {
  TouchableOpacity,
  View,
  FlatList,
  StyleSheet,
  Text,
  Image,
  ActivityIndicator,
} from "react-native";
import Header from "@/components/common/Header";

export default function ListELearningPdf() {
  const [eLearningsPdf, setELearningsPdf] = useState<TypeELearning[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { width } = Dimensions.get("window");
  const { user, companySelected } = useContext(AppContext);
  const [viewStatus, setViewStatus] = useState<{ [key: number]: boolean }>({});
  const [clickStatus, setClickStatus] = useState<{ [key: number]: boolean }>({});

  type typeRender = {
    item: TypeELearning;
  };

  const fetchDataELearnings = async () => {
    if (!user?.uid) return;
    
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from("elearnings")
        .select()
        .eq("uid_user", user.uid)
        .eq("file_type", "pdf");

      if (!error) {
        setELearningsPdf(data as any);
      } else {
        console.error('Erreur lors du chargement des PDFs:', error);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des PDFs:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getViewStatus = async (itemId: number) => {
    const { data } = await supabase
      .from("elearnings_students")
      .select("view_at")
      .eq("elearning_id", itemId)
      .eq("user_id", user?.uid);
    
    return data?.[0]?.view_at != null;
  };

  const getClickStatus = async (itemId: number) => {
    const { data } = await supabase
      .from("elearnings_students")
      .select("click_at")
      .eq("elearning_id", itemId)
      .eq("user_id", user?.uid);
    
    return data?.[0]?.click_at != null;
  };

  useEffect(() => {
    const fetchStatus = async () => {
      const newViewStatus: { [key: number]: boolean } = {};
      const newClickStatus: { [key: number]: boolean } = {};
      
      for (const item of eLearningsPdf) {
        newViewStatus[item.id] = await getViewStatus(item.id);
        newClickStatus[item.id] = await getClickStatus(item.id);
      }
      
      setViewStatus(newViewStatus);
      setClickStatus(newClickStatus);
    };
    
    fetchStatus();
  }, [eLearningsPdf]);

  // Charger les données au montage initial du composant
  useEffect(() => {
    if (user?.uid) {
      fetchDataELearnings();
    }
  }, [user?.uid]);

  // Recharger les données quand la page redevient active
  useFocusEffect(
    useCallback(() => {
      if (user?.uid) {
        fetchDataELearnings();
      }
    }, [user?.uid])
  );

  const renderItem = ({ item }: typeRender) => {
    const handlePress = async () => {
      const { error } = await supabase.from("elearnings_students").insert({
        elearning_id: item.id,
        user_id: user?.uid,
        view_at: new Date().toISOString(),
        company_id: companySelected?.id
      });

      if (error) {
        console.error('Erreur lors de l\'enregistrement de la vue:', error);
      }

      router.push({
        pathname: "/content/oneELearningPdf",
        params: {
          id: item.id,
          name: item.title,
          url_media: item.url_media,
          url_mignature: item.url_mignature,
        },
      });
    };

    return (
      <TouchableOpacity
        style={styles.card}
        onPress={handlePress}
      >
        <View style={{ width: width * 0.9 }}>
          {Platform.OS === "web" ? (
            <iframe
              src={item.url_media}
              style={{
                width: "100%",
                height: 300,
                border: "none",
                borderRadius: 10,
                backgroundColor: '#fff',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              }}
            />
          ) : (
            <Image
              source={{ uri: item.url_media }}
              style={{
                width: "100%",
                height: 300,
                borderRadius: 10,
              }}
            />
          )}

          <View style={styles.overlay}>
            <Text style={styles.title}>{item.title}</Text>
            <Text style={styles.description}>{item.description}</Text>

            <View style={styles.checkContainer}>
              <Ionicons
                name="checkmark-done-sharp"
                size={20}
                color={viewStatus[item.id] && clickStatus[item.id] ? "#34C759" : "gray"}
                style={styles.checkDoubl}
              />
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View>
      <Header
        title="PDF"
        onPressIcon={() => router.navigate("/content/createELearning")}
        onPressFlesh={() => router.navigate("/content/elearning")}
      />
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#F99527" />
        </View>
      ) : eLearningsPdf?.length > 0 ? (
        <FlatList
          data={eLearningsPdf}
          renderItem={renderItem}
          keyExtractor={(item) => item.id as any}
          showsHorizontalScrollIndicator={false}
          snapToAlignment="start"
          snapToInterval={width * 0.8}
          decelerationRate="fast"
          contentContainerStyle={{ paddingBottom: 300 }}
        />
      ) : (
        <Text style={{ padding: 10 }}>
          Vous avez pas encore crée ce contenu !
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "column",
    gap: 40,
    marginBottom: 200,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 10,
  },
  card: {
    borderRadius: 10,
    overflow: "hidden",
    backgroundColor: "#fff",
    elevation: 3,
    padding: 10,
    margin: 10,
    paddingBottom: 0,
  },
  image: {
    width: "100%",
    height: 150,
    borderRadius: 10,
  },
  overlay: {
    padding: 10,
  },
  title: {
    fontSize: 16,
    fontWeight: "bold",
    alignSelf: "center",
  },
  description: {
    fontSize: 14,
    color: "#666",
    alignSelf: "center",
  },
  checkDoubl: {
    alignSelf: "flex-end",
  },
  checkContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 5,
  },
  check: {
    width: 20,
    height: 20,
    borderRadius: 10,
    margin: 5,
  },
  checkGreen: {
    backgroundColor: "#34C759",
  },
  checkRed: {
    backgroundColor: "gray",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
});
