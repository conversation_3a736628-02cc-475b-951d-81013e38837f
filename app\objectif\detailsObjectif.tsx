import ContainedButton from "@/components/ui/buttons/ContainedButton";
import OutlinedButton from "@/components/ui/buttons/OutlinedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useRouter, useLocalSearchParams, useFocusEffect } from "expo-router";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Platform,
  Alert,
} from "react-native";

import { useCallback, useContext, useState } from "react";
import { Octicons } from "@expo/vector-icons";
import { AppContext } from "@/state/AppContext";
import PaperInfo from "@/components/common/paperInfo";
import { supabase } from "@/lib/supabase";
import Header from "@/components/common/Header";
import TypeValue from "@/types/typeValue";
import { Dimensions } from "react-native";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";


const screenWidth = Dimensions.get("window").width; // ✅ Récupère la largeur de l'écran


const inputs = [
  // {
  //   label: "Designation",
  //   name: "designation",
  //   placeholder: "Ecrire la designation",
  // },
  // {
  //   label: "Cible",
  //   name: "target",
  //   placeholder: "Ecrire la cible",
  // },
  // {
  //   label: "Indicateur de mesure",
  //   name: "measuring_indicator",
  //   placeholder: "Indicateur de mesure",
  // },
  //
];

export default function detailsObjectif() {
  const { user } = useContext(AppContext);

  const [values, setValues] = useState<TypeValue[]>([]);

  const router = useRouter();

      const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  

  const {
    uid_user,
    target,
    designation,
    measuring_indicator,
    cible,
    process_id,
    level,
    created_at,
    company_name,
    objectif_id,
    company_id,
    // values,
  } = useLocalSearchParams();

  const [objectifData, setObjectifData] = useState({
    target: target,
    designation: designation,
    measuring_indicator: measuring_indicator,
    level: level,
  });

  const fetchData = async () => {
    const { data: dataGetted, error } = await supabase
      .from("values")
      .select("*")
      .eq("objectif_id", objectif_id);

    if (!error && dataGetted) {
      // Filtrer les valeurs sans date_value
      const valuesWithDate = dataGetted.filter((v: any) => v.date_value) || [];
      const valuesWithoutDate = dataGetted.filter((v: any) => !v.date_value) || [];
      
      // Trier les valeurs avec date_value par date décroissante
      const sortedValuesWithDate = valuesWithDate.sort((a: any, b: any) => 
        new Date(b.date_value).getTime() - new Date(a.date_value).getTime()
      );
      
      // Trier les valeurs sans date_value par created_at décroissant
      const sortedValuesWithoutDate = valuesWithoutDate.sort((a: any, b: any) => 
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
      
      // Combiner les deux listes triées
      const allSortedValues = [...sortedValuesWithDate, ...sortedValuesWithoutDate];
      
      setValues(allSortedValues);
    }
  };

  const fetchObjectifData = async () => {
    const { data, error } = await supabase
      .from("objectifs")
      .select("*")
      .eq("id", objectif_id)
      .single();

    if (!error && data) {
      setObjectifData({
        target: data.target,
        designation: data.designation,
        measuring_indicator: data.measuring_indicator,
        level: data.level,
      });
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchData();
      fetchObjectifData();

      // Écoute les changements en temps réel pour les valeurs
      const valuesChannel = supabase
        .channel("values-changes")
        .on(
          "postgres_changes",
          { 
            event: "*", 
            schema: "public", 
            table: "values",
            filter: `objectif_id=eq.${objectif_id}`
          },
          () => {
            fetchData();
          }
        )
        .subscribe();

      // Écoute les changements en temps réel pour l'objectif
      const objectifChannel = supabase
        .channel("objectif-changes")
        .on(
          "postgres_changes",
          { 
            event: "*", 
            schema: "public", 
            table: "objectifs",
            filter: `id=eq.${objectif_id}`
          },
          () => {
            fetchObjectifData();
          }
        )
        .subscribe();

      return () => {
        supabase.removeChannel(valuesChannel);
        supabase.removeChannel(objectifChannel);
      };
    }, [objectif_id])
  );

  function formatDate(dateString: string) {
    const date = new Date(dateString);
    return date.toLocaleDateString("fr-FR");
  }

  const handlUpdateIcon = () => {
    router.push({
      pathname: "/objectif/updateObjectif",
      params: {
        uid_user: user?.uid,
        target: objectifData.target,
        designation: objectifData.designation,
        measuring_indicator: objectifData.measuring_indicator,
        process_id: process_id,
        level: objectifData.level,
        created_at: created_at,
        company_id: company_id,
        company_name: company_name,
        objectif_id: objectif_id,
        value_latest: values[values.length - 1]?.value_name,
      },
    });
  };

  // 🔥 Fonction de suppression d'un Objectif et ses Values associées
  const handleDeleteObjectif = async () => {
    const confirmDelete =
      Platform.OS === "web"
        ? window.confirm("Êtes-vous sûr de vouloir supprimer cet Objectif et ses valeurs associées ?")
        : await new Promise((resolve) =>
            Alert.alert(
              "Confirmation",
              "Êtes-vous sûr de vouloir supprimer cet Objectif et ses valeurs associées ?",
              [
                { text: "Annuler", style: "cancel", onPress: () => resolve(false) },
                { text: "Oui, supprimer", onPress: () => resolve(true) },
              ]
            )
          );

    if (!confirmDelete) return;

    try {
      // 🔥 1. Supprimer toutes les Values associées
      const { error: valuesError } = await supabase
        .from("values")
        .delete()
        .eq("objectif_id", objectif_id);

      if (valuesError) throw valuesError;

      console.log(`✅ Toutes les valeurs liées à l'objectif ${objectif_id} ont été supprimées.`);

      // 🔥 2. Supprimer l'Objectif lui-même
      const { error: objectifError } = await supabase
        .from("objectifs")
        .delete()
        .eq("id", objectif_id);

      if (objectifError) throw objectifError;

      alert("L'objectif et ses valeurs associées ont été supprimés avec succès.");
      navigation.goBack();
    } catch (error) {
      console.error("❌ Erreur lors de la suppression :", error);
      alert("Une erreur est survenue lors de la suppression.");
    }
  };

  return (
    <View style={styles.mainContainer}>
      <Header onPressIcon={() => handlUpdateIcon()} />
      <ScrollView style={styles.container}>
        <View style={styles.containerContent}>
          <View style={styles.registerInfo}>
            <View style={styles.inputs}>
              <View style={styles.infoContainer}>
                <View style={styles.infoText}>
                  <Text style={styles.infoLabel}>Descriptif :</Text>
                  <Text>{objectifData.designation}</Text>
                  <Text style={styles.infoLabel}>Cible :</Text>
                  <Text>{objectifData.target}</Text>
                  <Text style={styles.infoLabel}>Indicateur :</Text>
                  <Text>{objectifData.measuring_indicator}</Text>
                </View>
                <View style={styles.dateContainer}>
                  <Text numberOfLines={1} ellipsizeMode="tail">
                    {formatDate(created_at as string)}
                  </Text>
                </View>
              </View>
            </View>

            <View style={styles.valuesContainer}>
              <View style={styles.header}>
                <Text style={styles.headerText}>Historique des valeurs</Text>
                <TouchableOpacity
                  onPress={() =>
                    router.push({
                      pathname: "/objectif/createValue",
                      params: {
                        uid_user: user?.uid,
                        company_name: company_name,
                        objectif_id: objectif_id,
                        designation: objectifData.designation,
                        measuring_indicator: objectifData.measuring_indicator,
                        value_latest: values[0]?.value_name,
                        created_at: created_at,
                      },
                    })
                  }
                >
                  <Octicons name="diff-added" size={25} color="#F99527" />
                </TouchableOpacity>
              </View>
              <View style={styles.valuesList}>
                {values?.map((item: any, index: number) => (
                  <PaperInfo
                    key={index}
                    title={`Valeur: ${item.value_name}`}
                    date={item.date_value}
                    level={item.level}
                    onPress={() => 
                      router.push({
                        pathname: "/objectif/updateValue",
                        params: {
                          uid_user: user?.uid,
                          company_name: company_name,
                          objectif_id: objectif_id,
                          designation: objectifData.designation,
                          measuring_indicator: objectifData.measuring_indicator,
                          value_id: item.id,
                          value_name: item.value_name,
                          value_level: item.level,
                          created_at: created_at,
                        },
                      })
                    }
                  />
                ))}
              </View>
            </View>

            {user?.status === "Admin" && (
              <View style={styles.deleteButtonContainer}>
                <TouchableOpacity style={styles.deleteButton} onPress={handleDeleteObjectif}>
                  <Text style={styles.deleteButtonText}>Supprimer cet Objectif</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  containerContent: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    width: "100%",
    gap: 40,
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
  infoContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 10,
    flexWrap: "wrap",
  },
  infoText: {
    display: "flex",
    gap: 10,
  },
  infoLabel: {
    fontWeight: "bold",
  },
  dateContainer: {
    maxWidth: 150,
    alignItems: "flex-end",
  },
  valuesContainer: {
    gap: 10,
    width: "100%",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
  },
  headerText: {
    fontWeight: "bold",
  },
  valuesList: {
    width: "100%",
    gap: 10,
  },
  deleteButtonContainer: {
    width: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 30,
  },
  deleteButton: {
    width: 200,
    backgroundColor: "#ff4f42",
    padding: 10,
    borderRadius: 8,
    alignItems: "center",
  },
  deleteButtonText: {
    color: "#FFFFFF",
    fontWeight: "normal",
  },
});
