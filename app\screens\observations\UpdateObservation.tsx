/*
app/screens/observations/UpdateObservation.tsx

Formulaire pour modifier un constat existant.

Informations pertinentes :

- Un constat est relié à l'entreprise de l'utilisateur connecté
- Quand le formulaire est validé les données sont mises à jour dans la table `observations`
- Les constats ont les types suivants :
   -> `non_compliant` -> Constat de non-conformité
   -> `sensitive_point` -> Point sensible
   -> `progress` -> Piste de progrès
   -> `note` -> Note
- On récupère les processus de l'entreprise à afficher dans le dropdown depuis -> la table `process`
- Si l'audit est conclu, on ne peut pas modifier le constat

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User` et `Extern` n'ont pas accès à cette page.
*/

import React, { useContext, useEffect, useState } from "react";
import {
  Alert,
  StyleSheet,
  View,
  TextInput,
  Text,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import { supabase } from "../../../lib/supabase.native";
import { Controller, useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import {
  ParamListBase,
  RouteProp,
  useNavigation,
  useRoute,
} from "@react-navigation/native";
import { AntDesign } from "@expo/vector-icons";
import { Dropdown } from "react-native-element-dropdown";
import { useRouter } from "expo-router";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";

// Choix du dropdown (audits)
const observationTypes = [
  { label: "Point sensible", value: "sensitive_point" },
  { label: "Non conforme", value: "non_compliant" },
  { label: "Piste de progrès", value: "progress" },
  { label: "Note", value: "note" },
];

// Définition du type pour le formulaire
type FormValues = {
  name: string;
  type: string;
  requirements: string;
  risk: string;
  process: number;
};

// Données de navigation
type UpdateObservationRouteParams = {
  audit: {
    id: number;
    name: string;
    type: string;
    field: string;
    date: string;
    conclusion: string;
  };
  observation: {
    id: number;
    name: string;
    type: string;
    requirements: string;
    risk: string;
    process_id: number;
  };
};

export default function UpdateObservation() {
  const route =
    useRoute<RouteProp<{ params: UpdateObservationRouteParams }, "params">>();
  const { audit, observation } = route.params;
  const [process, setProcess] = useState([]);
  const router = useRouter();
  const { user, companySelected } = useContext(AppContext);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  // Vérifier si l'audit est conclu
  useEffect(() => {
    if (audit?.conclusion && audit.conclusion !== "Non renseignée") {
      Alert.alert(
        "Audit clôturé",
        "Cet audit est clôturé, vous ne pouvez pas modifier ses informations.",
        [
          {
            text: "OK",
            onPress: () => navigation.goBack(),
          },
        ]
      );
    }
  }, [audit]);

  const {
    handleSubmit,
    formState: { errors },
    control,
    reset,
    setValue,
  } = useForm<FormValues>({
    defaultValues: {
      name: "",
      type: "",
      requirements: "",
      risk: "",
      process: 0,
    },
    mode: "onBlur", // Validation sur perte de focus
    criteriaMode: "all", // Retourne toutes les erreurs par champ
  });

  // Charger les données du constat existant
  useEffect(() => {
    if (observation) {
      console.log("Chargement des données du constat:", observation);
      setValue("name", observation.name);
      setValue("type", observation.type);
      setValue("requirements", observation.requirements);
      setValue("risk", observation.risk);
      setValue("process", observation.process_id);
      console.log("Process ID défini:", observation.process_id);
    }
  }, [observation, setValue]);

  // Récupérer les informations de l'entreprise (ici les processus)
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Récupérer les processus de l'entreprise
        const { data: processData, error: processError } = await supabase
          .from("process")
          .select("id, process_name")
          .eq("company_id", companySelected?.id);

        if (processError) {
          console.error(
            "Erreur lors de la récupération des processus :",
            processError.message
          );
        } else {
          const formattedProcess: any = processData.map((process: any) => ({
            label: process.process_name,
            value: process.id,
          }));
          console.log("Processus disponibles:", formattedProcess);
          setProcess(formattedProcess);
        }
      } catch (err) {
        console.error("Erreur inattendue lors de la récupération :", err);
      }
    };

    fetchData();
  }, [user]);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  // Mettre à jour le constat
  const handleUpdateObservation = async (data: any) => {
    // Vérifier si l'audit est conclu
    if (audit?.conclusion && audit.conclusion !== "Non renseignée") {
      Alert.alert(
        "Audit clôturé",
        "Cet audit est clôturé, vous ne pouvez pas modifier ses informations."
      );
      return;
    }

    try {
      setIsSubmitting(true); // Active le chargement

      // Préparation des données pour la mise à jour
      const observationData = {
        name: data?.name,
        requirements: data?.requirements,
        type: data?.type,
        risk: data?.risk,
        process_id: parseInt(data?.process) || observation.process_id,
      };

      console.log("Données à mettre à jour:", observationData);

      // Mise à jour dans la table `observations`
      const { error: observationError } = await supabase
        .from("observations")
        .update(observationData)
        .eq("id", observation.id);

      if (observationError) {
        Alert.alert(
          "Échec de la mise à jour",
          observationError.message || "Une erreur est survenue."
        );
        return;
      }

      // Confirmation de la mise à jour
      Alert.alert("Succès", `Constat ${data?.name} mis à jour avec succès.`, [
        {
          text: "OK",
          onPress: () => {
            // Revenir à DetailObservation
            navigation.goBack();
          },
        },
      ]);
    } catch (err) {
      Alert.alert("Erreur inattendue", "Veuillez réessayer plus tard.");
      console.error("Erreur inattendue :", err);
    } finally {
      setIsSubmitting(false); // Désactive le chargement après la requête
    }
  };

  return (
    <>
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.select({ ios: 0, android: 100 })}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
          <ScrollView style={styles.form}>
            {/* Champ Type */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Types de constat</Text>
              <Controller
                control={control}
                name="type"
                rules={{ required: "Le type est requis." }}
                render={({ field: { onChange, value } }) => (
                  <Dropdown
                    style={dropdownStyles.dropdown}
                    placeholderStyle={dropdownStyles.placeholderStyle}
                    selectedTextStyle={dropdownStyles.selectedTextStyle}
                    inputSearchStyle={dropdownStyles.inputSearchStyle}
                    iconStyle={dropdownStyles.iconStyle}
                    data={observationTypes}
                    search
                    maxHeight={300}
                    labelField="label"
                    valueField="value"
                    placeholder={"Choix du type"}
                    searchPlaceholder="Rechercher un type"
                    value={value}
                    onChange={(item) => {
                      onChange(item.value);
                    }}
                    renderLeftIcon={() => (
                      <AntDesign
                        style={dropdownStyles.icon}
                        color="black"
                        name="Safety"
                        size={20}
                      />
                    )}
                  />
                )}
              />
            </View>

            {/* Champ Éxigence */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Exigence concernée</Text>
              <Controller
                control={control}
                name="requirements"
                rules={{ required: "L'exigence est requise." }}
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    placeholderTextColor={"#6B7280"}
                    style={styles.input}
                    placeholder="Exigence"
                    onChangeText={onChange}
                    value={value}
                    multiline
                  />
                )}
              />
              {errors.requirements && (
                <Text style={styles.errorText}>
                  {errors.requirements.message}
                </Text>
              )}
            </View>

            {/* Champ Nom */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Libellé du constat</Text>
              <Controller
                control={control}
                name="name"
                rules={{ required: "L'exigence est requise." }}
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    maxLength={150}
                    placeholderTextColor={"#6B7280"}
                    style={styles.input}
                    placeholder="Libellé du constat"
                    onChangeText={onChange}
                    value={value}
                    multiline
                  />
                )}
              />
              {errors.name && (
                <Text style={styles.errorText}>{errors.name.message}</Text>
              )}
            </View>

            {/* Champ Risque */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Risque</Text>
              <Controller
                control={control}
                name="risk"
                rules={{ required: "Le risque est requis." }}
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    placeholderTextColor={"#6B7280"}
                    style={styles.input}
                    placeholder="Risque..."
                    onChangeText={onChange}
                    value={value}
                    multiline
                  />
                )}
              />
              {errors.risk && (
                <Text style={styles.errorText}>{errors.risk.message}</Text>
              )}
            </View>

            {/* Champ Processus */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Processus concerné</Text>
              <Controller
                control={control}
                name="process"
                rules={{ required: "Le processus est requis." }}
                render={({ field: { onChange, value } }) => (
                  <Dropdown
                    style={dropdownStyles.dropdown}
                    placeholderStyle={dropdownStyles.placeholderStyle}
                    selectedTextStyle={dropdownStyles.selectedTextStyle}
                    inputSearchStyle={dropdownStyles.inputSearchStyle}
                    iconStyle={dropdownStyles.iconStyle}
                    data={process}
                    search
                    maxHeight={300}
                    labelField="label"
                    valueField="value"
                    placeholder={"Choix du processus"}
                    searchPlaceholder="Rechercher un processus"
                    value={value}
                    onChange={(item) => {
                      console.log("Processus sélectionné:", item);
                      onChange(item.value);
                    }}
                    renderLeftIcon={() => (
                      <AntDesign
                        style={dropdownStyles.icon}
                        color="black"
                        name="Safety"
                        size={20}
                      />
                    )}
                  />
                )}
              />
            </View>

            {/* Bouton de soumission */}
            <TouchableOpacity
              style={styles.confirmButton}
              onPress={handleSubmit(handleUpdateObservation)}
              disabled={isSubmitting || (!!audit?.conclusion && audit.conclusion !== "Non renseignée")}
            >
              {isSubmitting ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Text style={styles.confirmButtonText}>Mettre à jour</Text>
              )}
            </TouchableOpacity>
          </ScrollView>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    height: "100%",
    width: "100%",
    alignItems: "center",
    alignContent: "center",
    backgroundColor: "#FFFFFF",
    paddingBottom: 50,
  },
  headSection: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 30,
  },
  backButton: {
    width: 24,
    textAlign: "left",
  },
  titleWrapper: {
    display: "flex",
    flexDirection: "row",
    marginVertical: 15,
  },
  title: {
    color: "#262627",
    fontSize: 17,
    textAlign: "left",
  },
  form: {
    width: "100%",
    alignSelf: "center",
    paddingHorizontal: 30,
    marginTop: 50,
    marginBottom: 100,
  },
  input: {
    borderWidth: 2,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "auto",
    padding: 12,
    marginTop: 10,
    color: "#000000",
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginBottom: 5,
  },
  infoText: {
    fontSize: 14,
    marginVertical: 10,
    color: "#555",
  },
  inputContainer: {
    position: "relative",
    marginBottom: 20,
    width: "100%",
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
    textAlign: "left",
  },
  confirmButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    marginBottom: 200,
    backgroundColor: "#f99527",
    marginTop: 10,
  },
  confirmButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  imagePicker: {
    width: "100%",
    padding: 10,
    backgroundColor: "#FFFFFF",
    borderColor: "#CCC",
    borderRadius: 8,
    borderWidth: 2,
    alignItems: "center",
    marginBottom: 10,
    marginTop: 10,
  },
  imagePickerText: {
    color: "#1F2937",
    fontWeight: "bold",
  },
  imagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  imageContainer: {
    width: "100%",
    justifyContent: "center",
  },
  uploadPlaceholder: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  uploadIcon: {
    borderWidth: 1,
    borderColor: "#f99527",
    borderRadius: 7,
    padding: 5,
  },
  uploadText: {
    textAlign: "center",
    fontWeight: "normal",
  },
  fullImagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  dateTimePickerContainer: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    textAlign: "left",
    marginVertical: 20,
  },
  dateTimePickerLabel: {
    color: "#063E76",
    fontSize: 14,
    fontWeight: "bold",
    backgroundColor: "#FFF",
  },
  datePicker: {
    width: "100%",
    marginTop: 10,
    marginBottom: 10,
  },
});

// Style du dropdown
const dropdownStyles = StyleSheet.create({
  dropdown: {
    marginVertical: 16,
    height: 50,
    backgroundColor: "white",
    borderRadius: 12,
    padding: 12,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,

    elevation: 2,
  },
  icon: {
    marginRight: 5,
  },
  item: {
    padding: 17,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  textItem: {
    flex: 1,
    fontSize: 16,
  },
  placeholderStyle: {
    fontSize: 16,
  },
  selectedTextStyle: {
    fontSize: 16,
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 16,
  },
});
