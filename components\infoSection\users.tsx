import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  Platform,
} from "react-native";
import { useRouter, usePathname } from "expo-router";
import { AppContext } from "@/state/AppContext";
import { useEffect, useState, useContext } from "react";
import PaperInfoImg from "../common/paperInfoImg";
import typeUserByAdmin from "@/types/typeUserByAdmin";
import { Spinner } from "../ui/spinner";
// import IsLoadingPage from "@/components/common/isLoadingPage";
import { AntDesign } from "@expo/vector-icons";
import FontAwesome from "@expo/vector-icons/FontAwesome";

type typeUsersInfosSection = {
  icon?: any;
  data: typeUserByAdmin[];
  isLanding?: boolean;
};

export default function UsersInfosSection({
  icon,
  data,
  isLanding = false,
}: typeUsersInfosSection) {
  const router = useRouter();
  const path = usePathname();
  const { companySelected } = useContext(AppContext);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={{ fontWeight: "bold" }}>Mes utilisateurs</Text>

        <TouchableOpacity onPress={() => router.navigate("/info/usersAdmin")}>
          {icon ? (
            icon
          ) : (
            <Text
              style={{
                color: "#F99527",
                textDecorationLine: "underline",
                fontFamily: "Poppins",
                fontWeight: "bold",
              }}
            >
              Voir plus
            </Text>
          )}
        </TouchableOpacity>
      </View>

      <View style={styles.users}>
        {isLanding ? (
          <Spinner size="small" color="#1C3144" />
        ) : data && data.length > 0 ? (
          // Afficher les utilisateurs en fonction de maxUsersNumber
          (path === "/Informations" || path === "/Accueil" || path === "/menu/itemsMenuCockpit/info"
            ? data?.slice(-2)
            : data
          ).map((userByAdmin, index) => {
            return (
              <PaperInfoImg
                key={index}
                imgSrc={userByAdmin.profil_picture}
                title={userByAdmin.first_name + " " + userByAdmin.last_name}
                badge={userByAdmin.profil}
                text2={userByAdmin.email}
                fleshShow={false}
                badgeColor="#F99527"
                onPress={() => router.push({
                  pathname: "/info/detailsUser",
                  params: {
                    user_id: userByAdmin.id,
                    user_name: userByAdmin.first_name + " " + userByAdmin.last_name
                  }
                })}
              />
            );
          })
        ) : (
          <Text>Vous n'avez pas encore créé des utilisateurs</Text>
        )}
      </View>
      <View style={{ height: 10 }} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "column",
    width: "100%",
    gap: 10,
    marginBottom: 10,
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 10,
    marginTop: 10,
  },
  users: {
    paddingHorizontal: 10,
    gap: 10,
  },
  searchContainer: {
    width: "100%",
    marginBottom: 20,
    marginTop: 10,
    paddingHorizontal: 10,
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  searchInputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1C314414",
    borderRadius: 8,
    paddingHorizontal: 10,
    height: 60,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    padding: 10,
    fontSize: 16,
    color: "#000000",
  },
});
