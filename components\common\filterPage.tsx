// import React from "react";
// import { Text, View } from "react-native";
// import {
//   Checkbox,
//   CheckboxIndicator,
//   CheckboxLabel,
//   CheckboxIcon,
// } from "@/components/ui/checkbox";
// import { CheckIcon } from "@/components/ui/icon";

// type FilterPageProps = {
//   title?: string;
//   filters: {
//     objectifAtteint: boolean;
//     objectifPartiel: boolean;
//     objectifNonAtteint: boolean;
//   };
//   setFilters: (filters: any) => void;
// };

// const FilterPage = ({ title, filters, setFilters }: FilterPageProps) => {
//   return (
//     <View style={{ paddingHorizontal: 20, gap: 10 }}>
//       <Text style={{ fontSize: 15, fontWeight: "bold" }}>{title}</Text>

//       <Checkbox
//         size="md"
//         value="objectifAtteint"
//         isChecked={filters.objectifAtteint}
//         onChange={() =>
//           setFilters({ ...filters, objectifAtteint: !filters.objectifAtteint })
//         }
//       >
//         <CheckboxIndicator>
//           <CheckboxIcon as={CheckIcon} />
//         </CheckboxIndicator>
//         <CheckboxLabel>Objectif atteint</CheckboxLabel>
//       </Checkbox>

//       <Checkbox
//         size="md"
//         value="objectifPartiel"
//         isChecked={filters.objectifPartiel}
//         onChange={() =>
//           setFilters({ ...filters, objectifPartiel: !filters.objectifPartiel })
//         }
//       >
//         <CheckboxIndicator>
//           <CheckboxIcon as={CheckIcon} />
//         </CheckboxIndicator>
//         <CheckboxLabel>Objectif partiellement atteint</CheckboxLabel>
//       </Checkbox>

//       <Checkbox
//         size="md"
//         value="objectifNonAtteint"
//         isChecked={filters.objectifNonAtteint}
//         onChange={() =>
//           setFilters({
//             ...filters,
//             objectifNonAtteint: !filters.objectifNonAtteint,
//           })
//         }
//       >
//         <CheckboxIndicator>
//           <CheckboxIcon as={CheckIcon} />
//         </CheckboxIndicator>
//         <CheckboxLabel>Objectif non atteint</CheckboxLabel>
//       </Checkbox>
//     </View>
//   );
// };

// export default FilterPage;

import React from "react";
import { Text, View } from "react-native";
import {
  Checkbox,
  CheckboxIndicator,
  CheckboxLabel,
  CheckboxIcon,
} from "@/components/ui/checkbox";
import { CheckIcon } from "@/components/ui/icon";

type FilterPageProps = {
  title?: string;
  filters: { [key: string]: boolean | any }; // 🔥 Rend les filtres dynamiques
  setFilters: (filters: { [key: string]: boolean }) => void;
  filterLabels: { [key: string]: string }; // 🔥 Associe chaque filtre à un label
};

const FilterPage = ({
  title,
  filters,
  setFilters,
  filterLabels,
}: FilterPageProps) => {
  return (
    <View style={{ paddingHorizontal: 20, gap: 10 }}>
      <Text style={{ fontSize: 15, fontWeight: "bold" }}>{title}</Text>

      {Object.entries(filters).map(([key, value]) => (
        <Checkbox
          key={key}
          size="md"
          value={key}
          isChecked={value}
          onChange={() => setFilters({ ...filters, [key]: !value })}
        >
          <CheckboxIndicator>
            <CheckboxIcon as={CheckIcon} />
          </CheckboxIndicator>
          <CheckboxLabel>{filterLabels[key]}</CheckboxLabel>
        </Checkbox>
      ))}
    </View>
  );
};

export default FilterPage;
