import React from "react";
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from "react-native";
import { Badge, BadgeText } from "../ui/badge";
import FontAwesome5 from "@expo/vector-icons/FontAwesome5";

type typePropPaper = {
  title?: string;
  text1?: string;
  text2?: string;
  text3?: string;
  text4?: string;
  onPress?: any;
  scrollable?: boolean;
};

const PaperCompany = ({
  title,
  text1,
  text2,
  text3,
  text4,
  onPress,
  scrollable = false,
}: typePropPaper) => {
  const renderText1 = () => {
    if (scrollable) {
      return (
        <ScrollView 
          style={styles.scrollableText}
          showsVerticalScrollIndicator={false}
        >
          <Text style={styles.text}>{text1}</Text>
        </ScrollView>
      );
    }
    return <Text style={styles.text}>{text1}</Text>;
  };

  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title} numberOfLines={1}>
            {title}
          </Text>
        </View>

        <View style={styles.textContainer}>
          {text1 && (
            <View style={styles.textWrapper}>
              <Text style={styles.label}>Description</Text>
              {renderText1()}
            </View>
          )}
          {text2 && (
            <View style={styles.textWrapper}>
              <Text style={styles.label}>Organisation</Text>
              <Text style={styles.text}>{text2}</Text>
            </View>
          )}
          {text3 && (
            <View style={styles.textWrapper}>
              <Text style={styles.label}>Site Web</Text>
              <Text style={styles.text}>{text3}</Text>
            </View>
          )}
          {text4 && (
            <View style={styles.textWrapper}>
              <Text style={styles.label}>Historique</Text>
              <Text style={styles.text}>{text4}</Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
    borderRadius: 12,
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  content: {
    padding: 16,
  },
  header: {
    marginBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#E6E6E6",
    paddingBottom: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1C3144",
  },
  textContainer: {
    gap: 12,
  },
  textWrapper: {
    marginBottom: 8,
  },
  label: {
    fontSize: 14,
    fontWeight: "600",
    color: "#666",
    marginBottom: 4,
  },
  text: {
    fontSize: 15,
    color: "#333",
    lineHeight: 20,
  },
  scrollableText: {
    maxHeight: 200,
  },
});

export default PaperCompany; 