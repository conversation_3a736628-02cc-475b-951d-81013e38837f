/*
app/menu/itemsMenuCockpit/info.tsx

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `Supplier` n'ont pas accès à cette page.
*/

import {
    Alert,
    Dimensions,
    FlatList,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
    Platform,
  } from "react-native";
  import ProcessInfosSection from "@/components/infoSection/process";
  import ActivitiesInfosSection from "@/components/infoSection/activities";
  import { useCallback, useContext, useEffect, useState, useRef } from "react";
  import { AppContext } from "@/state/AppContext";
  import typeUserByAdmin from "@/types/typeUserByAdmin";
  import { supabase } from "@/lib/supabase";
  import TypeProcess from "@/types/typeProcess";
  import TypeActivity from "@/types/typeActivity";
  import UsersInfosSection from "@/components/infoSection/users";
  import { router, useFocusEffect } from "expo-router";
  import ContextsInfoSection from "@/components/infoSection/context";
  import IssuesInfoSection from "@/components/infoSection/issue";
  import TypePolicy from "@/types/typePolicies";
  import PoliciesInfoSection from "@/components/infoSection/policy";
  import { ParamListBase, useNavigation } from "@react-navigation/native";
  import { NativeStackNavigationProp } from "@react-navigation/native-stack";
  import Header from "@/components/common/Header";
//   import Carousel from "react-native-snap-carousel";
  import { Image } from "react-native";
  import AntDesign from "@expo/vector-icons/AntDesign";
  import FontAwesome6 from "@expo/vector-icons/FontAwesome6";
  import Pdf from "react-native-pdf";
  import TypeELearning from "@/types/typeELearning";
  
  import Ionicons from "@expo/vector-icons/Ionicons";
  import WebView from "react-native-webview";
  
  export default function Info() {
    const width = Dimensions.get("window").width;
    const [eLearningsVideos, setELearningsVideos] = useState<TypeELearning[]>([]);
    const [eLearningsPdf, setELearningsPdf] = useState<TypeELearning[]>([]);
    const [viewStatus, setViewStatus] = useState<{ [key: number]: boolean }>({});
    const [clickStatus, setClickStatus] = useState<{ [key: number]: boolean }>({});
    const videosScrollRef = useRef<any>(null);
    const pdfScrollRef = useRef<any>(null);
  
    const { user, companySelected } = useContext(AppContext);
    const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  
    const source = {
      uri: "http://samples.leanpub.com/thereactnativebook-sample.pdf",
      cache: true,
    };
  
    // Récupérer les politiques
    const fetchDataELearningsVideos = async () => {
      const { data, error } = await supabase
        .from("elearnings")
        .select()
        .eq("uid_user", user?.uid)
        .eq("file_type", "video");
  
      if (!error) {
        setELearningsVideos(data as any);
      }
    };
  
    // Récupérer les politiques
    const fetchDataELearningsPdf = async () => {
      const { data, error } = await supabase
        .from("elearnings")
        .select()
        .eq("uid_user", user?.uid)
        .eq("file_type", "pdf");
  
      if (!error) {
        setELearningsPdf(data as any);
      }
    };
  
    // Récupérer toutes les données
    useFocusEffect(
      useCallback(() => {
        fetchDataELearningsVideos();
        fetchDataELearningsPdf();
      }, [])
    );
  
    // Charger les données au montage initial du composant
    useEffect(() => {
      fetchDataELearningsVideos();
      fetchDataELearningsPdf();
    }, []);
  
    // Vérification du statut de l'utilisateur
    useEffect(() => {
      if (!user) return;
  
      if (["User", "Supplier"].includes(user.status)) {
        Alert.alert(
          "Accès restreint",
          "Vous n'avez pas les droits pour accéder à cette page."
        );
        navigation.goBack();
      }
    }, [user]);
  
    const getViewStatus = async (itemId: number) => {
      const { data } = await supabase
        .from("elearnings_students")
        .select("view_at")
        .eq("elearning_id", itemId)
        .eq("user_id", user?.uid);
      
      return data?.[0]?.view_at != null;
    };
  
    const getClickStatus = async (itemId: number) => {
      const { data } = await supabase
        .from("elearnings_students")
        .select("click_at")
        .eq("elearning_id", itemId)
        .eq("user_id", user?.uid);
      
      return data?.[0]?.click_at != null;
    };
  
    useEffect(() => {
      const fetchStatus = async () => {
        const newViewStatus: { [key: number]: boolean } = {};
        const newClickStatus: { [key: number]: boolean } = {};
        
        for (const item of eLearningsVideos) {
          newViewStatus[item.id] = await getViewStatus(item.id);
          newClickStatus[item.id] = await getClickStatus(item.id);
        }
        
        setViewStatus(newViewStatus);
        setClickStatus(newClickStatus);
      };
      
      fetchStatus();
    }, [eLearningsVideos]);
  
    type typeRender = {
      item: TypeELearning;
    };
  
    const getYouTubeVideoId = (url: string) => {
      const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
      const match = url?.match(regExp);
      return (match && match[2].length === 11) ? match[2] : null;
    };
  
    const isYouTubeUrl = (url: string) => {
      return url?.includes('youtube.com') || url?.includes('youtu.be');
    };

    // Simplified scroll functions with null check
    const scrollLeft = (ref: any) => {
      if (!ref) return;
      
      try {
        const offset = Platform.OS === 'web' ? 300 : 180;
        const currentOffset = ref._scrollOffset || 0;
        ref.scrollToOffset({ 
          offset: Math.max(0, currentOffset - offset), 
          animated: true 
        });
      } catch (err) {
        console.error("Error scrolling left:", err);
      }
    };
    
    const scrollRight = (ref: any) => {
      if (!ref) return;
      
      try {
        const offset = Platform.OS === 'web' ? 300 : 180;
        const currentOffset = ref._scrollOffset || 0;
        ref.scrollToOffset({ 
          offset: currentOffset + offset, 
          animated: true 
        });
      } catch (err) {
        console.error("Error scrolling right:", err);
      }
    };
  
    const renderItem = ({ item }: typeRender) => {
      const videoId = isYouTubeUrl(item.url_video) ? getYouTubeVideoId(item.url_video) : null;
      const thumbnailUrl = videoId 
        ? `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`
        : item.url_mignature;
  
      const handlePress = async () => {
        // Enregistrer la vue avant de naviguer
        const { error } = await supabase.from("elearnings_students").insert({
          elearning_id: item.id,
          user_id: user?.uid,
          view_at: new Date().toISOString(),
          company_id: companySelected?.id
        });
  
        if (error) {
          console.error('Erreur lors de l\'enregistrement de la vue:', error);
        }
  
        router.push({
          pathname: "/content/oneELearning",
          params: {
            idELearning: item.id,
            url_mignature: item.url_mignature,
            title: item.title,
            description: item.description,
            from: "elearning"
          },
        });
      };
  
      return (
        <TouchableOpacity
          style={styles.card}
          onPress={handlePress}
        >
          {/* Image de la vidéo */}
          <View>
            <Image 
              source={{ uri: thumbnailUrl }} 
              style={styles.image}
            />
            {/* Icône Play en haut à gauche */}
            <View style={[styles.playIconContainer, videoId && styles.youtubePlayIcon]}>
              {videoId ? (
                <AntDesign name="youtube" size={32} color="red" />
              ) : (
                <AntDesign name="playcircleo" size={32} color="#F99527" />
              )}
            </View>
          </View>
  
          {/* Texte de la vidéo */}
          <View style={styles.overlay}>
            <Text style={styles.title} numberOfLines={2} ellipsizeMode="tail">
              {item.title}
            </Text>
            <Text style={styles.description} numberOfLines={2} ellipsizeMode="tail">
              {item.description}
            </Text>
  
            <View style={styles.checkContainer}>
              <Ionicons
                name="checkmark-done-sharp"
                size={20}
                color={viewStatus[item.id] && clickStatus[item.id] ? "#34C759" : "gray"}
                style={styles.checkDoubl}
              />
            </View>
          </View>
        </TouchableOpacity>
      );
    };
  
    const renderItemPdf = ({ item }: typeRender) => {
      const handlePressPdf = async () => {
        // Enregistrer la vue avant de naviguer
        const { error } = await supabase.from("elearnings_students").insert({
          elearning_id: item.id,
          user_id: user?.uid,
          view_at: new Date().toISOString(),
          company_id: companySelected?.id
        });
  
        if (error) {
          console.error('Erreur lors de l\'enregistrement de la vue:', error);
        }
  
        router.push({
          pathname: "/content/oneELearningPdf",
          params: {
            id: item.id,
            name: item.title,
            url_media: item.url_media,
            url_mignature: item.url_mignature,
            from: "elearning"
          },
        });
      };
  
      return (
        <TouchableOpacity
          style={styles.cardPdf}
          onPress={handlePressPdf}
        >
          <View style={{ width: '100%' }}>
            {Platform.OS === "web" ? (
              <iframe
                src={item.url_media}
                style={{
                  width: "100%",
                  height: 100,
                  border: "none",
                  borderRadius: 10,
                  backgroundColor: '#fff',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                }}
              />
            ) : (
              <Image
                source={{ uri: item.url_media }}
                style={styles.image}
              />
            )}
  
            <View style={styles.overlay}>
              <Text style={styles.title} numberOfLines={2} ellipsizeMode="tail">
                {item.title}
              </Text>
              <Text style={styles.description} numberOfLines={1} ellipsizeMode="tail">
                {item.description.length > 60 ? item.description.substring(0, 60) + "..." : item.description}
              </Text>
  
              <View style={styles.checkContainer}>
                <Ionicons
                  name="checkmark-done-sharp"
                  size={20}
                  color={viewStatus[item.id] && clickStatus[item.id] ? "#34C759" : "gray"}
                  style={styles.checkDoubl}
                />
              </View>
            </View>
          </View>
        </TouchableOpacity>
      );
    };
  
    return (
      <View style={styles.mainContainer}>
        <Header
          title={"E-Learning"}
          onPressIcon={() => router.navigate("/content/createELearning")}
          onPressFlesh={() => router.navigate("/")}
        />
        <ScrollView style={styles.container}>
          <View style={styles.content}>
            <View style={styles.header}>
              <Text style={{ fontWeight: "bold" }}>Vidéos</Text>
              <TouchableOpacity
                onPress={() => router.navigate("/content/listELearning")}
              >
                <Text style={styles.seeMoreText}>Voir plus</Text>
              </TouchableOpacity>
            </View>
            
            {Platform.OS === 'web' && eLearningsVideos?.length > 0 && (
              <View style={styles.carouselContainer}>
                <TouchableOpacity 
                  style={styles.navButton} 
                  onPress={() => scrollLeft(videosScrollRef.current)}
                >
                  <AntDesign name="left" size={24} color="#666" />
                </TouchableOpacity>
                
                <View style={styles.carouselContent}>
                  <FlatList
                    ref={videosScrollRef}
                    data={eLearningsVideos}
                    renderItem={renderItem}
                    keyExtractor={(item) => item.id as any}
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    snapToAlignment="start"
                    snapToInterval={width * 0.8}
                    decelerationRate="fast"
                    contentContainerStyle={styles.flatListContent}
                    scrollEnabled={true}
                  />
                </View>
                
                <TouchableOpacity 
                  style={styles.navButton} 
                  onPress={() => scrollRight(videosScrollRef.current)}
                >
                  <AntDesign name="right" size={24} color="#666" />
                </TouchableOpacity>
              </View>
            )}
            
            {(Platform.OS !== 'web' || eLearningsVideos?.length === 0) && (
              <>
                {eLearningsVideos?.length > 0 ? (
                  <FlatList
                    data={eLearningsVideos}
                    renderItem={renderItem}
                    keyExtractor={(item) => item.id as any}
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    snapToAlignment="start"
                    snapToInterval={width * 0.8}
                    decelerationRate="fast"
                    contentContainerStyle={styles.flatListContent}
                  />
                ) : (
                  <Text style={styles.emptyText}>
                    Vous avez pas encore crée ce contenu !
                  </Text>
                )}
              </>
            )}
            
            <View style={styles.header}>
              <Text style={{ fontWeight: "bold" }}>PDF</Text>
              <TouchableOpacity
                onPress={() => router.navigate("/content/listELearningPdf")}
              >
                <Text style={styles.seeMoreText}>Voir plus</Text>
              </TouchableOpacity>
            </View>
            
            {Platform.OS === 'web' && eLearningsPdf?.length > 0 && (
              <View style={styles.carouselContainer}>
                <TouchableOpacity 
                  style={styles.navButton} 
                  onPress={() => scrollLeft(pdfScrollRef.current)}
                >
                  <AntDesign name="left" size={24} color="#666" />
                </TouchableOpacity>
                
                <View style={styles.carouselContent}>
                  <FlatList
                    ref={pdfScrollRef}
                    data={eLearningsPdf}
                    renderItem={renderItemPdf}
                    keyExtractor={(item) => item.id as any}
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    snapToAlignment="start"
                    snapToInterval={width * 0.8}
                    decelerationRate="fast"
                    contentContainerStyle={styles.flatListContent}
                    scrollEnabled={true}
                  />
                </View>
                
                <TouchableOpacity 
                  style={styles.navButton} 
                  onPress={() => scrollRight(pdfScrollRef.current)}
                >
                  <AntDesign name="right" size={24} color="#666" />
                </TouchableOpacity>
              </View>
            )}
            
            {(Platform.OS !== 'web' || eLearningsPdf?.length === 0) && (
              <>
                {eLearningsPdf?.length > 0 ? (
                  <FlatList
                    data={eLearningsPdf}
                    renderItem={renderItemPdf}
                    keyExtractor={(item) => item.id as any}
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    snapToAlignment="start"
                    snapToInterval={width * 0.8}
                    decelerationRate="fast"
                    contentContainerStyle={styles.flatListContent}
                  />
                ) : (
                  <Text style={styles.emptyText}>
                    Vous avez pas encore crée ce contenu !
                  </Text>
                )}
              </>
            )}
          </View>
        </ScrollView>
      </View>
    );
  }
  
  const styles = StyleSheet.create({
    mainContainer: {
      flex: 1,
    },
    container: {
      flex: 1,
      width: "100%",
      ...(Platform.OS === 'web' ? {
        maxWidth: 800,
        marginHorizontal: 'auto',
      } : {}),
    },
    content: {
      gap: 20,
      padding: 20,
    },
    header: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginTop: 14,
    },
    seeMoreText: {
      color: "#F99527",
      textDecorationLine: "underline",
      fontFamily: "Poppins",
      fontWeight: "bold",
    },
    card: {
      borderRadius: 10,
      overflow: "hidden",
      backgroundColor: "#fff",
      elevation: 3,
      padding: 10,
      margin: 10,
      paddingBottom: 0,
      width: Platform.OS === 'web' ? 300 : 280,
      height: Platform.OS === 'web' ? 320 : 300,
      alignSelf: 'center',
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
    },
    cardPdf: {
      borderRadius: 10,
      overflow: "hidden",
      backgroundColor: "#fff",
      elevation: 3,
      padding: 10,
      margin: 10,
      paddingBottom: 0,
      width: Platform.OS === 'web' ? 300 : 280,
      height: Platform.OS === 'web' ? 240 : 220,
      alignSelf: 'center',
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
    },
    image: {
      width: "100%",
      height: 160,
      borderRadius: 10,
    },
    overlay: {
      padding: 10,
      flex: 1,
      justifyContent: "space-between",
    },
    title: {
      fontSize: 16,
      fontWeight: "bold",
      alignSelf: "center",
      marginBottom: 8,
    },
    description: {
      fontSize: 14,
      color: "#666",
      alignSelf: "center",
      marginBottom: 8,
    },
    checkContainer: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      marginTop: 'auto',
    },
    checkDoubl: {
      alignSelf: 'flex-end',
    },
    playIconContainer: {
      position: "absolute",
      bottom: 10,
      left: 10,
      borderRadius: 20,
      padding: 5,
    },
    youtubePlayIcon: {
      backgroundColor: 'white',
      borderRadius: 50,
      padding: 5,
    },
    flatListContent: {
      paddingHorizontal: Platform.OS === 'web' ? 20 : 10,
      paddingRight: 30,
    },
    carouselContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginVertical: 10,
    },
    carouselContent: {
      flex: 1,
    },
    navButton: {
      width: 40,
      height: 40,
      backgroundColor: 'rgba(255,255,255,0.9)',
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
      zIndex: 10,
    },
    emptyText: {
      fontSize: 14,
      color: '#525252',
      textAlign: 'center',
      padding: 20,
    },
  });
  