/*
app/objectif/createValue.tsx

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern` et `Extern` n'ont pas accès à cette page.
*/

import ContainedButton from "@/components/ui/buttons/ContainedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useRouter, useLocalSearchParams } from "expo-router";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  Alert,
  Platform,
  TouchableOpacity,
} from "react-native";

import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import CustomizedSelect from "@/components/ui/customizedSelect/customizedSelect";
import TypeValue from "@/types/typeValue";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { WebDateTimePicker } from "@/components/ui/datepicker/WebDateTimePicker";
import DateTimePicker from "@react-native-community/datetimepicker";
import { DateTimePickerAndroid } from "@react-native-community/datetimepicker";

const inputs = [
  {
    label: "Nouvelle valeur",
    name: "value_name",
    placeholder: "Nouvelle valeur",
  },
];

const levels = [
  { value: 0, label: "Non maîtrisée" },
  { value: 1, label: "Partiel" },
  { value: 2, label: "Maîtrisée" },
];

export default function CreateValue() {
  const [loading, setLoading] = useState(false);
  const { user } = useContext(AppContext);
  const router = useRouter();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const {
    uid_user,
    target,
    designation,
    measuring_indicator,
    process_id,
    level,
    created_at: created_at_params,
    company_name,
    objectif_id,
    value_latest,
  } = useLocalSearchParams();

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors, touchedFields },
    control,
    reset,
    watch,
    setValue,
  } = useForm<TypeValue>({
    defaultValues: {
      value_name: "",
      objectif_id: objectif_id as any,
      level: 0,
      date_value: new Date().toISOString().split('T')[0],
    },
  });

  const [selectedDate, setSelectedDate] = useState(new Date());

  // Date (iOS)
  const handleDateChange = (event: any, date: any) => {
    if (date) {
      setSelectedDate(date);
      setValue("date_value", date.toISOString().split('T')[0]);
    }
  };

  // Date (Android)
  const showDatePicker = () => {
    DateTimePickerAndroid.open({
      value: selectedDate,
      mode: "date",
      onChange: handleDateChange,
      display: "default",
    });
  };

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const createValue = async (data: TypeValue) => {
    setLoading(true);

    const created_at = new Date();

    try {
      const { error } = await supabase.from("values").insert({
        uid_user: user?.uid,
        value_name: data.value_name,
        objectif_id: objectif_id,
        created_at: created_at,
        level: data.level || 0,
        date_value: data.date_value,
      });

      if (error) {
        Toast.show({
          type: "error",
          text1: "Erreur lors d'enregistrement de la valeur",
          text1Style: { color: "#1C3144" },
        });
        setLoading(false);
        return;
      }

      Toast.show({
        type: "success",
        text1: "La valeur était bien crée",
        text1Style: { color: "#1C3144" },
      });

      router.back();


      reset();
      setLoading(false);
    } catch (err) {
      setLoading(false);

      Toast.show({
        type: "error",
        text1: "Une erreur est survenue",
        text1Style: { color: "#1C3144" },
      });
    }
  };

  return (
    <View style={styles.mainContainer}>
      <ScrollView style={styles.container}>
        <View style={styles.containerContent}>
          <View style={styles.registerInfo}>
            <View style={styles.inputs}>
              <View style={styles.infoContainer}>
                <View style={styles.infoText}>
                  <Text style={styles.infoLabel}>Entreprise :</Text>
                  <Text>{company_name}</Text>
                  <Text style={styles.infoLabel}>Descriptif :</Text>
                  <Text>{designation}</Text>
                  <Text style={styles.infoLabel}>Dernière valeur :</Text>
                  <Text>{value_latest || "Aucune valeur"}</Text>
                </View>
              </View>
              {inputs.map((input, index) => {
                return (
                  <View key={index}>
                    <InputCustomized
                      label={input.label}
                      placeholder={input.placeholder}
                      name={input.name}
                      register={register}
                      control={control}
                      errors={errors}
                      setError={setError}
                    />
                  </View>
                );
              })}
              {/* Date Picker pour Android */}
              {Platform.OS === "android" && (
                <TouchableOpacity
                  onPress={showDatePicker}
                  style={styles.dateTimePickerContainer}
                >
                  <Text style={styles.dateTimePickerLabel}>Date de la valeur</Text>
                  <Text style={styles.datePicker}>
                    {selectedDate.toLocaleDateString()}
                  </Text>
                </TouchableOpacity>
              )}

              {/* Date Picker pour iOS */}
              {Platform.OS === "ios" && (
                <View style={styles.dateTimePickerContainer}>
                  <Text style={styles.label}>Date de la valeur</Text>
                  <DateTimePicker
                    value={selectedDate}
                    mode="date"
                    display="default"
                    onChange={handleDateChange}
                    style={styles.datePicker}
                  />
                </View>
              )}

              {/* Date Picker pour Web */}
              {Platform.OS === "web" && (
                <View style={styles.dateTimePickerContainer}>
                  <Text style={styles.label}>Date de la valeur</Text>
                  <WebDateTimePicker
                    value={selectedDate}
                    onChange={handleDateChange}
                  />
                </View>
              )}
              <CustomizedSelect
                name="level"
                label="Niveau atteinte objectif"
                register={register}
                control={control}
                errors={errors}
                setError={setError}
                data={levels as any}
              />
            </View>
          </View>

          <View style={styles.buttons}>
            <ContainedButton
              label="Enregistrer"
              backgroundColor="#F99527"
              onPress={handleSubmit(createValue)}
              disabled={loading}
            />
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  containerContent: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    width: "100%",
    gap: 20,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
    marginTop: 20,
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
  infoContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 10,
  },
  infoText: {
    display: "flex",
    gap: 10,
  },
  infoLabel: {
    fontWeight: "bold",
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: '#1C3144',
  },
  dateTimePickerContainer: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    textAlign: "left",
    marginVertical: 20,
  },
  dateTimePickerLabel: {
    color: "#063E76",
    fontSize: 14,
    fontWeight: "bold",
    backgroundColor: "#FFF",
  },
  datePicker: {
    width: "100%",
    marginTop: 10,
    marginBottom: 10,
  },
});
