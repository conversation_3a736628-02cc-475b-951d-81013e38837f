/*
app/menu/itemsMenuCockpit/supplier.tsx

Liste des fournisseurs.

- On récupère les fournisseurs de l'entreprise depuis la table `suppliers`

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Extern`, `Supplier` n'ont pas accès à cette page.
*/

import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Alert,
  FlatList,
  Platform,
} from "react-native";
import { useRouter } from "expo-router";
import {
  ParamListBase,
  useFocusEffect,
  useNavigation,
} from "@react-navigation/native";
import React, { useContext, useEffect, useState } from "react";
import { supabase } from "@/lib/supabase";
import { AppContext } from "@/state/AppContext";
import TypeSupplier from "@/types/typeSupplier";
import { Octicons } from "@expo/vector-icons";
import FilterPage from "@/components/common/filterPage";
import Header from "@/components/common/Header";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";

interface User {
  id: number;
  uid: string;
  first_name: string;
  last_name: string;
  phone_number: string;
  email: string;
  profil: string;
}

interface CompanyUser {
  user_id: string;
  users: User;
}

interface Event {
  administrator: string;
  id: number;
}

interface SupplierStatus {
  uid_user: string;
  status: string;
}

export default function Supplier() {
  const router = useRouter();
  const [data, setData] = useState<TypeSupplier[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { user, companySelected } = useContext(AppContext);
  const [showFilter, setShowFilter] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const [supplier, setSupplier] = useState<any>([]);
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  // Récupérer la liste des Suppliers de l'entreprise
  useEffect(() => {
    if (!user) return;

    // Redirection pour les rôles interdits
    if (!["Admin", "Supplier"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  useFocusEffect(
    React.useCallback(() => {
      const fetchSuppliers = async () => {
        try {
          if (!user || !user.uid) {
            console.error("Utilisateur non connecté.");
            return;
          }

          const companyId = companySelected?.id;
          if (!companyId) {
            setSupplier([]);
            setIsLoading(false);
            return;
          }

          let suppliersList = [];

          if (user.status === "Admin") {
            // Admin : Récupérer tous les fournisseurs de l'entreprise
            const { data: suppliersData, error: suppliersError } =
              await supabase
                .from("company_users")
                .select(
                  `
                user_id,
                users (
                  id,
                  uid,
                  first_name,
                  last_name,
                  phone_number,
                  email,
                  profil
                )
              `
                )
                .eq("company_id", companyId);

            if (suppliersError) {
              console.error(
                "Erreur récupération fournisseurs :",
                suppliersError
              );
              return;
            }

            // Filtrer uniquement les fournisseurs
            suppliersList = suppliersData
              .map((entry: CompanyUser) => entry.users)
              .filter((user: User) => user.profil === "Fournisseur");

            // Récupérer les informations des fournisseurs depuis la table suppliers
            const { data: suppliersInfo, error: suppliersInfoError } = await supabase
              .from("suppliers")
              .select("uid_user, company_name")
              .in("uid_user", suppliersList.map((s: User) => s.uid));

            if (suppliersInfoError) {
              console.error("Erreur récupération des informations des fournisseurs :", suppliersInfoError);
              return;
            }

            // Créer un map des informations des fournisseurs
            const suppliersInfoMap = suppliersInfo.reduce((acc: Record<string, string>, curr: any) => {
              acc[curr.uid_user] = curr.company_name;
              return acc;
            }, {});

            // Récupérer les statuts des fournisseurs
            const { data: suppliersStatus, error: statusError } = await supabase
              .from("suppliers")
              .select("uid_user, status")
              .in("uid_user", suppliersList.map((s: User) => s.uid));

            if (statusError) {
              console.error("Erreur récupération des statuts :", statusError);
              return;
            }

            // Associer le statut
            const statusMap = suppliersStatus.reduce(
              (acc: Record<string, string>, curr: SupplierStatus) => {
                acc[curr.uid_user] = curr.status;
                return acc;
              },
              {}
            );

            // Récupérer les événements par fournisseur
            const { data: eventCounts, error: eventsError } = await supabase
              .from("events")
              .select("administrator")
              .in(
                "administrator",
                suppliersList.map((s: User) => s.uid)
              );

            if (eventsError) {
              console.error(
                "Erreur récupération des événements :",
                eventsError
              );
              return;
            }

            const eventCountMap = eventCounts.reduce(
              (acc: Record<string, number>, curr: Event) => {
                acc[curr.administrator] = (acc[curr.administrator] || 0) + 1;
                return acc;
              },
              {}
            );

            // Associer les statuts et événements
            suppliersList = suppliersList.map((supplier: User) => ({
              id: supplier.id,
              uid: supplier.uid,
              first_name: supplier.first_name,
              last_name: supplier.last_name,
              phone_number: supplier.phone_number,
              email: supplier.email,
              company_name: suppliersInfoMap[supplier.uid] || `${supplier.first_name} ${supplier.last_name}`,
              status: statusMap[supplier.uid] || "regular",
              eventCount: eventCountMap[supplier.uid] || 0,
            }));
          } else if (user.status === "Supplier") {
            // Supplier : Récupérer uniquement ses propres informations
            const { data: supplierData, error: supplierError } = await supabase
              .from("company_users")
              .select(
                `
                user_id,
                users (
                  id,
                  uid,
                  first_name,
                  last_name,
                  phone_number,
                  email,
                  profil
                )
              `
              )
              .eq("company_id", companyId)
              .eq("user_id", user.uid)
              .single();

            if (supplierError) {
              console.error("Erreur récupération fournisseur :", supplierError);
              return;
            }

            if (!supplierData) {
              setSupplier([]);
              return;
            }

            // Récupérer ses événements
            const { data: eventCounts, error: eventsError } = await supabase
              .from("events")
              .select("administrator")
              .eq("administrator", user.uid);

            if (eventsError) {
              console.error(
                "Erreur récupération des événements :",
                eventsError
              );
              return;
            }

            const eventCount = eventCounts.length;

            suppliersList = [
              {
                id: supplierData.users.id,
                uid: supplierData.users.uid,
                first_name: supplierData.users.first_name,
                last_name: supplierData.users.last_name,
                phone_number: supplierData.users.phone_number,
                email: supplierData.users.email,
                eventCount,
              },
            ];
          }

          setSupplier(suppliersList);
        } catch (err) {
          console.error("Erreur inattendue :", err);
        } finally {
          setIsLoading(false);
        }
      };

      fetchSuppliers();
    }, [user, companySelected])
  );

  // Restrictions selon le statut
  useEffect(() => {
    if (!user) return;

    if (["User", "Extern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  // Gestion des types
  const getSuppliersStatusStyle = (type: string) => {
    console.log("Status reçu:", type); // Pour déboguer
    switch (type?.toLowerCase()) {
      case "good":
        return { text: "Bon", backgroundColor: "#2FC12B" };
      case "regular":
        return { text: "Moyen", backgroundColor: "#F39415" };
      case "bad":
        return { text: "Mauvais", backgroundColor: "#1C3144" };
      case "active":
        return { text: "Actif", backgroundColor: "#2FC12B" };
      case "inactive":
        return { text: "Inactif", backgroundColor: "#1C3144" };
      default:
        return { text: "Inconnu", backgroundColor: "#D3D3D3" };
    }
  };

  // État pour gérer les filtres
  const [filters, setFilters] = useState<{
    [key: string]: boolean;
  }>({
    objectifAtteint: false,
    objectifPartiel: false,
    objectifNonAtteint: false,
  });

  const filteredData =
    filters.objectifAtteint ||
    filters.objectifPartiel ||
    filters.objectifNonAtteint
      ? data.filter((item) => {
          // if (filters.objectifAtteint && item.status === 2) return true;
          // if (filters.objectifPartiel && item.status === 1) return true;
          // if (filters.objectifNonAtteint && item.status === 0) return true;
          return false;
        })
      : data;

  return (
    <View style={styles.mainContainer}>
      <Header
        title={showFilter ? "Filtrer les fournisseurs" : "Fournisseurs"}
        onPressIcon={() => setShowFilter(!showFilter)}
        onPressFlesh={() => router.navigate("/")}
      />

      <View style={styles.container}>
        {showFilter ? (
          <FilterPage
            title={"Fournisseurs"}
            filters={filters}
            setFilters={setFilters}
            filterLabels={{
              objectifAtteint: "Objectif atteint",
              objectifPartiel: "Objectif partiellement atteint",
              objectifNonAtteint: "Objectif non atteint"
            }}
          />
        ) : (
          <View style={styles.content}>
            <View style={styles.header}>
              <Text style={{ fontWeight: "bold" }}>
                Ajouter un nouveau fournisseur :
              </Text>
              <TouchableOpacity
                onPress={() => router.navigate("/supplier/createSupplier")}
              >
                <Octicons name="diff-added" size={25} color="#F99527" />
              </TouchableOpacity>
            </View>

            {isLoading ? (
              <Text style={styles.emptyText}>Chargement...</Text>
            ) : supplier.length === 0 ? (
              <View style={styles.emptyState}>
                <Text style={styles.emptyText}>Vous n'avez aucun fournisseur.</Text>
              </View>
            ) : (
              <FlatList
                data={supplier}
                keyExtractor={(item) => item.id.toString()}
                renderItem={({ item }) => {
                  const { text, backgroundColor } = getSuppliersStatusStyle(
                    item.status
                  );

                  return (
                    <TouchableOpacity
                      onPress={() =>
                        router.navigate({
                          pathname: "/supplier/detailsSupplier",
                          params: {
                            id: item.id,
                            uid: item.uid,
                            last_name: item.last_name,
                            first_name: item.first_name,
                            status: item.status,
                            phone_number: item.phone_number,
                            email: item.email,
                          },
                        })
                      }
                      style={styles.listItem}
                    >
                      <View style={styles.left}>
                        <Text style={styles.companyName}>
                          {item.company_name}
                        </Text>
                        <Text style={styles.userName}>
                          {item.last_name} {item.first_name}
                        </Text>
                        <Text style={styles.eventCount}>
                          Nombre d'événements : {item.eventCount}
                        </Text>
                      </View>

                      <View style={styles.right}>
                        {user.status === "Admin" && (
                          <Text style={[styles.statusText, { backgroundColor }]}>
                            {text}
                          </Text>
                        )}
                        <Octicons
                          name="chevron-right"
                          size={40}
                          color="black"
                        />
                      </View>
                    </TouchableOpacity>
                  );
                }}
                nestedScrollEnabled={true}
                contentContainerStyle={styles.listContainer}
              />
            )}
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  content: {
    gap: 20,
    padding: 20,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 14,
  },
  emptyState: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "center",
    padding: 15,
  },
  listContainer: {
    width: "100%",
    paddingVertical: 10,
  },
  listItem: {
    width: "100%",
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    display: "flex",
    justifyContent: "space-between",
    flexDirection: "row",
    gap: 10,
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  left: {
    maxWidth: "50%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    gap: 10,
  },
  right: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-evenly",
    alignItems: "center",
    gap: 15,
  },
  companyName: {
    fontSize: 16,
    fontWeight: "bold",
  },
  userName: {
    fontSize: 14,
    color: "#525252",
  },
  eventCount: {
    fontSize: 12,
    color: "#525252",
  },
  statusText: {
    fontSize: 10,
    color: "#FFFFFF",
    padding: 8,
    borderRadius: 50,
    fontStyle: "normal",
    flexWrap: "wrap",
    fontWeight: "normal",
    textAlign: "center",
    minWidth: 100,
  },
  emptyText: {
    fontSize: 14,
    color: '#525252',
    textAlign: 'center',
    padding: 20,
  },
});
