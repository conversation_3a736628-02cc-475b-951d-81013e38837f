module.exports = function(api) {
    api.cache(true);

    return {
        presets: [
            ["babel-preset-expo", {
                jsxImportSource: "nativewind",
                jsxRuntime: "automatic"
            }],
            "nativewind/babel"
        ],

        plugins: [
            // ✅ Ajoute la prise en charge des modules externes
            ["module-resolver", {
                root: ["./"],
                alias: {
                    "@": "./",
                    "tailwind.config": "./tailwind.config.js",
                    "expo-router/app": "./app", // 🚀 S'assurer que Expo Router trouve `app/`
                }
            }]
        ]
    };
};
