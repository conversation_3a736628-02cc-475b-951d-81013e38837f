import { StyleSheet, View, Text, TouchableOpacity, ViewStyle, TextStyle } from "react-native";
import { useState } from "react";
import { Ionicons } from "@expo/vector-icons";
import { Svg, Circle } from 'react-native-svg';

type AccordionSectionProps = {
  title: string;
  children: React.ReactNode;
  onEdit?: () => void;
  hasContent?: boolean;
};

export default function AccordionSection({ title, children, onEdit, hasContent }: AccordionSectionProps) {
  const [expanded, setExpanded] = useState(false);

  return (
    <View style={styles.container}>
      <TouchableOpacity 
        style={styles.header} 
        onPress={() => setExpanded(!expanded)}
        activeOpacity={0.7}
      >
        <View style={styles.titleContainer}>
          <View style={styles.titleWrapper}>
            <Text style={styles.title}>{title}</Text>
            <View style={styles.statusIndicator}>
              <Svg width="24" height="24" viewBox="0 0 24 24">
                <Circle
                  cx="12"
                  cy="12"
                  r="10"
                  stroke={hasContent ? "#F99527" : "#E6E6E6"}
                  strokeWidth="2"
                  fill="none"
                />
                {hasContent && (
                  <Circle
                    cx="12"
                    cy="12"
                    r="6"
                    fill="#F99527"
                  />
                )}
              </Svg>
            </View>
          </View>
          <View style={styles.actions}>
            {onEdit && (
              <TouchableOpacity 
                onPress={(e) => {
                  e.stopPropagation();
                  onEdit();
                }} 
                style={styles.editButton}
              >
                <Ionicons name="pencil" size={20} color="#F99527" />
              </TouchableOpacity>
            )}
            <View style={[styles.chevron, expanded && styles.chevronRotated]}>
              <Ionicons 
                name="chevron-down" 
                size={24} 
                color="#1C3144" 
              />
            </View>
          </View>
        </View>
      </TouchableOpacity>
      
      <View 
        style={[
          styles.content,
          expanded ? styles.contentExpanded : styles.contentCollapsed
        ]}
      >
        <View style={styles.contentInner}>
          {children}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
    borderRadius: 8,
    marginBottom: 10,
    overflow: "hidden",
    boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
    maxWidth: 800,
    width: "100%",
    marginLeft: "auto",
    marginRight: "auto",
  } as ViewStyle,
  header: {
    padding: 16,
  } as ViewStyle,
  titleContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    maxWidth: 800,
    width: "100%",
    marginLeft: "auto",
    marginRight: "auto",
  } as ViewStyle,
  titleWrapper: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  } as ViewStyle,
  title: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1C3144",
  } as TextStyle,
  statusIndicator: {
    width: 24,
    height: 24,
  } as ViewStyle,
  actions: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  } as ViewStyle,
  editButton: {
    padding: 4,
  } as ViewStyle,
  chevron: {
    transition: "transform 0.3s ease",
  } as ViewStyle,
  chevronRotated: {
    transform: [{ rotate: "180deg" }],
  } as ViewStyle,
  content: {
    overflow: "hidden",
    transition: "max-height 0.3s ease, opacity 0.3s ease",
    maxWidth: 800,
    width: "100%",
    marginLeft: "auto",
    marginRight: "auto",
  } as ViewStyle,
  contentExpanded: {
    maxHeight: 1000,
    opacity: 1,
  } as ViewStyle,
  contentCollapsed: {
    maxHeight: 0,
    opacity: 0,
  } as ViewStyle,
  contentInner: {
    padding: 16,
  } as ViewStyle,
}); 