import ContainedButton from "@/components/ui/buttons/ContainedButton";
import OutlinedButton from "@/components/ui/buttons/OutlinedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useRouter, useLocalSearchParams, useFocusEffect } from "expo-router";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Platform,
  Alert,
} from "react-native";

import { useCallback, useContext, useEffect, useState } from "react";
import { Octicons } from "@expo/vector-icons";
import { AppContext } from "@/state/AppContext";
import PaperInfo from "@/components/common/paperInfo";
import { supabase } from "@/lib/supabase";
import Header from "@/components/common/Header";
import TypeValue from "@/types/typeValue";
import { Dimensions } from "react-native";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { Badge, BadgeText } from "@/components/ui/badge";
import { Image } from "react-native";
import TypeProdut from "@/types/typeProduct";
import TypeActionEvent from "@/types/typeActionEvent";
import TypeUser from "@/types/user";
import TypeProcess from "@/types/typeProcess";

const screenWidth = Dimensions.get("window").width; // ✅ Récupère la largeur de l'écran

type PreventiveAction = {
  id: number;
  name: string;
  date: string;
  type: string;
  status: string;
  created_at: string;
  product_id: number;
  resultAction?: string;
  company_id?: number;
  administrator: TypeUser;
  process: {
    id: number;
    process_name: string;
  };
};

type ActionResponse = {
  id: number;
  name: string;
  date: string;
  type: string;
  status: string;
  created_at: string;
  resultAction?: string;
  company_id?: number;
  action_products: Array<{ product_id: number }>;
  action_administrators: Array<{ users: TypeUser }>;
  action_processes: Array<{ process: { id: number; process_name: string } }>;
};

export default function detailsProduct() {
  const { user } = useContext(AppContext);

  const [product, setProduct] = useState<TypeProdut>();
  const [productDocs, setProductDocs] = useState<
    { name: string; created_at: string; id: number }[]
  >([]);

  const [productActions, setProductActions] = useState<PreventiveAction[]>([]);

  const router = useRouter();

  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  const { product_id } = useLocalSearchParams();

  const fetchProduct = async () => {
    const { data: dataGetted, error } = await supabase
      .from("products")
      .select("*")
      .eq("id", product_id)
      .single();

    if (!error) {
      setProduct(dataGetted as any);
    }
  };

  const fetchDocs = async () => {
    const { data: dataGetted, error } = await supabase
      .from("docsProduct")
      .select("*")
      .eq("product_id", product_id)
      .eq("type", "doc");

    if (!error) {
      setProductDocs(dataGetted as any);
    }
  };

  const fetchActions = async () => {
    const { data: dataGetted, error } = await supabase
      .from("actions")
      .select(`
        id,
        name,
        date,
        type,
        status,
        created_at,
        resultAction,
        company_id,
        action_products!inner(product_id),
        action_administrators!inner(users:users(*)),
        action_processes!inner(process:process(id, process_name))
      `)
      .eq("action_products.product_id", product_id)
      .eq("type", "preventive");

    if (!error) {
      const transformedData = (dataGetted as ActionResponse[]).map(action => ({
        id: action.id,
        name: action.name,
        date: action.date,
        type: action.type,
        status: action.status,
        created_at: action.created_at,
        resultAction: action.resultAction,
        company_id: action.company_id,
        product_id: action.action_products[0]?.product_id,
        administrator: action.action_administrators[0]?.users,
        process: action.action_processes[0]?.process
      }));
      setProductActions(transformedData);
    } else {
      console.error("Erreur lors de la récupération des actions :", error);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchProduct();
      fetchDocs();
      fetchActions();
    }, [])
  );

  function capitalizeFirstLetter(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
  }

  return (
    <View>
      <Header
        onPressFlesh={() => router.navigate("/menu/maintenance")}
        onPressIcon={() =>
          router.push({
            pathname: "/product/updateProduct",
            params: {
              product_id: product_id,
              name: product?.name,
              description: product?.description,
              image: product?.image,
              status: product?.status as any,
            },
          })
        }
      />
      <ScrollView>
        <View style={styles.containerContent}>
          <View style={styles.registerInfo}>
            <View style={styles.inputs}>
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  gap: 10,
                  flexWrap: "wrap",
                }}
              >
                <View
                  style={{
                    // display: "flex",
                    gap: 10,
                    // backgroundColor: "red",
                    width: "100%",
                  }}
                >
                  <Text style={{ fontWeight: "bold" }}>{product?.name}</Text>
                  <Badge
                    size="md"
                    variant="solid"
                    style={{
                      backgroundColor: product?.status ? "#2FC12B" : "#B91C1C",
                      borderRadius: 48,
                      alignSelf: "flex-start", // 🔥 Permet au badge de s'adapter à son contenu
                    }}
                  >
                    <BadgeText style={{ color: "white" }}>
                      {product?.status ? "En service" : "Hors service"}
                    </BadgeText>
                  </Badge>
                  <Text>{product?.description}</Text>

                  <Image
                    source={{ uri: product?.image as string }}
                    style={{
                      width: "100%",
                      height: 200,
                      marginTop: 10,
                      alignSelf: "center",
                    }}
                  />
                </View>
              </View>
            </View>

            <View style={{ gap: 10, width: "100%" }}>
              <View style={styles.header}>
                <Text style={{ fontWeight: "bold" }}>Documents techniques</Text>
                <TouchableOpacity
                  onPress={() =>
                    router.push({
                      pathname: "/product/doc/listDocs",
                      params: {
                        product_id: product_id,
                      },
                    })
                  }
                >
                  <Text style={{ color: "#F99527" }}>Voir plus</Text>
                </TouchableOpacity>
              </View>
              <View style={{ gap: 10 }}>
                {productDocs.length > 0 ? (
                  productDocs.map((item, index) => {
                    return (
                      <PaperInfo
                        title={item.name}
                        date={new Date(item.created_at)}
                        badgeShow={false}
                        onPress={() =>
                          router.push({
                            pathname: "/product/doc/detailsDoc",
                            params: { doc_id: item.id, product_id: product_id },
                          })
                        }
                      />
                    );
                  })
                ) : (
                  <Text>Vous n'avez pas encore crée des documents</Text>
                )}
              </View>

              <View style={{ ...styles.header, marginTop: 10 }}>
                <Text style={{ fontWeight: "bold" }}>
                  Action de maintenance
                </Text>
                <TouchableOpacity
                  onPress={() =>
                    router.push({
                      pathname: "/product/action/listActions",
                      params: {
                        product_id: product_id,
                      },
                    })
                  }
                >
                  <Text style={{ color: "#F99527" }}>Voir plus</Text>
                </TouchableOpacity>
              </View>
              <View style={{ gap: 10 }}>
                {productActions.length > 0 ? (
                  productActions.map((item, index) => {
                    return (
                      <PaperInfo
                        title={item.name}
                        text1={`Responsable: ${
                          item?.administrator?.first_name + " " + item?.administrator?.last_name
                        }`}
                        text2={`Processus: ${item?.process?.process_name}`}
                        date={new Date(item.date)}
                        badgeShow={false}
                        onPress={() =>
                          router.push({
                            pathname: "/screens/actions/ActionDetails",
                            params: { 
                              action: JSON.stringify({
                                id: item.id,
                                name: item.name,
                                type: item.type,
                                status: item.status,
                                date: item.date,
                                resultAction: item.resultAction,
                                administrator: {
                                  first_name: item.administrator?.first_name,
                                  last_name: item.administrator?.last_name,
                                  uid: item.administrator?.uid
                                },
                                process: {
                                  id: item.process?.id,
                                  process_name: item.process?.process_name
                                }
                              })
                            },
                          })
                        }
                      />
                    );
                  })
                ) : (
                  <Text>Vous n'avez pas encore crée des actions</Text>
                )}
              </View>

              <View style={{ ...styles.header, marginTop: 10 }}>
                <Text style={{ fontWeight: "bold" }}>
                  Les événements liée au matériel
                </Text>
                <TouchableOpacity
                // onPress={() =>
                //   router.push({
                //     pathname: "/product/action/listActions",
                //     params: {
                //       product_id: product_id,
                //     },
                //   })
                // }
                >
                  <Text style={{ color: "#F99527" }}>Voir plus</Text>
                </TouchableOpacity>
              </View>
              {/* <View style={{ gap: 10 }}>
                {productActions.length > 0 ? (
                  productActions.map((item, index) => {
                    return (
                      <PaperInfo
                        title={item.wording_action}
                        text1={`Responsable: ${
                          item?.users.first_name + " " + item?.users.last_name
                        }`}
                        text2={`Champs d'action: ${capitalizeFirstLetter(
                          item.action_champ || ""
                        )}`}
                        text3={`Type d'action: ${item.type}`}
                        date={new Date(item.created_at)}
                        badgeShow={false}
                        onPress={() =>
                          router.push({
                            pathname: "/product/action/detailsAction",
                            params: {
                              action_id: item.id,
                              product_id: product_id,
                            },
                          })
                        }
                      />
                    );
                  })
                ) : (
                  <Text>Vous n'avez pas encore crée des actions</Text>
                )}
              </View> */}
            </View>

            {/* Section Actions Préventives */}
            {/* <View style={{ gap: 10, width: "100%", marginTop: 20 }}>
              <View style={styles.header}>
                <Text style={{ fontWeight: "bold" }}>Actions préventives</Text>
                <TouchableOpacity
                  onPress={() =>
                    router.push({
                      pathname: "/screens/actions/CreateAction",
                      params: {
                        context: "product",
                        productId: product_id,
                        productName: product?.name,
                        actionType: "preventive"
                      },
                    })
                  }
                >
                  <Text style={{ color: "#F99527" }}>Créer une action</Text>
                </TouchableOpacity>
              </View>
              <View style={{ gap: 10 }}>
                {productActions.length > 0 ? (
                  productActions.map((action, index) => (
                    <PaperInfo
                      key={index}
                      title={action.name}
                      text1={`Responsable: ${action.administrator?.first_name} ${action.administrator?.last_name}`}
                      text2={`Processus: ${action.process?.process_name}`}
                      date={new Date(action.date)}
                      badgeShow={true}
                      level={action.status === 'achieved' ? 2 : 1}
                      onPress={() =>
                        router.push({
                          pathname: "/screens/actions/ActionDetails",
                          params: {
                            action: JSON.stringify({
                              id: action.id,
                              name: action.name,
                              type: action.type,
                              status: action.status,
                              date: action.date,
                              resultAction: action.resultAction,
                              administrator: {
                                first_name: action.administrator?.first_name,
                                last_name: action.administrator?.last_name,
                                uid: action.administrator?.uid
                              },
                              process: {
                                process_name: action.process?.process_name
                              },
                              companyId: action.company_id
                            })
                          }
                        })
                      }
                    />
                  ))
                ) : (
                  <Text>Vous n'avez pas encore créé d'actions préventives</Text>
                )}
              </View>
            </View> */}

            {/* 🔥 Bouton de suppression visible uniquement pour les Admins */}
            {/* {user?.status === "Admin" && (
              <View style={styles.deleteButtonContainer}>
                <TouchableOpacity
                  style={styles.deleteButton}
                  // onPress={handleDeleteProduct}
                >
                  <Text style={styles.deleteButtonText}>
                    Supprimer ce produit
                  </Text>
                </TouchableOpacity>
              </View>
            )} */}
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  containerContent: {
    flexDirection: "column",
    // alignItems: "center",
    // alignContent: "center",
    paddingHorizontal: 20,
    // backgroundColor: "#fff",
    gap: 20,
    // marginVertical: 20,
    // width: "80%",
    // alignSelf: "center",
    marginBottom: 300,
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    // backgroundColor: "yellow",
    width: "100%",
    gap: 40,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  buttonPassword: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
  },
  titleContainer: {
    flexDirection: "row",
    gap: 8,
  },
  imgIsLoadingPage: {
    height: 75,
    width: 130,
    alignSelf: "center",
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
  input: {
    backgroundColor: "white",
    borderColor: "none",
    height: 40,
    padding: 10,
    borderRadius: 4,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  deleteButtonContainer: {
    width: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 30,
  },
  deleteButton: {
    width: 200,
    backgroundColor: "#ff4f42",
    padding: 10,
    borderRadius: 8,
    alignItems: "center",
  },
  deleteButtonText: {
    color: "#FFFFFF",
    fontWeight: "normal",
  },
});
