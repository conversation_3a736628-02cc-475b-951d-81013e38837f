/*
app/screens/observations/CreateCloture.tsx

Informations pertinentes :

-

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User` et `Extern` n'ont pas accès à cette page.
*/

import React, { useContext, useEffect, useState } from "react";
import {
  Alert,
  StyleSheet,
  View,
  TextInput,
  Text,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import { supabase } from "../../../lib/supabase.native";
import { Controller, useForm } from "react-hook-form";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { useRoute, RouteProp } from "@react-navigation/native";
import moment from "moment";
import { useRouter } from "expo-router";
import { AppContext } from "@/state/AppContext";

// Données de navigation
type UpdateObservationRouteParams = {
  audit: {
    id: number;
    name: string;
    type: string;
    field: string;
    date: string;
    conclusion: string;
  };
};

export default function UpdateDocument() {
  const { user, companySelected } = useContext(AppContext);
  const route =
    useRoute<RouteProp<{ params: UpdateObservationRouteParams }, "params">>();
  const { audit } = route.params;
  const router = useRouter();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const {
    handleSubmit,
    formState: { errors },
    control,
    reset,
  } = useForm({
    defaultValues: {
      conclusion: audit.conclusion || "",
    },
  });

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  // Valider le formulaire
  const handleUpdateObservation = async (data: any) => {
    try {
      setIsSubmitting(true); // Activer l'indicateur de chargement

      // ✅ Étape 1 : Mise à jour de l'audit avec conclusion et clôture
      const updatedAudit = {
        conclusion: data.conclusion, // ✅ On met à jour la conclusion
        type: "validated",
      };

      const { error: auditError } = await supabase
        .from("audits")
        .update(updatedAudit)
        .eq("id", audit.id);

      if (auditError) {
        Alert.alert("Erreur", "L'audit n'a pas pu être mis à jour.");
        return;
      }

      // ✅ Étape 2 : Récupérer toutes les observations liées à cet audit
      const { data: auditObservations, error: observationsError } = await supabase
        .from("audit_observations")
        .select("observation_id")
        .eq("audit_id", audit.id);

      if (observationsError) {
        console.error("Erreur lors de la récupération des observations :", observationsError.message);
      } else if (auditObservations && auditObservations.length > 0) {
        // Extraire les IDs des observations
        const observationIds = auditObservations.map(item => item.observation_id);
        
        // Récupérer les détails des observations
        const { data: observations, error: detailsError } = await supabase
          .from("observations")
          .select("*")
          .in("id", observationIds);
        
        if (detailsError) {
          console.error("Erreur lors de la récupération des détails des observations :", detailsError.message);
        } else if (observations) {
          // Filtrer les observations de type 'sensitive_point' ou 'non_compliant'
          const relevantObservations = observations.filter(
            obs => obs.type === "sensitive_point" || obs.type === "non_compliant"
          );
          
          // Créer un event pour chaque observation pertinente
          for (const observation of relevantObservations) {
            const eventData = {
              wording: observation.name,
              type: observation.type,
              company_id: observation.company_id,
              created_at: new Date(),
              administrator: user?.uid,
              process: observation.process_id,
              uid_admin: user?.uid,
              file: null,
              date: new Date(),
              description: observation.name
            };
            
            const { error: eventError } = await supabase
              .from("events")
              .insert(eventData);
              
            if (eventError) {
              console.error(`Erreur lors de la création de l'event pour l'observation ${observation.id}:`, eventError.message);
            } else {
              console.log(`Event créé avec succès pour l'observation ${observation.id}`);
            }
          }
          
          console.log(`${relevantObservations.length} events créés pour l'audit ${audit.id}`);
        }
      }

      // ✅ Succès
      Alert.alert("Succès", "Audit mis à jour et clôturé avec succès.");

      // ✅ Réinitialisation et retour
      setTimeout(() => {
        reset();
        router.back();
      }, 1000);
    } catch (err) {
      Alert.alert("Erreur", `Une erreur est survenue : ${err}`);
    } finally {
      setIsSubmitting(false); // Désactiver l'indicateur de chargement
    }
  };

  return (
    <>
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.select({ ios: 0, android: 100 })}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <ScrollView style={styles.form}>
            {/* Informations de l'audit */}
            <View style={styles.auditCard}>
              <View style={styles.topElements}>
                <Text style={styles.cardName}>{audit?.name}</Text>
                <Text style={styles.date}>
                  {moment(audit.date).format("DD/MM/YYYY")}
                </Text>
              </View>
              <View style={styles.bottomElements}>
                <Text style={styles.cardText}>{audit?.field}</Text>
              </View>
            </View>

            {/* Champ Conclusion */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Conclusion</Text>
              <Controller
                control={control}
                name="conclusion"
                rules={{ required: "La conclusion est requise." }}
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    placeholderTextColor={"#6B7280"}
                    style={styles.input}
                    placeholder={"Conclusion"}
                    onChangeText={onChange}
                    value={value}
                    multiline
                  />
                )}
              />
              {errors.conclusion && (
                <Text style={styles.errorText}>
                  {errors.conclusion.message}
                </Text>
              )}
            </View>

            {/* Bouton de soumission */}
            <TouchableOpacity
              style={styles.confirmButton}
              onPress={() => {
                handleSubmit(handleUpdateObservation)();
              }}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Text style={styles.confirmButtonText}>Valider le rapport</Text>
              )}
            </TouchableOpacity>

            {/* Texte */}
            <Text>*La validation est irréversible</Text>
          </ScrollView>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    height: "100%",
    width: "100%",
    alignItems: "center",
    alignContent: "center",
    backgroundColor: "#FFFFFF",
  },
  headSection: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 30,
  },
  backButton: {
    width: 24,
    textAlign: "left",
  },
  titleWrapper: {
    display: "flex",
    flexDirection: "row",
    marginVertical: 15,
  },
  title: {
    color: "#262627",
    fontSize: 17,
    textAlign: "left",
  },
  form: {
    width: "100%",
    alignSelf: "center",
    paddingHorizontal: 30,
  },
  input: {
    borderWidth: 2,
    borderColor: "#ccc",
    borderRadius: 8,
    width: "auto",
    padding: 12,
    marginTop: 10,
    color: "#000000",
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginBottom: 5,
  },
  infoText: {
    fontSize: 14,
    marginVertical: 10,
    color: "#555",
  },
  inputContainer: {
    position: "relative",
    marginBottom: 20,
    width: "auto",
  },
  label: {
    fontSize: 14,
    color: "#262627",
    paddingHorizontal: 4,
    fontWeight: "bold",
  },
  confirmButton: {
    maxHeight: 50,
    borderRadius: 7,
    width: "100%",
    padding: 12,
    marginBottom: 16,
    backgroundColor: "#f99527",
    marginTop: 10,
  },
  confirmButtonText: {
    textAlign: "center",
    width: "100%",
    color: "#FFFFFF",
    fontSize: 14,
  },
  imagePicker: {
    width: "100%",
    padding: 10,
    backgroundColor: "#FFFFFF",
    borderColor: "#CCC",
    borderRadius: 8,
    borderWidth: 2,
    alignItems: "center",
    marginBottom: 10,
    marginTop: 10,
  },
  imagePickerText: {
    color: "#1F2937",
    fontWeight: "bold",
  },
  imagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  imageContainer: {
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  fullImagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginTop: 10,
  },
  uploadPlaceholder: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  uploadIcon: {
    borderWidth: 1,
    borderColor: "#f99527",
    borderRadius: 7,
    padding: 5,
  },
  uploadText: {
    textAlign: "center",
    fontWeight: "normal",
  },
  auditCard: {
    display: "flex",
    flexDirection: "column",
    // alignItems: "center",
    backgroundColor: "#CCC",
    borderRadius: 8,
    padding: 8,
    paddingVertical: 25,
    marginVertical: 10,
  },
  topElements: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  bottomElements: {
    display: "flex",
    flexDirection: "row",
  },
  cardName: {
    fontSize: 14,
    flexWrap: "wrap",
  },
  cardText: {
    fontSize: 12,
    flexWrap: "wrap",
    textAlign: "left",
    color: "#525252",
  },
  date: {
    fontSize: 14,
    color: "#525252",
    fontStyle: "normal",
    flexWrap: "wrap",
    fontWeight: "normal",
  },
});
