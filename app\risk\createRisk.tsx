/*
app/risk/createRisk.tsx

+++++++++++++++++ Restrictions +++++++++++++++++

- Les utilisateurs `User`, `Intern` et `Extern` n'ont pas accès à cette page.
*/

import ContainedButton from "@/components/ui/buttons/ContainedButton";
import InputCustomized from "@/components/ui/inputs/InputCustomized";
import { useRouter } from "expo-router";
import { StyleSheet, View, ScrollView, Alert, Platform } from "react-native";
import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { AppContext } from "@/state/AppContext";
import { supabase } from "@/lib/supabase";
import Toast from "react-native-toast-message";
import CustomizedSelect from "@/components/ui/customizedSelect/customizedSelect";
import TypeProcess from "@/types/typeProcess";
import TypeRisk from "@/types/typeRisk";
import { ParamListBase, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";

const possibilities = [
  { value: 0, label: "Quasi impossible", weight: 1 },
  { value: 1, label: "Possible", weight: 2 },
  { value: 2, label: "Fortes chances", weight: 3 },
  { value: 3, label: "Quasi certain !", weight: 4 },
];

const consequences = [
  { value: 0, label: "Impact critique", weight: 4 },
  { value: 1, label: "Impact important", weight: 3 },
  { value: 2, label: "Impact modéré", weight: 2 },
  { value: 3, label: "Impact mineur", weight: 1 },
];

const levels = [
  { value: 0, label: "Non maîtrisée" },
  { value: 1, label: "Partiel" },
  { value: 2, label: "Maîtrisée" },
];

export default function CreateRisks() {
  const [loading, setLoading] = useState(false);
  const { user, companySelected } = useContext(AppContext);
  const [process, setProcess] = useState<TypeProcess[]>([]);
  const router = useRouter();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  const inputs = [
    {
      label: "Processus concerné",
      name: "process_id",
      placeholder: "Processus concerné",
      type: "Select",
      data: process,
    },
    {
      label: "Événement redouté",
      name: "event",
      placeholder: "Événement redouté",
      type: "text",
      multiline: true, 

    },
    {
      label: "Possibilité que cela arrive ?",
      name: "possibility",
      placeholder: "Possibilité que cela arrive ?",
      type: "Select",
      data: possibilities,
    },
    {
      label: "Conséquence potentielle ?",
      name: "consequence",
      placeholder: "Conséquence potentielle ?",
      type: "Select",
      data: consequences,
    },
    {
      label: "Description du dispositif de prévention",
      name: "description",
      placeholder: "Description",
      type: "text",
      multiline: true, 

    },
    {
      label: "Niveau de maîtrise de risque ?",
      name: "level",
      placeholder: "Niveau de maîtrise de risque ?",
      type: "Select",
      data: levels,
    },
  ];

  useEffect(() => {
    const fetchUsersAdmin = async () => {
      const { data: dataGetted } = await supabase
        .from("process")
        .select()
        .eq("uid_admin", user?.uid)
        .eq("company_id", companySelected?.id);

      setProcess(
        (dataGetted?.map((item: any) => {
          return { label: item.process_name, value: item.id };
        }) as any) || null
      );
    };
    fetchUsersAdmin();
  }, []);

  // Vérification du statut de l'utilisateur
  useEffect(() => {
    if (!user) return;

    if (["Extern", "User", "Intern"].includes(user.status)) {
      Alert.alert(
        "Accès restreint",
        "Vous n'avez pas les droits pour accéder à cette page."
      );
      navigation.goBack();
    }
  }, [user]);

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors, touchedFields },
    control,
    reset,
    watch,
  } = useForm<TypeRisk>({
    defaultValues: {
      process_id: 0,
      uid_user: "",
      event: "",
      possibility: 0,
      consequence: 0,
      description: "",
      level: 0,
      process: {},
      created_at: "",
      company_id: 0,
      companies: { id: 0, uid_user: "", company_name: "", created_at: "" },
    },
  });

  const createRisk = async (data: TypeRisk) => {
    setLoading(true);

    const created_at = new Date();

    try {
      const { error } = await supabase.from("risks").insert({
        created_at: created_at,
        uid_user: user?.uid,
        event: data.event,
        possibility: data.possibility,
        description: data.description,
        process_id: data.process_id,
        level: data.level,
        company_id: companySelected?.id,
        consequence: data.consequence,
      });

      if (error) {
        console.log("error__", error);
        Toast.show({
          type: "error",
          text1: "Erreur lors d'enregistrement du risque",

          text1Style: { color: "#1C3144" },
        });
        setLoading(false);
        return;
      }

      Toast.show({
        type: "success",
        text1: "L'risk était bien crée",
        text1Style: { color: "#1C3144" },
      });

      router.back();

      reset();
      setLoading(false);
    } catch (err) {
      setLoading(false);
      Toast.show({
        type: "error",
        text1: "Une erreur est survenue",
        text1Style: { color: "#1C3144" },
      });
      // }
    }
  };

  return (
    <View style={styles.mainContainer}>
      <ScrollView style={styles.container}>
        <View style={styles.containerContent}>
          <View style={styles.registerInfo}>
            <View style={styles.inputs}>
              {inputs.map((input, index) => {
                if (input?.type === "Select") {
                  return (
                    <View key={index}>
                      <CustomizedSelect
                        name={input.name}
                        label={input.label}
                        register={register}
                        control={control}
                        errors={errors}
                        setError={setError}
                        data={input.data as any}
                      />
                    </View>
                  );
                } else {
                  return (
                    <View key={index}>
                      <InputCustomized
                        label={input.label}
                        placeholder={input.placeholder}
                        register={register}
                        name={input.name}
                        control={control}
                        errors={errors}
                        setError={setError}
                        multiline={input.multiline}
                      />
                    </View>
                  );
                }
              })}
            </View>
          </View>

          <View style={styles.buttons}>
            <ContainedButton
              label="Créer"
              backgroundColor="#F99527"
              onPress={handleSubmit(createRisk)}
              disabled={loading}
            />
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    width: "100%",
    ...(Platform.OS === 'web' ? {
      maxWidth: 800,
      marginHorizontal: 'auto',
    } : {}),
  },
  containerContent: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  registerInfo: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    width: "100%",
    gap: 20,
  },
  buttons: {
    flexDirection: "column",
    alignItems: "center",
    alignContent: "center",
    gap: 20,
    width: "100%",
    marginTop: 20,
  },
  inputs: {
    width: "100%",
    gap: 20,
  },
});
