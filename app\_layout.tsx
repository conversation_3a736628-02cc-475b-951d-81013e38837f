/*
app/_layout.tsx
*/

import {
  DarkTheme,
  DefaultTheme,
  ThemeProvider,
} from "@react-navigation/native";
import "@/global.css";
import { GluestackUIProvider } from "@/components/ui/gluestack-ui-provider";
import { useFonts } from "expo-font";
import { Stack, useRouter, useSegments, useLocalSearchParams } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { StatusBar } from "expo-status-bar";
import { useContext, useEffect } from "react";
import "react-native-reanimated";
import { useColorScheme } from "@/hooks/useColorScheme";
import { AppContext, AppProvider } from "@/state/AppContext";
import Header from "@/components/common/Header";
import Toast from "react-native-toast-message";
import { Platform, View } from "react-native";
import * as Linking from 'expo-linking';
import { supabase } from '@/lib/supabase';
import { ToastProvider } from '@/context/ToastContext';
export { ErrorBoundary } from "expo-router";

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export const unstable_settings = {
  initialRouteName: "index",
};

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
  });
  const { session } = useContext(AppContext);
  const router = useRouter();
  const segments = useSegments();

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  useEffect(() => {
    const handleDeepLink = (event: { url: string }) => {
      console.log("=== DEEP LINK TRACE ===");
      console.log("URL complète reçue:", event.url);
      const { path, queryParams } = Linking.parse(event.url);
      console.log("Path extrait:", path);
      console.log("Paramètres de requête:", queryParams);
      
      if (path === 'reset-password') {
        console.log("=== REDIRECTION RESET PASSWORD DETECTÉE ===");
        console.log("Source: Deep Link Handler");
        const token = event.url.split('token=')[1]?.split('&')[0];
        if (token) {
          console.log('Token de réinitialisation trouvé:', token);
          router.replace({
            pathname: '/reset-password',
            params: { token }
          });
        } else {
          console.log('Aucun token trouvé dans l\'URL');
        }
      }
    };

    const subscription = Linking.addEventListener('url', handleDeepLink);

    Linking.getInitialURL().then((url) => {
      if (url) {
        console.log("=== URL INITIALE DETECTÉE ===");
        console.log("URL initiale:", url);
        handleDeepLink({ url });
      }
    });

    return () => {
      subscription.remove();
    };
  }, []);

  if (!loaded) {
    return null;
  }

  return (
    <ToastProvider>
      <AppProvider>
        <GluestackUIProvider mode="light">
          <ThemeProvider
            value={colorScheme === "dark" ? DarkTheme : DefaultTheme}
          >
            <Stack>
              <Stack.Screen name="reset-password" options={{ headerShown: false }} />
              <Stack.Screen name="pre-login" options={{ headerShown: false }} />
              <Stack.Screen name="index" options={{ headerShown: false }} />
              <Stack.Screen name="login" options={{ headerShown: false }} />
              <Stack.Screen name="register" options={{ headerShown: false }} />
              <Stack.Screen
                name="profil"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="info/usersAdmin"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header
                        onPressFlesh={() =>
                          router.back()
                        }
                      />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="info/createActivity"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="event/createEvent"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="info/createUserByAdmin"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="info/process"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="info/createProcess"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="info/updateProcess"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="info/activities"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header
                        onPressFlesh={() =>
                          router.navigate("/menu/itemsMenuCockpit/info")
                        }
                      />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="supplier/supplier"
                options={{
                  headerShown: false,
                  header: () => (
                    <View>
                      <Header onPressFlesh={() => router.navigate("/")} />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="supplier/createSupplier"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="supplier/updateSupplier"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="supplier/detailsSupplier"
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="objectif/createObjectif"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="objectif/updateObjectif"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="objectif/detailsObjectif"
                options={{
                  headerShown: false,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="objectif/createValue"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="objectif/updateValue"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="info/context"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header
                        onPressFlesh={() =>
                          router.navigate("/menu/itemsMenuCockpit/info")
                        }
                      />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="info/createContext"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="info/updateContext"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="info/issue"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header
                        onPressFlesh={() =>
                          router.navigate("/menu/itemsMenuCockpit/info")
                        }
                      />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="info/createIssue"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="info/updateIssue"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="info/policy"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header
                        onPressFlesh={() =>
                          router.navigate("/menu/itemsMenuCockpit/info")
                        }
                      />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="info/createPolicy"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="info/updatePolicy"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="screens/notifications/CreateFlashQSE"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="screens/notifications/FlashQSEDetails"
                options={{
                  headerShown: false,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="screens/notifications/UpdateFlashQSE"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="screens/documents/CreateDocument"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="screens/documents/DocumentDetails"
                options={{
                  headerShown: false,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="screens/documents/UpdateDocument"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="screens/notifications/FlashQSEList"
                options={{
                  headerShown: false,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="screens/actions/RegularActionsList"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="screens/actions/InProgressActionsList"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="screens/actions/LateActionsList"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="screens/actions/CreateAction"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="risk/createRisk"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />

              <Stack.Screen
                name="risk/detailsRisk"
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="risk/updateRisk"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="pip/detailsPIP"
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="info/detailsProcess"
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="pip/updatePIP"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="pip/createPIP"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="event/eventSpecific"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="event/eventsCompany"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="screens/actions/UpdateAction"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="screens/actions/ActionDetails"
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="screens/actions/CreateActionDocument"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="screens/actions/UpdateActionDocument"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="screens/audits/CreateAudit"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="screens/audits/AuditDetails"
                options={{
                  headerShown: false,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="screens/audits/UpdateAudit"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />

              <Stack.Screen
                name="menu/itemsMenuDefault/doc"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header onPressFlesh={() => router.navigate("/")} />
                      <Toast />
                    </View>
                  ),
                }}
              />

              <Stack.Screen
                name="Documents"
                options={{
                  headerShown: false,
                  presentation: "card",
                  animation: "default",
                }}
              />

              <Stack.Screen
                name="menu/itemsMenuDefault/audit"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header onPressFlesh={() => router.navigate("/")} />
                      <Toast />
                    </View>
                  ),
                }}
              />

              <Stack.Screen
                name="Audit"
                options={{
                  headerShown: false,
                  presentation: "card",
                  animation: "default",
                }}
              />

              <Stack.Screen
                name="menu/itemsMenuDefault/event"
                options={{
                  headerShown: false,
                  presentation: "card",
                  animation: "default",
                }}
              />
              <Stack.Screen
                name="Evenements"
                options={{
                  headerShown: false,
                  presentation: "card",
                  animation: "default",
                }}
              />
              <Stack.Screen
                name="event/detailsEvent"
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="event/createActionEvent"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="event/updateEvent"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="menu/itemsMenuCockpit/objectif"
                options={{
                  headerShown: false,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />

              <Stack.Screen
                name="menu/itemsMenuCockpit/pip"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header onPressFlesh={() => router.navigate("/")} />
                      <Toast />
                    </View>
                  ),
                }}
              />

              <Stack.Screen
                name="PIP"
                options={{
                  headerShown: false,
                  presentation: "card",
                  animation: "default",
                }}
              />

              <Stack.Screen
                name="menu/itemsMenuCockpit/info"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header onPressFlesh={() => router.navigate("/")} />
                      <Toast />
                    </View>
                  ),
                }}
              />

              <Stack.Screen
                name="Informations"
                options={{
                  headerShown: false,
                  presentation: "card",
                  animation: "default",
                }}
              />

              <Stack.Screen
                name="screens/observations/CreateObservation"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="screens/observations/UpdateObservation"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="screens/observations/DetailObservation"
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="screens/observations/CreateCloture"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="info/detailsActivity"
                options={{
                  headerShown: false,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="info/updateActivity"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
              <Stack.Screen name="+not-found" />

              <Stack.Screen
                name="equipment/createEquipment"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="equipment/updateEquipment"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="equipment/detailsEquipment"
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="equipment/assignEquipment"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="menu/itemsMenuDefault/equipment"
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="menu/itemsMenuDefault/maintenance"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header title="Matériels" />
                      <Toast />
                    </View>
                  ),
                }}
              />

              {/* Content Routes */}
              <Stack.Screen
                name="content/elearning"
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="content/createELearning"
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="content/updateELearning"
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="content/listELearning"
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="content/updateELearningPdf"
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="content/oneELearningPdf"
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="content/oneELearning"
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="content/listELearningPdf"
                options={{
                  headerShown: false,
                }}
              />

              {/* Product Routes */}
              <Stack.Screen
                name="product/createProduct"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header title="Créer un matériel" />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="product/detailsProduct"
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="product/updateProduct"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header title="Modifier le matériel" />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="product/updateValue"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="product/action/createAction"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="product/action/detailsAction"
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="product/action/listActions"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="product/doc/createDoc"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header title="Créer un document" />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="product/doc/detailsDoc"
                options={{
                  headerShown: true,
                  header: () => {
                    const params = useLocalSearchParams();
                    return (
                      <View>
                        <Header 
                          title="Détails du document"
                          onPressFlesh={() => router.back()}
                          onPressIcon={() => router.push({
                            pathname: "/product/doc/updateDoc",
                            params: {
                              doc_id: params.doc_id as string,
                              name: params.name as string,
                              image: params.image as string,
                              product_id: params.product_id as string
                            }
                          })}
                        />
                        <Toast />
                      </View>
                    );
                  },
                }}
              />
              <Stack.Screen
                name="product/doc/listDocs"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header title="Documents techniques" />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="product/doc/updateDoc"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header />
                      <Toast />
                    </View>
                  ),
                }}
              />

              <Stack.Screen
                name="info/detailsUser"
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="info/updateUser"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header title="Modifier l'utilisateur" />
                      <Toast />
                    </View>
                  ),
                }}
              />

              <Stack.Screen
                name="menu/itemsMenuDefault/action"
                options={{
                  headerShown: true,
                  header: () => (
                    <View>
                      <Header onPressFlesh={() => router.navigate("/")} />
                      <Toast />
                    </View>
                  ),
                }}
              />
              <Stack.Screen
                name="Action"
                options={{
                  headerShown: false,
                  presentation: "card",
                  animation: "default",
                }}
              />

              <Stack.Screen
                name="Equipements"
                options={{
                  headerShown: false,
                  presentation: "card",
                  animation: "default",
                }}
              />

              <Stack.Screen
                name="Objectifs"
                options={{
                  headerShown: false,
                  presentation: "card",
                  animation: "default",
                }}
              />

              <Stack.Screen
                name="Risques"
                options={{
                  headerShown: false,
                  presentation: "card",
                  animation: "default",
                }}
              />

              <Stack.Screen
                name="menu/home"
                options={{
                  headerShown: false,
                  presentation: "card",
                  animation: "default",
                }}
              />

              <Stack.Screen
                name="Accueil"
                options={{
                  headerShown: false,
                  presentation: "card",
                  animation: "default",
                }}
              />

            </Stack>
            <StatusBar style="auto" />
          </ThemeProvider>
        </GluestackUIProvider>
      </AppProvider>
    </ToastProvider>
  );
}
