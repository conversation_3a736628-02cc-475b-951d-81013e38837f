export const getEventTypeLabel = (typeValue: string): string => {
  const typesEvent = [
    { label: "Non conformité - Fournisseur", value: "supplier" },
    { label: "Audit - Non-conformité (code NC)", value: "non_compliant" },
    { label: "Audit - Point sensible (code PS)", value: "sensitive_point" },
    { label: "Situation dangereuse (code SD)", value: "dangerous_situation" },
    { label: "Accident du travail (code AT)", value: "work_accident" },
    { label: "Presqu'accident (code PAT)", value: "near_miss" },
    { label: "Malfaçon produit ou service (code NQ)", value: "product_defect" },
    { label: "Danger environnemental (code DE)", value: "environmental_danger" },
    { label: "Accident environnemental (code AE)", value: "environmental_accident" },
    { label: "Réclamation client (code REC)", value: "customer_complaint" },
    { label: "Risque (code RI)", value: "risk" },
    { label: "Opportunité (code OPP)", value: "opportunity" },
    { label: "Objectif (code OBJ)", value: "objective" },
    { label: "Partie intéressée pertinente (code PIP)", value: "interested_party" },
    { value: "audit_report", label: "Constat d'audit (code CO)", ActionImmediate: "OUI" },
    { label: "Matériel - Défaut (code MAT)", value: "material_defect" },

  ];

  const type = typesEvent.find(t => t.value === typeValue);
  return type ? type.label : typeValue;
}; 